import { Injectable } from '@angular/core';
import { Ajax } from '@shark/shark-angularX';
import { AjaxUrl } from '../../config';

export interface IUser {
    fullname: string;
    email: string;
}

@Injectable()
export class UserService {
    constructor(
        private ajax: Ajax
    ) {
        console.log('UserService init');
        this.getUser();
    }

    async getUser() {
        const result = await this.ajax.get(AjaxUrl.user.getUserInfo);
        const user: IUser = result.data;
        return user;
    }
    // 获取用户的信息
    async getUserInfo() {
        const res = await this.ajax.get(AjaxUrl.user.getUserFullInfo, {});

        if (res.data && !res.data.userName) {
            const user = await this.getUser();
            res.data.userName = user.fullname;
        }
        return res;
    }
    async getUserInfoByUid(uid) {
        return await this.ajax.get(AjaxUrl.user.getUserFullInfo, {
            email: uid
        });
    }

    /**
     * 检查是否在组织架构中
     */
    async checkUserIsInOrg(): Promise<boolean> {
        const res = await this.ajax.get(AjaxUrl.org.checkInOrg);
        return res.data;
    }
}
