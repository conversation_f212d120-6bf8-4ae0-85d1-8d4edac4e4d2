import { FlowData, FlowSubmitData } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { contextPath } from '../../../../conf';
import { getUserExtInfo } from '../../../service/permcenter/unitOrg';
import WorkflowQuery from '../../workflow-query';
import { topologyName } from '../perm-config';
import Workflow from '../perm-workflow';

type SubmitResult = AjaxResult<string[]>;
type UpsertResult = AjaxResult<string>;
const system = { email: 'system', fullname: '系统自动审批' };

/**
 * 工单创建与提交
 */
@RestController
@RequestMapping(`${contextPath}/xhr/workflow/${topologyName}`)
export class PermWorkflowController {

    constructor(
        private workflow: Workflow,
        private query: WorkflowQuery
    ) {
    }

    /**
     * 工单创建并提交，触发流程流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/submit.json')
    async submit(ctx: RequestContext<FlowSubmitData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        const userInfo = ctx.openIDInfo;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;

        try {
            // 创建时候需要获取用户额外信息填充工单流
            if (flowMetaData.currentNodeId === '30010501' || !flowMetaData.currentNodeId) {
                const userExtInfo = await getUserExtInfo({
                    orgPosId: flowNodeMessage.orgPosId,
                    level: flowNodeMessage.level,
                    type: -1,
                    queryType: 0,
                    keyword: flowNodeMessage.applerUid
                });
                flowNodeMessage.duty = userExtInfo.data[0].duty;
                flowNodeMessage.employeeNum = userExtInfo.data[0].employeeNum || '-';
            }
            const nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            console.error('工单提交失败:', e);
            ctx.body = AjaxResult.internal('工单提交失败');
        }
    }

    /**
     * 工单创建与保存草稿，不提交，不触发流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/upsert.json')
    async upsert(ctx: RequestContext<FlowData, UpsertResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const flowId = await this.workflow.upsert(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(flowId);
        } catch (e) {
            console.error('工单保存失败:', e);
            ctx.body = AjaxResult.internal('工单保存失败');
        }
    }

    /**
     * 工单更新
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/update.json')
    async update(ctx: RequestContext<FlowData, UpsertResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const flowId = await this.workflow.update(flowMetaData, flowNodeMessage, system);

            ctx.body = AjaxResult.success(flowId);
        } catch (e) {
            console.error('工单保存失败:', e);
            ctx.body = AjaxResult.internal('工单保存失败');
        }
    }
}
