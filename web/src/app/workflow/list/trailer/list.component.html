<div class="section">
    <div class="section-header section-header-bordered">
        <span class="section-header-title">跟踪工单</span>
    </div>
    <div class="section-block margin-b-0x">
        <form class="form form-inline">
            <div class="form-item-group">
                <div class="form-item col-6">
                    <label class="form-item-label col-6">工单类型：</label>
                    <div class="form-item-control col-18">
                        <shark-select [(ngModel)]="searchData.topologyId" [data]="flowTypeOptions" [size]="'full'" name="topologyId"></shark-select>
                    </div>
                </div>
                <div class="form-item col-6">
                    <label class="form-item-label col-8">工单ID：</label>
                    <div class="form-item-control col-16">
                        <shark-input [(ngModel)]="searchData.flowId" [size]="'full'"  name="flowId"></shark-input>
                    </div>
                </div>
                <div class="form-item col-6">
                    <label class="form-item-label col-6">创建人邮箱：</label>
                    <div class="form-item-control col-18">
                        <single-user-selector [(model)]="searchData.createUser" [size]="'full'" name="createUserEmail"></single-user-selector>
                        <!-- <shark-input [(ngModel)]="searchData.createUser" [size]="'full'" name="createUserEmail"></shark-input> -->
                    </div>
                </div>
                <div class="form-item col-6">
                    <div class="form-item-control col-16">
                        <button class="btn btn-primary" (click)="doSearch()">查询</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="section-block">
        <div class="table-wrap">
            <table class="table table-full">
                <thead>
                    <tr>
                        <th>工单ID</th>
                        <th>工单类型</th>
                        <th>创建人</th>
                        <th>工单关键信息</th>
                        <th>当前节点/审批人</th>
                        <th>更新时间</th>
                        <th>工单状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody *ngIf="itemList.length === 0">
                    <tr>
                        <td class="table-no-data" colspan="7">
                            <span class="no-data-tip">暂无数据</span>
                        </td>
                    </tr>
                </tbody>
                <tbody *ngIf="itemList.length > 0">
                    <tr *ngFor="let item of itemList;let index = index;">
                        <!-- <td class="text-nowrap table-operation" name="工单标题">
                            <button (click)="detail(item)" class="btn btn-link user-select-auto" type="button">
                                {{item.flowMetaData.topologyName === 'nav_personnelchange_perm_child' && item.flowNodeMessage.applicantProductList ? item.flowNodeMessage.applicantProductList[0]?.productName + '-' : ''}}{{item.flowMetaData.topologyName |
                                toponamepipe}}-{{item.flowNodeMessage.applerName || item.flowNodeMessage.createUserName}}-{{item.flowNodeMessage.createTime |
                                dateFormat: 'yyyy-MM-dd'}}
                            </button>
                            <div class="text-xs">工单ID：{{item.flowMetaData.globalId}}</div>
                        </td> -->
                        <td class="text-nowrap table-operation" name="工单ID">
                            <ng-template #notDoc>
                                <button (click)="detail(item)" class="btn btn-link user-select-auto" type="button">
                                    {{item.flowMetaData.globalId}}
                                </button>
                            </ng-template>
                            <div class="text-xs" *ngIf="item.flowMetaData.topologyId === 'written_doc_project' || item.flowMetaData.topologyId === 'written_doc_delete_project';else notDoc">
                                <div>
                                    <button (click)="detail(item)" class="btn btn-link user-select-auto" type="button">{{item.flowMetaData.flowId}}</button>
                                </div>
                                <div>文件名称：{{item.flowNodeMessage?.content?.name}}</div>
                            </div>
                        </td>
                        <td class="text-nowrap" name="工单类型">
                            {{item.flowMetaData.topologyName | toponamepipe}}
                        </td>
                        <td class="text-nowrap" name="创建人">
                            <div *ngIf="item.flowNodeMessage.createUser">
                                <popo [email]="item.flowNodeMessage.createUser"></popo>
                                <span *ngIf="item.flowNodeMessage.businessObject?.applyType === 0 && item.flowMetaData.topologyName === 'ius_entry_apply_topology'">
                                    (系统自动发起)
                                </span>
                                <span *ngIf="item.flowMetaData.topologyName === 'ius_org_identity_confirm_topology'">
                                    (系统自动发起)
                                </span>
                            </div>
                        </td>
                        <td class="text-nowrap" name="工单关键信息">
                            <span *ngIf ="item.flowMetaData.topologyName === 'ius_quit_apply_topology'">
                                离职人员：{{item.flowNodeMessage.createUserName}}
                            </span>
                            <span *ngIf ="item.flowMetaData.topologyName === 'nav_personnelchange_departure'">
                                离职人员：{{item.flowNodeMessage.applerName}}
                            </span>
                            <div class="text-center" *ngIf ="item.flowMetaData.topologyName === 'ius_ext_system_auth_apply' || item.flowMetaData.topologyName === 'ius_ext_system_auth_clear'">
                                {{item.flowNodeMessage.systemName}}：{{item.flowNodeMessage.accountName}}
                                <div *ngIf="item.flowNodeMessage.forOtherMobileCheckRemark" class="text-error">{{item.flowNodeMessage.forOtherMobileCheckRemark}}</div>
                                <div *ngIf="item.flowNodeMessage.changeUserInfo">
                                    {{item.flowNodeMessage.changeUserInfo.name}} ({{item.flowNodeMessage.changeUserInfo.uid}})：{{item.flowNodeMessage.changeUserInfo.changeStatus}}
                                </div>
                            </div>
                            <div class="text-center" *ngIf="(item.flowMetaData.topologyName === 'ius_org_identity_permit_topology' || item.flowMetaData.topologyName === 'ius_org_identity_permit_child_topology' || item.flowMetaData.topologyName === 'ius_special_rights' || item.flowMetaData.topologyName === 'ius_personal_special_rights' || item.flowMetaData.topologyName === 'ius_special_rights_child') && item?.flowNodeMessage?.businessObject?.multiApplySystem?.length">
                                <div class="text-center padding-t-1x" *ngFor="let flowItem of item.flowNodeMessage.businessObject.multiApplySystem; let i = index;">
                                    <span *ngIf="!item.show && i < 3">{{flowItem.applySystemName}}</span>
                                    <span *ngIf="item.show">{{flowItem.applySystemName}}</span>
                                </div>
                                <button class="btn btn-link" type="button" (click)="targetShow(item)" *ngIf="item.flowNodeMessage.businessObject.multiApplySystem.length > 3 ">{{item?.show ? '收起' : '展开'}}</button>
                            </div>
                        </td>
                        <td *ngIf="!item.isNewFlow" class="text-nowrap" name="当前节点">
                            <span *ngIf="item.flowNodeMessage?.businessStatus === 0">{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                            <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                            <div>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </div>
                        </td>
                        <td *ngIf="item.isNewFlow" class="text-nowrap" name="当前节点">
                            <span style="padding: 5px;">{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <div>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </div>
                        </td>
                        <!-- <td class="text-nowrap" name="审批人">
                            <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                            <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                        </td> -->
                        <td class="text-nowrap" name="更新时间">
                            {{item.flowNodeMessage.updateTime | dateFormat: 'yyyy-MM-dd'}}
                            <br> {{item.flowNodeMessage.updateTime | dateFormat: 'HH:mm:ss'}}
                        </td>
                        <!-- <td class="text-nowrap" class="text-nowrap" name="创建时间">
                            {{item.flowNodeMessage.createTime | dateFormat: 'yyyy-MM-dd'}}
                            <br> {{item.flowNodeMessage.createTime | dateFormat: 'HH:mm:ss'}}
                        </td> -->
                        <td class="text-nowrap" name="工单状态">
                            <span *ngIf="!item.isNewFlow">{{item.flowNodeMessage.businessStatus | flowbusinessstate}}</span>
                            <span *ngIf="item.isNewFlow">{{item.flowNodeMessage.businessStatus | flowspecialbusinessstate}}</span>
                        </td>
                        <td class="table-operation">
                            <button class="btn btn-link" type="button" (click)="detail(item)">查看详情</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="section-block text-right">
        <shark-pagination [page]="pagination.page" [totalPage]="pagination.totalPage" [pageSize]="pagination.size"
            (onSizeChanged)="onSizeChanged($event);" (onPageChanged)="onPageChanged($event);"></shark-pagination>
    </div>
</div>