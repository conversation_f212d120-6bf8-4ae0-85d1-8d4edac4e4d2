<div class="m-category clearfix" [ngClass]="{'fav-tag': isFav}">
    <h5>
        {{tagName}}
    </h5>
    <div class="float-left app-content" [ngClass]="{'app-content-4': cols == 4}" *ngFor="let app of appList; trackBy: idFn"
        [attr.data-show-index]="app.showIndex">
        <div class="u-app-block" [ngClass]="app.tag.background">
            <a href="{{app.link}}" target="_blank">
                <i class="app-icon" [ngClass]="app.icon || app.tag.icon"></i>
                <span>{{app.name}}</span>
            </a>
            <div class="collect-box">
                <div class="collect-shadow"></div>
                <i class="u-top" [ngClass]="{'icon-collect': !app.isFav, 'icon-collected': app.isFav}" (click)="onFavClick(app)"></i>
            </div>
            <div class="lock" *ngIf="app.perm">
                <div class="lock-shadow"></div>
                <i class="lock-icon"></i>
            </div>
        </div>
    </div>
</div>