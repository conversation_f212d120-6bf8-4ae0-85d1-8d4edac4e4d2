<!-- 文档管理列表 -->
<div class="section">
    <div class="section-header section-header-bordered">
        <span class="section-header-title">文档管理</span>
        <div class="float-right">
            <button class="btn btn-primary" (click)="expExcel();">导出列表</button>
        </div>
    </div>
    <div class="section-block margin-b-0x">
        <form class="form form-inline ">
            <div class="form-item-group">
                <div class="form-item col-8">
                    <label class="form-item-label col-8">文档名称：</label>
                    <div class="form-item-control col-16">
                        <shark-input [placeholder]="'输入文字'" [size]="'full'" name="name"
                        [(ngModel)]="name" [ngModelOptions]="{standalone: true}"></shark-input>
                    </div>
                </div>

                <div class="form-item col-8">
                    <label class="form-item-label col-8">文档密级：</label>
                    <div class="form-item-control col-16">
                        <shark-select [size]="'full'" [(ngModel)]="confidentiality.value"
                        [data]="confidentiality.data" [ngModelOptions]="{standalone: true}"></shark-select>
                    </div>
                </div>

                <div class="form-item col-8">
                    <label class="form-item-label col-8">创建人：</label>
                    <div class="form-item-control col-16">
                        <!-- <shark-input [placeholder]="'输入文字'" [size]="'full'" name="createUser"
                            [(ngModel)]="createUser" [ngModelOptions]="{standalone: true}"></shark-input> -->
                        <single-user-selector [(model)]="createUser" [size]="'full'"
                        name="createUserEmail"></single-user-selector>
                    </div>
                </div>
            </div>

            <div class="form-item-group">
                <div class="form-item col-8">
                    <label class="form-item-label col-8">工单状态：</label>
                    <div class="form-item-control col-16">
                        <shark-select [size]="'full'" [(ngModel)]="businessStatus.value"
                        [data]="businessStatus.data" [ngModelOptions]="{standalone: true}"
                        (ngModelChange)="isFinish($event)"></shark-select>
                    </div>
                </div>
                
                <div class="form-item col-8">
                    <label class="form-item-label col-8">当前节点：</label>
                    <div class="form-item-control col-16">
                        <shark-select [size]="'full'" [(ngModel)]="currentNode.value"
                        [data]="currentNode.data" [disabled]="isDisabled" [ngModelOptions]="{standalone: true}">
                    </shark-select>
                    </div>
                </div>

                <div class="form-item col-8">
                    <label class="form-item-label col-8">拟制部门：</label>
                    <div class="form-item-control col-16">
                        <button class="btn btn-link" (click)="selectOrg()">选择部门</button><br />
                        <div *ngFor="let item of relatedResponsibilities;let i = index" class="disp-inline-block margin-t-1x">
                            <div class="tag tag-withclose ">
                                <p class="tag-text">{{item.orgPosName}}</p>
                                <i class="icon-close" (click)="deleteRelatedResponsibilities(item,i)"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-item-group">
                <div class="form-item col-8">
                    <label class="form-item-label col-8">文档类型：</label>
                    <div class="form-item-control col-16">
                        <shark-cascad-select [(ngModel)]="docType.value" [data]="docType.data"
                        [size]="'full'" [displayWay]="'path'" [separator]="'—'" [ngModelOptions]="{standalone: true}">
                    </shark-cascad-select>
                    </div>
                </div>

                <div class="form-item col-16">
                    <label class="form-item-label col-4">创建时间：</label>
                    <div class="form-item-control col-20">
                        <shark-daterangepicker [size]="'lg'" [(ngModel)]="applyDateRange"
                        [format]="'yyyy-MM-dd HH:mm：ss'" [ngModelOptions]="{standalone: true}"></shark-daterangepicker>
                    </div>
                </div>
            </div>

            <div class="form-item-group">
                <div class="form-item col-8">
                    <div class="form-item-control col-offset-8">
                        <button class="btn btn-primary" (click)="search();">查询</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="section-block">
        <div class="table-wrap">
            <table class="table table-full">
                <thead>
                    <tr>
                        <th>工单ID</th>
                        <th>文档编号</th>
                        <th>文档名称</th>
                        <th>文档密级</th>
                        <th>文档类型</th>
                        <th>拟制部门</th>
                        <th>创建人</th>
                        <th>审批人</th>
                        <th>工单状态</th>
                        <th>当前节点</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="!itemList||itemList.length === 0">
                        <td class="table-no-data" colspan="12">
                            <span class="no-data-tip">暂无数据</span>
                        </td>
                    </tr>

                    <tr *ngFor="let item of itemList; ">
                        <td class="text-nowrap" name="工单ID">
                            <span
                                *ngIf="item.flowNodeMessage?.content !== 'undefined' &&
                                item.flowNodeMessage?.content?.number !== 'undefined'">
                                {{ item.flowMetaData.flowId }}
                            </span>
                        </td>
                        <td class="text-nowrap" name="文档编号">
                            <span
                                *ngIf="item.flowNodeMessage?.content !== 'undefined' && item.flowNodeMessage?.content?.number !== 'undefined'">
                                {{ item.flowNodeMessage?.content?.number }}
                            </span>
                        </td>
                        <td class="text-nowrap" name="文档名称">
                            <span
                                *ngIf="item.flowNodeMessage?.content !== 'undefined' && item.flowNodeMessage?.content?.number !== 'undefined'">
                                {{ item.flowNodeMessage?.content?.name }}
                            </span>
                        </td>
                        <td class="text-nowrap" name="文件类型">
                            <!-- {{ item.flowNodeMessage.content.confidentiality | confidentiality}} -->
                            <span [ngSwitch]="item.flowNodeMessage?.content?.confidentiality">
                                <span *ngSwitchCase="1">
                                    绝密级
                                </span>
                                <span *ngSwitchCase="2">
                                    机密级
                                </span>
                                <span *ngSwitchCase="3">
                                    秘密级
                                </span>
                                <span *ngSwitchCase="4">
                                    普一级
                                </span>
                                <span *ngSwitchCase="5">
                                    普二级
                                </span>
                                <span *ngSwitchCase="6">
                                    普三级
                                </span>
                                <span *ngSwitchDefault>

                                </span>
                            </span>
                        </td>
                        <td class="text-nowrap" name="文档类型">
                             <span *ngIf="item.flowNodeMessage?.content?.categoryList
                                && item.flowNodeMessage?.content?.categoryList.length > 0">
                                 {{ item.flowNodeMessage?.content?.categoryList[0]}}
                                 <span
                                     *ngIf="item.flowNodeMessage?.content?.categoryList[1]">
                                     — {{ item.flowNodeMessage?.content?.categoryList[1]}}</span>
                             </span>
                        </td>
                        <td name="拟制部门">
                            <span
                                *ngIf="item.flowNodeMessage?.content !== 'undefined' && item.flowNodeMessage?.content?.number !== 'undefined'">
                                {{ item.flowNodeMessage?.content?.mainDepartment }}
                            </span>
                        </td>
                        <td class="text-nowrap" name="创建人">
                            <span>
                                 {{item.flowNodeMessage.createUserName}}
                                 <p>{{item.flowNodeMessage.createUser}}</p>
                            </span>

                        </td>

                        <td class="text-nowrap" name="审批人">
                            <popo *ngFor="let uid of item.flowNodeMessage.acceptorList;" [email]="uid"></popo>
                            <span *ngIf="item.flowNodeMessage.acceptorList.length === 0">/</span>
                        <td class="text-nowrap" name="工单状态">
                            <!-- <span
                                *ngIf="item.flowNodeMessage.content !== 'undefined' && item.flowNodeMessage.content?.number !== 'undefined'">
                                {{ item.flowNodeMessage.businessStatus | businessstatuspipe}}
                            </span> -->
                            <!-- <span>
                                {{ item.flowNodeMessage.businessStatus | businessstatuspipe}}
                            </span> -->
                            <span>{{item.flowMetaData.status | flowstate}}</span>
                        </td>
                        <td class="text-nowrap" name="当前节点">
                            <span *ngIf="item.flowMetaData.currentNodeId !== '9999'">
                                {{item.flowMetaData.currentNodeId | nodenamepipe }}
                            </span>
                            <!-- 如果完结 则工单节点 都显示为 / -->
                            <span *ngIf="item.flowMetaData.currentNodeId === '9999'">/</span>
                        </td>
                        <td class="text-nowrap" name="创建时间">
                            <span
                                *ngIf="item.flowNodeMessage?.content !== 'undefined' && item.flowNodeMessage?.content?.number !== 'undefined'">
                                {{ item.flowMetaData.createTime | dateFormat: 'yyyy-MM-dd'}}
                            </span>
                        </td>
                        <td class="table-operation" name="操作">
                            <button type="button" class="btn btn-link" (click)="checkDetail(item)">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="section-block text-right">
        <shark-pagination [page]="pagination.page" [totalPage]="pagination.totalPage" [pageSize]="pagination.size"
            (onPageChanged)="onPageChanged($event);" (onSizeChanged)="onSizeChanged($event);"></shark-pagination>
    </div>
</div>