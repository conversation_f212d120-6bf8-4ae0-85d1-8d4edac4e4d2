/**
 * @description
 *  module 目录下放置业务相关的代码。按业务模块组织代码格式
 *  例如：有一个用户管理模块,对应的目录结构为
 *  user
 *  |__user.api.ts  //放置路由映射处理，做为controller
 *  |__user.service.ts  //业务处理逻辑代码
 */
import { BaseModule, TgModule } from '@tiger/boot';
import { ApplicationModule } from './application/application.module';
import { CmdbRolesModule } from './cmdb-roles/cmdb-roles.module';
import { ErrorModule } from './error/error.module';
import { FmsController } from './fms/fms.controller';
import { SharedModule } from './shared/shared.module';
import { StaticModule } from './static/static.module';
import { UnitOrgModule } from './unitorg/unitorg.module';
import { UserModule } from './user/user.module';
import { WorkflowModule } from './workflow/workflow.module';
import { WrittendocWorkflowModule } from './workflow/writtendoc/writtendoc-workflow.module';
import { WrittendocDeleteWorkflowModule } from './workflow/writtendocdelete/writtendoc-delete-workflow.module';

@TgModule({
    imports: [ApplicationModule, ErrorModule, StaticModule, SharedModule, UnitOrgModule, UserModule, WorkflowModule,
        WrittendocWorkflowModule, WrittendocDeleteWorkflowModule, CmdbRolesModule],
    controllers: [FmsController],
    middlewares: []
})
export class AppModule extends BaseModule {
}
