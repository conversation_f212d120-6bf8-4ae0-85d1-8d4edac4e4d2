<div class="section" [ngClass]="{'section-fold':!switch}">
    <div class="section-header section-header-bordered" style="cursor: pointer;" (click)="switch = !switch">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block">
        <form class="form" novalidate="" shark-validform #form="shark-validform" autocomplete="off" *ngIf="switch">
            <div class="form-item">
                <label class="form-item-label col-4">申请人：</label>
                <div class="form-item-text col-10 input-affix">
                    {{flowData.flowNodeMessage.applerUid}}({{flowData.flowNodeMessage.applerName}})
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">申请转出组织架构：</label>
                <div class="form-item-control col-12 input-affix">
                    <shark-select [multiple]=true [(model)]="flowData.flowNodeMessage.transferOrgPos"
                        [data]="originOrgPosList" [size]="'full'" shark-valid [validRule]="'required'"></shark-select>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">申请加入组织架构：</label>
                <div class="form-item-control col-10">
                    <shark-stree [(ngModel)]="selectedOrgId" [data]="orgPosDataOption"
                        (onSelected)="onOrgPosSelected($event);" [displayKey]="'orgPosName'" [actualKey]="'orgPosId'"
                        [parentActualKey]="'parentOrgPosId'" name="orgPosId"></shark-stree>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">角色类型：</label>
                <div class="form-item-control col-5">
                    <shark-select [(ngModel)]="flowData.flowNodeMessage.applerRoleType" [data]="applerRoleTypeOption"
                        [size]="'lg'" shark-valid [validRule]="'required'" name="applerRoleType"></shark-select>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">员工类型：</label>
                <div class="form-item-control col-5">
                    <shark-select [(ngModel)]="flowData.flowNodeMessage.applerType" [data]="applerTypesOption"
                        [size]="'lg'" shark-valid [validRule]="'required'" name="applerType"></shark-select>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">员工职责：</label>
                <div class="form-item-control col-12">
                    <input type="text" placeholder="员工职责" [(ngModel)]="flowData.flowNodeMessage.dutyDesc"
                        class="input input-w-lg" shark-valid [validRule]="'required'" name="dutyDesc" />
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">转岗说明：</label>
                <div class="form-item-control col-12">
                    <shark-textarea [(ngModel)]="flowData.flowNodeMessage.remark" [size]="'full'" [placeholder]="'转岗说明'"
                        [maxLength]=200 name="remark"></shark-textarea>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">跟踪者：</label>
                <div class="form-item-control col-16">
                    <user-select-result-list [(userList)]="userlist" [placeholder]="'请选择对应的用户'">
                    </user-select-result-list>
                    <button class="btn btn-primary btn-sm" type="button" (click)="selectUsers()">添加跟踪者</button>
                </div>
            </div>
            <div class="form-item" *ngIf="cmdbRoleText!=''">
                <label class="form-item-label col-4"></label>
                <div class="form-item-text col-16 text-error vertical-baseline">
                    <i class="icon-exclamationcircle text-error"></i>&nbsp;{{cmdbRoleText}}，请立即前往 cmdb 进行交接！&nbsp;=> &nbsp;&nbsp;<a href="http://yx.mail.netease.com/cmdb#/service/list">CMDB <i class="icon-link text-primary"></i></a>
                </div>
            </div>
            <div class="form-item">
                <div class="col-offset-4 col-20">
                    <button class="btn btn-primary" type="button" (click)="submit()"
                        [disabled]="pedding || !isInOrgUnit">提交</button>
                </div>
            </div>
        </form>
    </div>
</div>