import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkBaseModal, SharkModalParams, SharkModalService, SharkToastrService, SharkValidForm, Cookie } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';
import { Event } from '@sharkr/utils';

@Component({
    templateUrl: './approve.modal.html',
    styleUrls: ['./approve.modal.scss']
})
export class ApproveModal extends SharkBaseModal {
    @ViewChild('form', { read: SharkValidForm }) form: SharkValidForm;
    approveData: any = {
        approved: true,
        operateRemark: ''
    };
    itemList: any = []
    checkedList: any[] = [];
    pedding: boolean = false;
    value: boolean = false;  // 责任条款确认
    onFocus(e: FocusEvent) {
        console.log('onFocus');
    }
    onBlur(e: FocusEvent) {
        console.log('onBlur');
    }
    constructor(
        private sharkModalService: SharkModalService,
        private router: Router,
        private params: SharkModalParams,
        private ajax: Ajax,
        private cookie: Cookie,
        private toastr: SharkToastrService
    ) {
        super();
        this.itemList = this.params.data.checkedList;
        // 是否通过
        this.approveData = this.params.data.approveData;
    }

    formatCheckList(list, v: boolean) {
        list.forEach(item => {
            if(item.checked) {
               this.checkedList.push({
                    flowId: item.flowMetaData.flowId,
                    nodeId: item.flowMetaData.currentNodeId,
                    operateRemark: this.approveData.operateRemark,
                    operateResult: v ? 'agree' : 'disagree',
                    workOrderId: item.flowNodeMessage.workOrderId,
                    workOrderType: item.flowNodeMessage.workOrderType,
                    orderType: item.flowMetaData.topologyId !== 'ius_org_identity_confirm_topology' ? item.flowNodeMessage.orderType : 9,
                    topologyId: item.flowMetaData.topologyId,
                    businessObject: item.flowNodeMessage.businessObject,
                    currUid: this.cookie.getCookie('yx_username'),
                    currUserName: this.cookie.getCookie('yx_name')
               }) 
            }
        });
        
    }

    onsubmit(isPass: boolean) {
        // 验证有没有勾选责任条款没有勾选Modal挺行
        if(!this.value) {
            this.sharkModalService.alert({
                title: '请您认真阅读“责任条款”的内容并确认遵守',
                okText:'我知道了'
            })
            return;
        }
        // 验证有没有输入内容
        if(!isPass && !this.approveData.operateRemark) {
            this.toastr.info('请输入备注信息');
            return;
        }
        this.approval(isPass)
    }

    // 批量审批
    async approval(isPass: boolean) {
        this.form.submit().then(async () => {
            Event.dispatch('nav:refreshDealing', null);
            this.pedding = true;
            let error = [];
            this.formatCheckList(this.itemList, isPass)

            try {
                const res = await this.ajax.postByJson(AjaxUrl.createFlow.batchApproval, this.checkedList);
                error = res.data || []
            } catch (e) {
                this.toastr.error(`工单审批失败: ${e.errorCode}`);

                error = null
            }
            
            this.pedding = false;
            this.close({ error });
        }, (re) => {});
    }
}
