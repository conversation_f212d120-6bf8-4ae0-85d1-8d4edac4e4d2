import { BaseWorkflow, FlowMetaData, FlowNodeMessage, SkipParam } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import { getBestOrg, getOrgAgencyUser, getSecondLevelManagerInfoByOrgId, getSecondLevelPoliticalInfoByOrgId, getThirdLevelManagerInfoByOrgId, isAuthedThirdLevelOrg, isOrgAgency } from '../../service/permcenter/approvePerm';
import { getUpperByOrgPosId } from '../../service/permcenter/unitOrg';
import Config from './onboard-config';

@Service
export default class Workflow extends BaseWorkflow {
    constructor(config: Config) {
        super(config);
    }
    /**
     * @description 获取节点的审批人信息。处理各个节点
     * @param flowMetaData 工单元数据
     * @param flowNodeMessage 工单业务数据
     * @returns string[] 审批人uid列表
     */
    async getApproverList(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<string[]> {
        const currentNodeId = flowMetaData.currentNodeId;
        let approverUsers = [];
        switch (currentNodeId) {
            // 三级部门代理审批人
            case '30010302':
                approverUsers = await getThirdLevelManagerInfoByOrgId(flowNodeMessage.orgPosId);
                break;
            // 二级部门负责人审批节点
            case '30010303':
                approverUsers = await getSecondLevelManagerInfoByOrgId(flowNodeMessage.orgPosId);
                break;
            // 指导员审批节点
            case '30010304':
                approverUsers = await getSecondLevelPoliticalInfoByOrgId(flowNodeMessage.orgPosId);
                break;
        }
        return approverUsers.map((approver: any) => {
            return approver.uid;
        });
    }

    /**
     * @description 流程跳转条件, 如果有多条件的跳转，需要用户进行重写实现，处理各个节点的跳转条件设置
     * @param flowMetaData
     * @param flowNodeMessage
     */
    async getCustomSkipParam(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<SkipParam> {
        const currentNodeId = flowMetaData.currentNodeId;
        const orgPosId = flowNodeMessage.orgPosId;
        const level = flowNodeMessage.level;
        const skipParam: SkipParam = { approved: flowNodeMessage.approved!, paramMap: {} };
        // 通过审批的情况下
        if (flowNodeMessage.approved) {
            switch (currentNodeId) {
                // 指导员审批节点
                case '30010304':
                    skipParam.paramMap.agency = await isAuthedThirdLevelOrg('onboard', level, orgPosId) || false;
                    break;
            }
        }
        return skipParam;
    }
}
