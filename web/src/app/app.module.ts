import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule, Routes } from '@angular/router';
import { AppComponent } from './app.component';
import { SharedModule } from './shared/shared.module';

// 业务模块
import { DepartureModule } from './departure/departure.module';
import { OnBoardModule } from './onboard/onboard.module';
import { TransferModule } from './transfer/transfer.module';
import { WorkflowModule } from './workflow/workflow.module';
import { PermModule } from './perm/perm.module';

import { HomeComponent } from './home/<USER>';
import { HomeModule } from './home/<USER>';
import { UserCenterModule } from './userCenter/userCenter.module';

import { UmcPermissionModule, UserPermListService } from '@yx-module/umc-permission'

//列表详情页
import { DocProjectModule } from './docProject/docProject.module';

// 定义常量 路由
const appRoutes: Routes = [
    {
        path: '',
        redirectTo: '/home',
        pathMatch: 'full'
    },
    {
        path: '**',
        component: HomeComponent
    }
];

@NgModule({
    imports: [
        BrowserModule,
        SharedModule,
        RouterModule.forRoot(appRoutes, {
            useHash: true,
            onSameUrlNavigation: 'reload'
        }),
        WorkflowModule,
        OnBoardModule,
        TransferModule,
        DepartureModule,
        PermModule,
        UserCenterModule,
        HomeModule,
        UmcPermissionModule,
        DocProjectModule
    ],
    declarations: [AppComponent],
    bootstrap: [AppComponent]
})
export class AppModule { }
