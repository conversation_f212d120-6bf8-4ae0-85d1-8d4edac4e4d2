<form class="form form-inline">
    <div class="form-item-group form-item-group-plained">

        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                申请人：
            </div>
            <div class="form-item-text col-16 word-break">
                {{flowData?.flowNodeMessage?.createUserName}}
            </div>
        </div>
        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                申请人邮箱：
            </div>
            <div class="form-item-text col-16 word-break">
                {{flowData?.flowNodeMessage?.createUser}}
            </div>
        </div>
        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                申请人工号：
            </div>
            <div class="form-item-text col-16 word-break">
                {{flowData?.flowNodeMessage?.employeeNum}}
            </div>
        </div>
        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                员工职责：
            </div>
            <div class="form-item-text col-16 word-break">
                <div>{{flowData?.flowNodeMessage?.duty}}</div>
            </div>
        </div>
    </div>
    <div class="form-item-group form-item-group-plained">
        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                所在严选统一业务架构：
            </div>
            <div class="form-item-text col-16 word-break">
                <div *ngFor="let item of posTreeList">{{item}}</div>
            </div>
        </div>
        <div class="form-item col-12">
            <div class="form-item-label col-8">
                工单编号：
            </div>
            <div class="form-item-text col-16">
                {{flowData?.flowMetaData?.globalId}}
            </div>
        </div>
        <div class="form-item col-12" *ngIf="flowData?.flowNodeMessage?.extend">
            <div class="form-item-label col-8">
                开通的角色名称：
            </div>
            <div class="form-item-text col-16">
                {{flowData?.flowNodeMessage?.extend.roleName}}
            </div>
        </div>
    </div>
    <div class="form-item-group form-item-group-plained">
        <div class="form-item col-24" *ngFor="let item of flowData?.flowNodeMessage?.applicantProductList">
            <div class="form-item-label col-4 word-break">
                {{item.productName}}(申请理由)：
            </div>
            <div class="form-item-text col-20 word-break">
                {{item.desc}}
            </div>
        </div>
    </div>
    <div class="form-item-group form-item-group-plained"
        *ngIf="flowData?.flowNodeMessage?.businessStatus === -1 && userInfo?.uid === flowData?.flowNodeMessage?.createUser">
        <div class="form-item col-12">
            <button class="btn btn-primary" (click)="goEditOrder()">编辑</button>
        </div>
    </div>
    <div class="form-item-group form-item-group-plained noprint"
        *ngIf="flowData?.flowNodeMessage?.businessStatus === 1 && userInfo?.uid === flowData?.flowNodeMessage?.createUser">
        <!-- 完结状态下，打印按钮-->
        <div class="form-item col-12">
            <button class="btn btn-primary" (click)="printOrder()">打印工单</button>
        </div>
    </div>
</form>