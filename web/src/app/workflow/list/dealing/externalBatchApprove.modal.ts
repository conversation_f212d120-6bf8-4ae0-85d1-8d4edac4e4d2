import { Component, ViewChild } from '@angular/core';
import { Ajax, SharkBaseModal, SharkModalParams, SharkModalService, SharkToastrService, SharkValidForm, Cookie } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';

@Component({
    template: `
        <div class="modal-header m-modal-approve">
            <a class="modal-close" (click)="dismiss()"></a>
            <span class="modal-title">批量审批</span>
        </div>
        <div class="modal-body">
            <div class="form form-horizontal" shark-validform #form="shark-validform">
                <div class="form-item" *ngIf="topologyId === 'ius_ext_system_auth_clear'" >
                    <label class="form-item-label col-3">是否清退：</label>
                    <div class="form-item-control col-18">
                        <shark-radio-group [(ngModel)]="clear" [radios]="radioOptions" shark-valid [validRule]="'required'"></shark-radio-group>
                    </div>
                </div>
                <div class="form-item" >
                    <label class="form-item-label col-3">备注信息：</label>
                    <div class="form-item-control col-18">
                        <shark-textarea  [size]="'lg'" [(ngModel)]="operateRemark" [placeholder]="'审批说明'"></shark-textarea>
                    </div>
                </div>
                <!-- 责任条款 -->
                <div class="m-liability-clause">
                    <div class="m-liability-content">
                        <p>责任条款</p>
                        <p>本人已知悉所涉内容为保密信息并确认此工单内容是此申请人工作所必须权限。本人承诺并保证在商业秘密保护及信息安全等方面，会对申请人做好宣贯与管理工作。本人清楚“谁主管，谁主责”的问责原则，如果本部门员工存在信息泄露或其他违规责任，本人愿承担相应责任。同时，本人保证遵守公司信息安全及商业秘密保护的各项规定，协助并配合公司各项措施和要求。如果有信息泄露或其他违规操作，本人愿承担相应责任。</p>
                    </div>
                    本人已认真阅读上述内容并确认遵守：<shark-switch labelOn="" labelOff="" [(ngModel)]="value"></shark-switch>
                </div>
            </div>
        </div>
        <div class="modal-footer">
        <button class="btn btn-primary " type="button" (click)="onsubmit(true)" [disabled]="submitting">通过</button>
        <button *ngIf="topologyId === 'ius_ext_system_auth_apply'" class="btn btn-secondary " type="button" (click)="onsubmit(false)" [disabled]="submitting">驳回</button>    
        </div>
    `,
    styleUrls: ['./approve.modal.scss']
})
export class ExternalBatchApproveModal extends SharkBaseModal {
    @ViewChild('form', { read: SharkValidForm }) form: SharkValidForm;
    operateRemark: string = '';
    itemList: any = []
    submitting: boolean = false;
    value: boolean = false;  // 责任条款确认
    topologyId: string;
    radioOptions: any = [
        {label: '是', value: 1},
        {label: '否', value: 0}
    ];
    clear: boolean;
    constructor(
        private sharkModalService: SharkModalService,
        private params: SharkModalParams,
        private ajax: Ajax,
        private cookie: Cookie,
        private toastr: SharkToastrService
    ) {
        super();
        this.itemList = this.params.data.checkedList;
        this.topologyId = this.params.data.topologyId;
    }

    onsubmit(isPass: boolean) {
        // 验证有没有勾选责任条款没有勾选Modal挺行
        if(!this.value) {
            this.sharkModalService.alert({
                title: '请您认真阅读“责任条款”的内容并确认遵守',
                okText:'我知道了'
            })
            return;
        }
        this.approval(isPass)
    }

    // 批量审批
    async approval(isPass: boolean) {
        this.form.submit().then(async () => {
            this.submitting = true;
            const list = []
            const bizData = this.topologyId === 'ius_ext_system_auth_clear' ? {
                clear: !!this.clear
            } : {
                proofPicUrl: ''
            }
            this.itemList.forEach((item) => {
              if (item.checked) {
                list.push({
                  topoId: item.flowMetaData.topologyId,
                  nodeId: item.flowMetaData.currentNodeId,
                  flowId: item.flowMetaData.flowId,
                  operateRemark: this.operateRemark,
                  operateResult: isPass ? 'agree' : 'disagree',
                  operatorUid: this.cookie.getCookie('yx_username'),
                  operatorName: this.cookie.getCookie('yx_name'),
                  bizData: bizData,
                })
              }
            })
            try {
                const url = this.topologyId === 'ius_ext_system_auth_clear' ? AjaxUrl.createFlow.externalBatchClear : AjaxUrl.createFlow.externalBatchApprove;
                const res = await this.ajax.postByJson(url, list)
                this.close(res)
            } catch (e) {
                this.toastr.error(`工单审批失败: ${e.errorCode}`);
            }
            
            this.submitting = false;
        });
    }
}
