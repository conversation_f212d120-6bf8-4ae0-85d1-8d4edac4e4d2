import moment from 'moment';
import { Component, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Ajax, <PERSON>ie, SharkToastrService, SharkModalService } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';
import { TopologyNamePipe } from '../../../shared/pipes/workflow.pipe';
import { UtilService } from '../../../shared/services/util.service';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import _ from 'lodash';
import { formatSpecialFlowData, flowNamesOptions, formatEntryFlowData, 
    formatSpecialChildFlowData, formatPersonalSpecialFlowData, formatQuitFlowData, 
    formatHoldPostFlowData, formatOrgIdentityFlowData, formatOrgIdentityChildFlowData, 
    formatNewOrgIdentityFlowData, workFlowTypeOptions, flowNamesAllOptions, topologyStatus, formatRemarkLabel, formatExternalSystemFlowData, 
    formatExternalSystemClearFlowData} from './../../utils';

@Component({
    templateUrl: './list.component.html',
    selector: 'bpm-newcreatelist',
    styleUrls: ['./list.component.scss']
})
export class CreatedListComponent {
    @Input()
    workFlowData: WorkFlowData = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };

    formatRemarkLabel = formatRemarkLabel
    
    itemList: any = [];

    // 整合业务数据
    extraBussinessData: any = []
    searchData: any = {

    };
    // 分页参数
    pagination: any = {
        page: 1,
        size: 10
    };
    email: string;// 当前登录人

    flowTypeOptions: any[] = [];
    isTest = false;
    hoverStr: string[] // hover的数据
    flowNameOptions: any[] = [];

    workFlowTypeOptions = workFlowTypeOptions

    
    topologyStatusOptions = topologyStatus

    // 时间选择器下拉类型
    public dateType: any = {
        POINT: '0',
        RANGE: '1',
        data: [
            { value: '0', name: '时间节点' },
            { value: '1', name: '时间范围' }
        ]
    };

    // 时间节点下拉
    public timeFrameOptions: any = {
        value: '0',
        data: [
            { value: '0', name: '最近24小时' },
            { value: '1', name: '最近7天' },
            { value: '2', name: '最近30天' },
            { value: '3', name: '最近3个月' }
        ]
    };
    detailMessage: string =
        '点击催办后，审批&会签人员会收到邮件提醒';
    constructor(
        private toastr: SharkToastrService,
        private ajax: Ajax,
        private cookie: Cookie,
        private router: Router,
        private aRoute: ActivatedRoute,
        private topoPipeServ: TopologyNamePipe,
        private utilService: UtilService,
        private sharkModalService: SharkModalService,
        private sharkToastrService: SharkToastrService,
    ) {
        this.email = this.cookie.getCookie('yx_username');
        // console.log(this.email);
        this.initDataOption();
        this.aRoute.params.subscribe((param) => {
            this.pagination.page = this.utilService.toNumber(param.page, 1);
            this.pagination.size = this.utilService.toNumber(param.size, 10);
            // this.searchData.topologyId = param.topologyId || '';
            // this.searchData.createUser = param.createUser || '';
            // this.searchData.dateType = this.dateType.POINT
            this.getItemList();
        });
        this.isTest = location.host === 'local.yx.mail.netease.com:9000';

    }

    initDataOption() {
        this.flowTypeOptions = [{
            name: '请选择',
            value: ''
        }].concat(this.topoPipeServ.transform('', true));
        // this.flowNameOptions = this.topoPipeServ.transform('', true)
    }

    // 处理工单类型点击事件
    changeWolkflowType(e) {
        if (e) {
            // this.flowTypeOptions = [{
            //     name: '请选择',
            //     value: ''
            // }].concat(flowNamesOptions[e])
            this.flowTypeOptions = flowNamesOptions[e]
        } else {
            this.flowTypeOptions = flowNamesAllOptions
        }
        // 可选择所有的工单名称
        this.searchData.topologyName = ''
        
    }
    // 获取待我审批的工单列表
    getItemList() {
        const _searchData = this.dealSpecialParams()

        
        
        const param = Object.assign({}, {
            status: [0, 100, 200, -1], // 默认传所有状态列表
            ..._searchData,
            ...this.pagination
        });
        this.ajax.postByJson(AjaxUrl.bpmflow.queryCreatedList, param).then((res) => {
            this.itemList = res.data.result || [];

        
            this.itemList = this.itemList.map(itemData => {
                if(itemData.flowMetaData.topologyId === "ius_personal_special_rights") {
                    formatPersonalSpecialFlowData(itemData)
                }
                if(itemData.flowMetaData.topologyId === "ius_special_rights") {
                    formatSpecialFlowData(itemData)
                }
                if(itemData.flowMetaData.topologyId === "ius_special_rights_child" || itemData.flowMetaData.topologyId === "ius_personal_special_rights_child") {
                    formatSpecialChildFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_entry_apply_topology") {
                    formatEntryFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_quit_apply_topology") {
                    formatQuitFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_hold_post_apply_topology") {
                    formatHoldPostFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_permit_topology") {
                    formatOrgIdentityFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_permit_child_topology") {
                    formatOrgIdentityChildFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_confirm_topology") {
                    formatNewOrgIdentityFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_ext_system_auth_apply") {
                    formatExternalSystemFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_ext_system_auth_clear") {
                    formatExternalSystemClearFlowData(itemData)
                }
                return itemData;
            });

            
            
            
            this.pagination = res.data.pagination;
            this.getCompleteItemList(this.itemList)
        }, (err) => {
            this.toastr.error(`查询我创建的工单列表失败:${err.errorCode}`);
        });
    }


    /**
     * 请求流程状态
     */
    getCompleteItemList(list) {
        const params = {
            flowIdList: [],
            nodeIdList: []
        }
        list.forEach(ele => {
            params.flowIdList.push(ele.flowMetaData.flowId)
            params.nodeIdList.push(
                ele.flowNodeMessage.nodeId || ele.flowMetaData.currentNodeId
            )
        });

        this.ajax.get(AjaxUrl.createFlow.flowStatus, params).then((res) => {
            this.extraBussinessData = res.data || []
        })

    }

    getWorkPostValue(topologyId, idx) {
        const { flowNodeMessage } = this.itemList[idx] || {flowNodeMessage: {businessObject: {}}};
        const { businessObject } = flowNodeMessage || {businessObject: {}};
        const { applyedIdentityList, newIdentityList } = businessObject || {applyedIdentityList: "[]", newIdentityList: "[]"};

        if (topologyId === 'ius_hold_post_apply_topology') {
            const _applyedIdentityList = JSON.parse(applyedIdentityList || "[]");
            return _applyedIdentityList.map(item => item.postName).join('；') || '无'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            const _newIdentityList = JSON.parse(newIdentityList || "[]");
            return _newIdentityList.map(item => item.postName).join('；') || '无'
        }
        return (this.extraBussinessData[idx] || {}).workPost || '无';
    }

    getWorkDepartValue(topologyId, idx) {
        const { flowNodeMessage } = this.itemList[idx] || {flowNodeMessage: {businessObject: {}}};
        const { businessObject } = flowNodeMessage || {businessObject: {}};
        const { applyedIdentityList, newIdentityList } = businessObject || {applyedIdentityList: "[]", newIdentityList: "[]"};
        if (topologyId === 'ius_hold_post_apply_topology') {
            const _applyedIdentityList = JSON.parse(applyedIdentityList || "[]");
            return _applyedIdentityList[0] ? _applyedIdentityList[0].departmentName.replace(/ \/ /g, '-') : '无'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            const _newIdentityList = JSON.parse(newIdentityList || "[]");
            return _newIdentityList[0] ? _newIdentityList[0].departmentName.replace(/ \/ /g, '-') : '无'
        }
        return (this.extraBussinessData[idx] || {}).department || '无';
    }

    getWorkDepartLabel(topologyId) {
        if (topologyId === 'ius_hold_post_apply_topology') {
            return '兼职部门'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            return '部门（新）'
        }
        return '部门'
    }

    getWorkPostLabel(topologyId) {
        if (topologyId === 'ius_hold_post_apply_topology') {
            return '兼职岗位'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            return '岗位（新）'
        }
        return '岗位'
    }

    doSearch() {
        this.pagination.page = 1;
        this.search();
    }
    doReset() {
        // 清空搜索项
        this.searchData = {}
        this.pagination.page = 1;
        this.search();
    }

    /**
     * 处理时间节点
     */
    dealTimeNode(node) {
        const createEndTime = moment().valueOf()
        const getLast24Hours = () => {
            return moment().subtract(1, 'days').valueOf()
        }
        const getLast7Days = () => {
            return moment().subtract(7, 'days').valueOf()
        }

        const getLastMonth = () => {
            return moment().subtract(1, 'months').valueOf()
        } 

        const getLastThreeMonth = () => {
            return moment().subtract(3, 'months').valueOf()
        } 
        const dateType = {
            0: {createStartTime: getLast24Hours(), createEndTime},
            1: {createStartTime: getLast7Days(), createEndTime},
            2: {createStartTime: getLastMonth(), createEndTime},
            3: {createStartTime: getLastThreeMonth(), createEndTime},
        }

        return dateType[node]
    }

    dealSpecialParams() {
        const { topologyId, status } = this.searchData
        const _searchData = _.cloneDeep(this.searchData)
        
        if (topologyId === 'ius_transfer_apply_topology') {
            _searchData.topologyId = 'ius_entry_apply_topology'
            _searchData.applyType = 0
        }   

        if (topologyId === 'ius_entry_apply_topology') {
            _searchData.applyType = 1
        }

        if (status === 8) {
            _searchData.businessStatus = 8
            _searchData.status = [0, 100, 200, -1]
        } else if (status === -1) {
            _searchData.businessStatus = 3
            _searchData.status = [0, 100, 200, -1]
        } else if (status === -2) {
            _searchData.businessStatus = 4
            _searchData.status = [0, 100, 200, -1]
        } else {
            _searchData.businessStatus = ''
        }
        
        return _searchData
    }
    
    search() {
        // console.log(this.searchData.topologyId);
        /**
         * 处理时间节点
         */
        let time = {}
        const list = []
        const {createTime, topologyIds, topologyId, status } = this.searchData
        
        if (createTime) {
            if(this.searchData.dateType === '0') {
                time = this.dealTimeNode(createTime)
            } else {
                time = {createStartTime: createTime[0], createEndTime: createTime[1]}
            }
            this.searchData = {...this.searchData, ...time}
        }

        if (topologyIds) {
            flowNamesOptions[topologyIds].map((item) => {
                list.push(item.value)
                this.searchData.topologyIdList = list
            })
        }

        
        const _searchData = this.dealSpecialParams();

        this.router.navigate(['/workflow/createdList', {
            ..._searchData,
            ...this.pagination,
            ...time,
            _r: Date.now()
        }]);
    }

    onPageChanged(event) {
        this.pagination.page = event.data.page;
        this.search();
    }

    onSizeChanged(event) {
        this.pagination.page = 1;
        this.pagination.size = event.data.size;
        this.search();
    }
    
    targetShow(item) {
        item.show = !item.show
    }

    // 查看 跳转详情页
    detail(item) {
        if(!item) {
            // TODO 点击名字跳转详情
            return
        }
        if (item.flowMetaData.topologyName === "ius_personal_special_rights") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialFlowApprove/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_special_rights") {
            const flowId = item.flowMetaData.flowId
            if (item.flowNodeMessage.nodeId === '30110101') {
                window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
            } else if (item.flowNodeMessage.nodeId === '30110102') {
                window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
            } else if (item.flowNodeMessage.nodeId === '30110103') {
                if (item.flowNodeMessage.haveChildFlow) {
                    window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
                } else {
                    window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
                }
            } else {
                if (item.flowNodeMessage.haveChildFlow) {
                    window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
                } else {
                    window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
                }
            }
        } else if (item.flowMetaData.topologyName === "ius_special_rights_child") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_entry_apply_topology") {
            const flowId = item.flowMetaData.flowId
            if (item.flowMetaData.currentNodeId === '30110303' || item.flowMetaData.currentNodeId === '9999') {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryApproveFlow/${flowId}`);  
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow/${flowId}`);  
            }
        } else if (item.flowMetaData.topologyName === "ius_quit_apply_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/quitApproveFlow/${flowId}`);
            // if (item.flowMetaData.currentNodeId === '30110503' || item.flowMetaData.currentNodeId === '9999') {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/quitApproveFlow/${flowId}`);  
            // } else {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/quitFlow/${flowId}`);  
            // }
        } else if (item.flowMetaData.topologyName === "ius_hold_post_apply_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/concurrentApproveFlow/${flowId}`);
            // if (item.flowMetaData.currentNodeId === '30110603' || item.flowMetaData.currentNodeId === '9999') {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/concurrentApproveFlow/${flowId}`);  
            // } else {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/concurrentFlow/${flowId}`);  
            // }
        } else if (item.flowMetaData.topologyName === "ius_org_identity_permit_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/identityApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_org_identity_permit_child_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/identityChildApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_org_identity_confirm_topology") {
            const flowId = item.flowMetaData.flowId
            if (item.flowMetaData.currentNodeId === '30110903' || item.flowMetaData.currentNodeId === '9999') {
                window.location.href = encodeURI(`/bflow/icacflow/#/newIdentityApproveFlow/${flowId}`);  
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/newIdentityFlow/${flowId}`);  
            }
        } else if (item.flowMetaData.topologyName === "ius_ext_system_auth_apply" || item.flowMetaData.topologyName === "ius_ext_system_auth_apply_rpa") {
             const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_ext_system_auth_clear") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemClearFlow/${flowId}`);
        } else {
            this.router.navigate([this.utilService.judgeRouter(item.flowMetaData.topologyName), {
                flowId: item.flowMetaData.flowId,
                topologyId: item.flowMetaData.topologyId,
                enterType: 3
            }]);
        }
    }
    // 撤回工单
    async revoke(item){
        const params = {
            flowMetaData: item.flowMetaData,
            flowNodeMessage: item.flowNodeMessage
        }
        this.sharkModalService.confirm(
            {
                title: '撤回工单',
                content: '<p>确定要撤回此工单吗？</p>',
                okText: '确定',
                cancelText: '取消'
            }).then(async () => {
                await this.ajax.postByJson(AjaxUrl.docManagement.revoke, params).then(res => {
                    if(res.code === 200){
                        this.toastr.success('撤回成功！');
                        //自动刷新列表
                        this.pagination = {
                            page: 1,
                            size: 10
                        };
                        this.getItemList();

                    }
                },(err) =>{
                    this.toastr.error('撤回失败！');
                })
            },() => {
                this.toastr.success('取消撤回！');
            });
    }
    // 撤回作废工单
    async revokeDel(item) {
        const params = {
            flowMetaData: item.flowMetaData,
            flowNodeMessage: item.flowNodeMessage
        }
        this.sharkModalService.confirm(
            {
                title: '撤回工单',
                content: '<p>确定要撤回此工单吗？</p>',
                okText: '确定',
                cancelText: '取消'
            }).then(async () => {
                await this.ajax.postByJson(AjaxUrl.docManagement.revokeDel, params).then(res => {
                    if(res.code === 200){
                        this.toastr.success('撤回成功！');
                        //自动刷新列表
                        this.pagination = {
                            page: 1,
                            size: 10
                        };
                        this.getItemList();
                    }
                },(err) => {
                    this.toastr.error('撤回失败！');
                })
            }, () => {
                this.toastr.success('取消撤回！');
            });
        }
    // 点击编辑，跳转至成文系统编辑文档页面
    toEdit(item:any) {
        // console.log(item.flowNodeMessage.content.fileId);
        if (window.location.hostname === 'bflow.test.yx.mail.netease.com') {
            window.open('http://document.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + item.flowNodeMessage.content.fileId);
        } else if (window.location.hostname === 'dev.test.yx.mail.netease.com') {
            window.open('http://dev.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + item.flowNodeMessage.content.fileId);
        } else if (window.location.hostname === 'yx.mail.netease.com') {
            window.open('http://yx.mail.netease.com/written-doc/index.html#/editdocs/' + item.flowNodeMessage.content.fileId);
        } else if (window.location.hostname === 'test.yx.mail.netease.com') {
            window.open('http://test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + item.flowNodeMessage.content.fileId);
        } else {
            window.open('http://remote.yx.mail.netease.com:9000/written-doc/index.html#/editdocs/' + item.flowNodeMessage.content.fileId);
        }
    }
    // 点击处理，跳转至成文系统对应文档查看页面
    toDeal(item:any){
        if (window.location.hostname === 'bflow.test.yx.mail.netease.com') {
            window.open('http://document.test.yx.mail.netease.com/written-doc/index.html#/doc/' + item.flowNodeMessage.content.fileId + '/doc')
            // window.open('http://test.yx.mail.netease.com/written-doc/index.html#/doc/'+ this.flowData.flowNodeMessage.content.categoryId +'/doc') 
        } else if (window.location.hostname === 'dev.test.yx.mail.netease.com') {
            window.open('http://dev.test.yx.mail.netease.com/written-doc/index.html#/doc/' + item.flowNodeMessage.content.fileId + '/doc')
        } else if (window.location.hostname === 'yx.mail.netease.com') {
            window.open('http://yx.mail.netease.com/written-doc/index.html#/doc/' + item.flowNodeMessage.content.fileId + '/doc')
        } else if (window.location.hostname === 'test.yx.mail.netease.com') {
            window.open('http://test.yx.mail.netease.com/written-doc/index.html#/doc/' + item.flowNodeMessage.content.fileId + '/doc')
        } else {
            window.open('http://remote.yx.mail.netease.com:9000/written-doc/index.html#/doc/' + item.flowNodeMessage.content.fileId + '/doc')
        }
    }

    goEditOrder(item) {
        this.router.navigate([this.utilService.judgeRouter(item.flowMetaData.topologyName), {
            flowId: item.flowMetaData.flowId,
            topologyId: item.flowMetaData.topologyId,
            isEdit: 1
        }]);
    }
    
    // 催办功能
    // 催办操作
   onUrge(flowId: string) {
    this.sharkModalService.confirm({
      title: '确定发起催办吗？',
      okText: '确定',
      cancelText: '取消'
    }).then(async () => {
        try {
            await this.ajax.get(AjaxUrl.docManagement.reminders,{flowId:flowId})
            this.sharkToastrService.success('操作成功')
        }catch(e){
            this.sharkToastrService.error('操作失败')
        }
    });
  }

}