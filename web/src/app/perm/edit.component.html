<form class="form form-horizontal" shark-validform #form="shark-validform" autocomplete="off">
    <div class="form-item">
        <label class="form-item-label col-4">
            申请人：
        </label>
        <div class="form-item-text col-14">
            {{showData.appler}}
        </div>
    </div>
    <div class="form-item">
        <label class="form-item-label col-4">
            选择所在的严选统一业务架构：
        </label>
        <div class="form-item-control col-14">
            <!-- <div *ngFor="let item of showData.orgPosTreeList">{{item}}</div> -->
            <shark-radio-group name="orgPosTreeList" [(ngModel)]="this.flowData.flowNodeMessage.orgPosId"
                [radios]="showData.orgPosTreeList"></shark-radio-group>
        </div>
    </div>
    <div class="form-item">
        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                选择申请权限的系统:
            </div>
            <div class="form-item-control col-16">
                <shark-ctree #tree name="productListName" shark-valid [validRule]="'required'" [autolink]="false"
                    [expandAll]="true" [(ngModel)]="checkedList" [line]=true [data]="productList"
                    (onChecked)="onChecked($event)">
                </shark-ctree>
            </div>
        </div>
    </div>
    <!-- 已选中的系统需要填写备注 -->
    <div class="form-item" *ngFor="let item of flowData.flowNodeMessage.applicantProductList; let i = index;">
        <div class="form-item col-12">
            <div class="form-item-label col-8 word-break">
                {{item.productName}}(申请理由)：
            </div>
            <div class="form-item-control col-16">
                <shark-textarea [size]="'lg'" [placeholder]="item.placeholder" shark-valid [validRule]="'required'"
                    name="'desc{{i}}'" [(ngModel)]="item.desc"></shark-textarea>
            </div>
        </div>
    </div>
    <div class="form-item">
        <label class="form-item-label col-4">跟踪者：</label>
        <div class="form-item-control col-16">
            <user-select-result-list [(userList)]="userList" [placeholder]="'请选择对应的用户'"></user-select-result-list>
            <button class="btn btn-primary btn-sm" type="button" (click)="selectUsers()">添加跟踪者</button>
        </div>
    </div>
    <div class="form-item">
        <div class="col-offset-4 col-20">
            <button class="btn btn-primary" type="button" (click)="save()" [disabled]="pedding">提交</button>
        </div>
    </div>
</form>