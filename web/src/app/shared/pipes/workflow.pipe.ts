import { Pipe, PipeTransform } from '@angular/core';
import { BasePipe } from './base.pipe';

const TopologyNames = {
    ius_special_rights: '个人特殊权限申请',
    ius_special_rights_child: '个人特殊权限申请(子工单)',
    ius_personal_special_rights: '个人特殊权限申请（老）',
    ius_transfer_apply_topology: '组织身份确认(入职)',
    ius_org_identity_confirm_topology: '组织身份确认(转岗)',
    ius_entry_apply_topology: '加入严选组织架构',
    ius_org_identity_permit_topology: '组织身份权限申请',
    ius_org_identity_permit_child_topology: '组织身份权限申请(子工单)',
    ius_hold_post_apply_topology: '兼岗工单',
    ius_quit_apply_topology: '离职工单',
    written_doc_project: '成文文档下发',
    written_doc_delete_project:'文档作废工单',
    nav_personnelchange_onboard: '入职申请（工单已下线）',
    nav_personnelchange_departure: '离职申请（工单已下线）',
    nav_personnelchange_transfer: '转岗申请（工单已下线）',
    ius_ext_system_auth_apply: '外部系统权限申请',
    ius_ext_system_auth_apply_rpa: '外部系统权限申请(RPA)',
    ius_ext_system_auth_clear: '外部系统权限清退',
    // nav_personnelchange_perm: '业务系统权限申请',
    // nav_personnelchange_perm_child: '业务系统权限申请-子工单',
};
@Pipe({
    name: 'toponamepipe'
})
export class TopologyNamePipe extends BasePipe implements PipeTransform {
    constructor() {
        super();
    }

    transform(key?, isList = false) {
        return this.transfromData(TopologyNames, 'string', key, isList);
    }
}


@Pipe({
    name: 'topoauthnamepipe'
})
export class TopologyAuthNamePipe extends BasePipe implements PipeTransform {
    constructor() {
        super();
    }

    transform(key?, isList = false) {
        
        
        if (key.flowMetaData.topologyName === 'ius_entry_apply_topology') {
            if (key.flowNodeMessage.conditionParamMap.applyType === 0) {
                return '组织身份确认(入职)'
            }
        }
        
        return this.transfromData(TopologyNames, 'string', key.flowMetaData.topologyName, isList);
    }
}

const NodeNames = {
    '': '结束',
    '30010201': '员工提交离职申请',
    '30010202': '代理人审批',
    '30010203': '二级部门负责人审批',
    '30010204': '指导员审批',
    '30010210': '拒绝',
    '30010301': '员工提交入职申请',
    '30010302': '代理人审批',
    '30010303': '二级部门负责人审批',
    '30010304': '指导员审批',
    '30010310': '拒绝',
    '30010401': '员工提交转岗申请',
    '30010402': '代理人审批(转出)',
    '30010403': '二级部门负责人审批(转出)',
    '30010404': '代理人审批(转入)',
    '30010405': '二级部门负责人审批(转入)',
    '30010407': '指导员审批(转出)',
    '30010408': '指导员审批(转入)',
    '30010410': '拒绝',
    '30010501': '员工提交权限申请',
    '30010503': '三级部门负责人审批',
    '30010504': '部门审核',
    '30010505': '回绝申请人处理',
    '30010506': '二级部门负责人审批',
    '30010514': '二级部门负责人审批',
    '30010516': '产品归属二级负责人审批',
    '30010517': '产品经理审批',
    '30010518': '拒绝',
    '70210101': '拟写成文文件',
    '70210102': '内控文档管理员复核',
    '70210103': '会签人员会签',
    '70210104': '三级部门负责人审核',
    '70210105': '二级部门负责人审核',
    '70210106': '网易严选CEO审批',
    '70210107': '工单完结',
    '70210108': '内控与合规部负责人审定',
    '70210109':  'ISO管理员复核',
    '70210301': '发起文档作废',
    '70210302': '主责部门负责人审批',
    '70210303': '工单完结',
    '70210304': '内控文档管理员复核确认',
    '30110101': '部门负责人审批',
    '30110102': '部门负责人审批',
    '30110103': '系统负责人开通权限',
    '30110104': '系统负责人（子工单）开通权限',
    '30110201': '系统负责人开通权限', // 个人特殊权限-子工单
    '30110301': '加入严选组织架构申请',
    '30110302': '申请人确认',
    '30110303': '部门负责人审批',
    '30110601': '兼岗工单申请',
    '30110602': '部门负责人审批',
    '30110603': '原二级部门负责人审批',
    '30110604': '兼职部门负责人审批',
    '30110605': '兼职二级部门部门负责人审批',
    '30110501': '离职工单申请',
    '30110502': '申请人确认',
    '30110503': '部门负责人审批',
    '30110701': '组织身份权限申请',
    '30110702': '部门负责人审批',
    '30110703': '系统负责人开通权限',
    '30110704': '系统负责人（子工单）开通权限',
    '30110801': '系统负责人开通权限',
    '30110901': '新组织身份确认工单创建',
    '30110902': '申请人确认',
    '30110903': '部门负责人审批',
    '30111001': '外部系统权限申请',
    '30111002': '申请人部门负责人',
    '30111003': '财务审批',
    '30111004': '系统管理部门负责人审批',
    '30111005': '系统管理员开通权限',
    '30111100': '外部系统权限清退申请',
    '30111101': '系统管理员清退权限'  
};

// tslint:disable-next-line:max-classes-per-file
@Pipe({
    name: 'nodenamepipe'
})
export class NodeNamePipe implements PipeTransform {
    transform(value: any) {
        return NodeNames[value] || '/';
    }
}

const FlowState = {
    '0': '创建',
    '100': '处理中',
    '200': '完结'
};
// tslint:disable-next-line:max-classes-per-file
@Pipe({
    name: 'flowstate'
})
export class FlowStatePipe implements PipeTransform {
    transform(value) {
        return FlowState[value];
    }
}


// const  DelFlowState = {
//     '0': '待审批',
//     '100': '审批通过',
//     '200': '审批不通过'
// }
// @Pipe({
//     name: 'delflowstate'
// })
// export class DelFlowStatePipe implements PipeTransform {
//     transform(value) {
//         return DelFlowState[value];
//     }
// }

const FlowBusinessState = {
    '-1': '拒绝',
    '0': '处理中',
    '1': '完结'
};
// tslint:disable-next-line:max-classes-per-file
@Pipe({
    name: 'flowbusinessstate'
})
export class FlowBusinessStatePipe implements PipeTransform {
    transform(value) {
        return FlowBusinessState[value];
    }
}

const FlowSpecialBusinessState = {
    '0': '审批中',
    '1': '审批中',
    '2': '完结',  // 完结（通过)
    '3': '驳回',  // 完结(拒绝)
    '4': '驳回中待确认'  // 未完结（驳回）
};
// tslint:disable-next-line:max-classes-per-file
@Pipe({
    name: 'flowspecialbusinessstate'
})
export class FlowSpecialBusinessStatePipe implements PipeTransform {
    transform(value) {
        return FlowSpecialBusinessState[value];
    }
}


@Pipe({
    name: 'muliapplypipe'
})
export class MultiApplySystemPipe implements PipeTransform {
    transform(item) {
        const {flowMetaData, flowNodeMessage} = item
        let flowItems = []
        
        if (flowMetaData.topologyId === 'ius_special_rights_child' || flowMetaData.topologyId === 'ius_org_identity_permit_child_topology') {
            flowItems = [flowNodeMessage.currApplySystem]
        } else if (flowMetaData.topologyId === 'ius_ext_system_auth_apply' || flowMetaData.topologyId === 'ius_ext_system_auth_apply_rpa' || flowMetaData.topologyId === 'ius_ext_system_auth_clear') {
            flowItems = [
              {
                applySystemName: flowNodeMessage.systemName,
                applySystemDesc: `${flowNodeMessage.accountName}（${flowNodeMessage.applyRole}）`
              },
            ]
            if (flowNodeMessage.changeUserInfo) {
              flowItems.push({
                applySystemName: `${flowNodeMessage.changeUserInfo.name}(${flowNodeMessage.changeUserInfo.uid})`,
                applySystemDesc: flowNodeMessage.changeUserInfo.changeStatus
              })
            }
        } else {
            flowItems =  flowNodeMessage.businessObject.multiApplySystem
        }
        
        const list = flowItems.map((item) => {
            return `${item.applySystemName}：${item.applySystemDesc}；`
        })
        return list.join('  ')
    }
}


