import { Service } from '@tiger/boot';
import { getServiceUrl } from '@tiger/core';
import axios from '@tiger/request';

const domains = {
    // dev: 'http://local.yx.mail.netease.com:9300',
    dev: 'http://bflowadmin.you.163.com',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-nav-bflow-admin.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-nav-bflow-admin.service.mailsaas'
};

/**
 * 获取各业务系统产品信息服务
 */
const productDomains = {
    dev: 'http://127.0.0.1:8550/proxy/foreign.yanxuan-ius.service.mailsaas',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-app.service.mailsaas'
};

@Service
export class ApplicationService {

    async getTagsWithApplications(email: string) {
        const serviceUrl = getServiceUrl(domains) + '/api/user/getTagsWithApplications.do';
        const res = await axios.get(serviceUrl, { params: { email } });

        return res.data;
    }

    async getFavApps(email: string) {
        const serviceUrl = getServiceUrl(domains) + '/api/user/getFavApps.do';
        const res = await axios.get(serviceUrl, { params: { email } });

        return res.data;
    }

    async addFavApp(email: string, appId: string) {
        const serviceUrl = getServiceUrl(domains) + '/api/user/addFavApp.do';
        const res = await axios.post(serviceUrl, { email, appId });

        return res.data;
    }

    async cancelFavApp(email: string, appId: string) {
        const serviceUrl = getServiceUrl(domains) + '/api/user/cancelFavApp.do';
        const res = await axios.post(serviceUrl, { email, appId });

        return res.data;
    }

    async getAllProduct() {
        const serviceUrl = getServiceUrl(productDomains) + '/navAdmin/product/getAllProduct.json';
        const res = await axios.post(serviceUrl);

        return res.data;
    }
}
