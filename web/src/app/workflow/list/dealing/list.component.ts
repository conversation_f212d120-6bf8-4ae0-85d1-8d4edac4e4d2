import { Component, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Ajax, Cookie, SharkModalService, SharkToastrService } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';
import { TopologyNamePipe, NodeNamePipe } from '../../../shared/pipes/workflow.pipe';
import { UtilService } from '../../../shared/services/util.service';
import { ApproveModal } from './approve.modal';
import { FailModal } from './fail.modal'
import { formatSpecialFlowData, formatEntryFlowData, formatSpecialChildFlowData, formatPersonalSpecialFlowData, formatQuitFlowData, formatHoldPostFlowData, formatOrgIdentityFlowData, formatOrgIdentityChildFlowData, formatNewOrgIdentityFlowData, flowNamesOptionsForWait, formatRemarkLabel, formatExternalSystemFlowData, formatExternalSystemClearFlowData } from './../../utils';
import { Event } from '@sharkr/utils';
import { ExternalBatchApproveModal } from './externalBatchApprove.modal';

@Component({
    templateUrl: './list.component.html',
    selector: 'bpm-newwaitlist',
    styleUrls: ['./list.component.scss']
})
export class DealingListComponent {
    itemList: any = [];
    searchData: any = {
    };
    // 分页参数
    pagination: any = {
        page: 1,
        size: 10,
        totalPage: 1
    };
    
    email: string = '' // 用户邮箱
    isMobile: boolean = 'ontouchstart' in window; // 判断是否是移动设备

    // 工单类型定义
    WORKFLOW_TYPE = {
        All: 'All',
        Authority: 'Authority',
        Organizational: 'Organizational',
        Document: 'Document'
    }


    formatRemarkLabel = formatRemarkLabel

    activeTypeBtn: string = this.WORKFLOW_TYPE.All // 保存当前高亮的工单类型按钮

     
 
    selectedCount = 0 // 批量数量 

    // 整合业务数据
    extraBussinessData: any = []


    // 保存每页item的批量选择状态
    pageOfApprovalStatus = []

    flowNamesAllOptions = flowNamesOptionsForWait

    isBatchAudit: boolean = false; // 是否批量操作

    showBatchApproval: boolean = false; // 是否展示批量审批按钮
    
    isAddCount = false;
    
    isInitPageOfApprovalStatus = false; // 只初始化一次批量列表
    
 
    constructor(
        private sharkModalService: SharkModalService,
        private toastr: SharkToastrService,
        private ajax: Ajax,
        private router: Router,
        private aRoute: ActivatedRoute,
        private topoPipeServ: TopologyNamePipe,
        private nodeNamePipeServ: NodeNamePipe,
        private utilService: UtilService,
        private ref: ChangeDetectorRef,
        private cookie: Cookie
    ) {
        this.email = this.cookie.getCookie('yx_username');
        
        this.aRoute.params.subscribe((param) => {
            this.pagination.page = this.utilService.toNumber(param.page, 1);
            this.pagination.size = this.utilService.toNumber(param.size, 10);
            this.pagination.totalPage = this.utilService.toNumber(param.totalPage, 1)
            this.searchData.topologyId = param.topologyId || '';
            this.searchData.createUser = param.createUser || '';
            this.getItemList();
        });
        
        this.formatWorkOrderSearchBtn();
    }


    // 初始化批量列表
    initPageOfApprovalStatus() {
        this.pageOfApprovalStatus = []
        
        for (let i = 0, length = this.pagination.totalPage + 1; i < length; i++) {
            this.pageOfApprovalStatus.push({
                withAll: false, // 是否全选
                checkedList: [] // 已选择的item
            })
        }
    }


    // 获取待我审批的工单列表
    getItemList() {
        const param = Object.assign({}, {
            status: [0, 100],
            ...this.searchData,
            ...this.pagination
        });
        this.ajax.postByJson(AjaxUrl.bpmflow.queryDealingList, param).then((res) => {
            const list = res.data.result || [];
            this.itemList = list.map(itemData => {
                if(itemData.flowMetaData.topologyId === "ius_personal_special_rights") {
                    formatPersonalSpecialFlowData(itemData)
                }
                if(itemData.flowMetaData.topologyId === "ius_special_rights") {
                    formatSpecialFlowData(itemData)
                }
                if(itemData.flowMetaData.topologyId === "ius_special_rights_child" || itemData.flowMetaData.topologyId === "ius_personal_special_rights_child") {
                    
                    formatSpecialChildFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_entry_apply_topology") {
                    formatEntryFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_quit_apply_topology") {
                    formatQuitFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_hold_post_apply_topology") {
                    formatHoldPostFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_permit_topology") {
                    formatOrgIdentityFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_permit_child_topology") {
                    formatOrgIdentityChildFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_confirm_topology") {
                    formatNewOrgIdentityFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_ext_system_auth_apply" || itemData.flowMetaData.topologyId === "ius_ext_system_auth_apply_rpa") {
                    formatExternalSystemFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_ext_system_auth_clear") {
                    formatExternalSystemClearFlowData(itemData)
                }
                const action = itemData.flowMetaData.topologyId.replace('nav_personnelchange_', '');
                itemData.disabled = !(action in AjaxUrl.submitFlow);
                // 转岗申请的指导员转出审批，不能批量审批
                if (itemData.flowMetaData.topologyId === 'nav_personnelchange_transfer' && itemData.flowMetaData.currentNodeId === '30010407') {
                    itemData.disabled = true;
                }
                return itemData;
            });

            this.pagination = res.data.pagination || {};
            
            if (!this.isInitPageOfApprovalStatus) {
                this.initPageOfApprovalStatus();
                this.isInitPageOfApprovalStatus = true
            }
            this.getCompleteItemList(this.itemList);
            this.showBatchCheckedItem(this.itemList)
        }, (err) => {
            this.toastr.error(`查询我创建的工单列表失败:${err.errorCode}`);
        });
    }

    /**
     * 记录当前页被选中的item
     * @param list {itemList}
     */
     showBatchCheckedItem(list) {
         const { checkedList } = this.pageOfApprovalStatus[this.pagination.page]
         checkedList.forEach((item ,i: number) => {
            if (item) list[i].checked = true
        });
     }

     /**
     * 请求流程数据
     */
      getCompleteItemList(list) {
        const params = {
            flowIdList: [],
            nodeIdList: []
        }
        list.forEach(ele => {
            params.flowIdList.push(ele.flowMetaData.flowId)
            params.nodeIdList.push(
                ele.flowNodeMessage.nodeId || ele.flowMetaData.currentNodeId
            )
        });

        this.ajax.get(AjaxUrl.createFlow.flowStatus, params).then((res) => {
            this.extraBussinessData = res.data || []
        })
    }

    // 获取工单创建人
    getUserName(item) {
        const {conditionParamMap = {}} = item.flowNodeMessage

        if (item.flowMetaData.topologyId === 'ius_entry_apply_topology' && conditionParamMap.applyType === 0 ) return conditionParamMap.username || ''
        if (item.flowMetaData.topologyId === 'ius_org_identity_confirm_topology') return item.flowNodeMessage.currUserName || ''
        
        return item.flowNodeMessage.createUserName
    }


    // 点击工单名称
    onClickName(id, index: number) {
        this.activeNameBtn = id.toString()

        this.searchData.topologyIdList = id
        this.searchData.applyType = ''

        if (id.toString() === 'ius_transfer_apply_topology') {
            this.searchData.topologyIdList = ['ius_entry_apply_topology']
            this.searchData.applyType = 0
        }

        if (id.toString() === 'ius_entry_apply_topology') {
            this.searchData.applyType = 1
        }

        if (id.toString() === 'ius_ext_system_auth_apply' || id.toString() === 'ius_ext_system_auth_apply_rpa' || id.toString() === 'ius_ext_system_auth_clear') {
            this.showBatchApproval = true
        } else {
            this.showBatchApproval = this.activeTypeBtn === this.WORKFLOW_TYPE.Organizational
        }

        this.doSearch()        
    }

    // 点击工单类型
    onClickType(key) {
        this.activeTypeBtn = key

        this.activeNameBtn = this.workOrderSearchBtn[key].todoNameList[0].id.toString()

         
        this.showBatchApproval = key === this.WORKFLOW_TYPE.Organizational

        this.isBatchAudit = false

        this.searchData.topologyIdList = this.workOrderSearchBtn[key].id

        this.doSearch()
        

    }



    /**
     * 获取每个工单类型的count数量
     * 计算并处理数据（目前前端处理）
     */
     getFlowCount(topologyId) {
        let params = {
            topologyId: topologyId,
            acceptor: this.email,
            status: [0, 100],
            applyType: null
        }

        if (topologyId === 'ius_transfer_apply_topology') {
            params.topologyId = 'ius_entry_apply_topology';
            params.applyType = 0
        }

        if (topologyId === 'ius_entry_apply_topology') {
            params.applyType = 1
        }
        this.ajax.postByJson(AjaxUrl.bpmflow.queryFlowCount, params).then((res) => {
           const count = res.data
           
           Object.keys(this.oTypeConfig).forEach((name) => {
                if(this.oTypeConfig[name].includes(topologyId)) {
                    this.workOrderSearchBtn[name].num += count
                    this.workOrderSearchBtn.All.num += count

                    if (topologyId === 'ius_personal_special_rights_child' || topologyId === 'ius_special_rights_child') {
                        // 两种工单属于同一类型  避免重复添加
                        if (!this.isAddCount) {
                            const todoNameItem = {
                                id: ['ius_special_rights_child', 'ius_personal_special_rights_child' ],
                                name: this.topoPipeServ.transform('ius_special_rights_child'),
                                num: count
                            }
                            this.workOrderSearchBtn[name].todoNameList.push(todoNameItem)
                            this.workOrderSearchBtn.All.todoNameList.push(todoNameItem)
                            this.isAddCount = true
                        } else {
                            this.workOrderSearchBtn[name].todoNameList.forEach((item) => {
                                if (item.id.length === 2) {
                                    item.num += count 
                                }
                            })
                        }
                    } else {
                        const todoNameItem = {
                            id: [topologyId],
                            name: this.topoPipeServ.transform(topologyId),
                            num: count
                        }
                        this.workOrderSearchBtn[name].todoNameList.push(todoNameItem)
                        this.workOrderSearchBtn.All.todoNameList.push(todoNameItem)
                    }
                    this.workOrderSearchBtn[name].todoNameList[0].id.push(topologyId)
                    this.workOrderSearchBtn[name].todoNameList[0].num += count
                    this.workOrderSearchBtn.All.todoNameList[0].num += count                    
                }
            })
            this.setDealingNum(this.workOrderSearchBtn.All.num || 0);
        })
    }

     
    // 不同工单类型下的工单名称id
    oTypeConfig = {
        [this.WORKFLOW_TYPE.Authority]: ['ius_special_rights', 'ius_special_rights_child', 'ius_personal_special_rights_child', 'ius_personal_special_rights', 'ius_org_identity_permit_topology', 'ius_org_identity_permit_child_topology', 'ius_ext_system_auth_apply', 'ius_ext_system_auth_clear'],
        [this.WORKFLOW_TYPE.Organizational]: ['ius_entry_apply_topology', 'ius_org_identity_confirm_topology', 
                                             'ius_hold_post_apply_topology', 'ius_quit_apply_topology', 
                                             'nav_personnelchange_onboard', 'nav_personnelchange_departure',
                                              'nav_personnelchange_transfer', 'ius_transfer_apply_topology'],
        [this.WORKFLOW_TYPE.Document]: ['written_doc_project', 'written_doc_delete_project']
    }

    /**
     * 待办工单  search按钮的数据结构
     * @returns 
     */
    getInitSearchBtn() {
        return {
            [this.WORKFLOW_TYPE.All]: {
                name: '全部',
                id: [...this.oTypeConfig[this.WORKFLOW_TYPE.Authority], ...this.oTypeConfig[this.WORKFLOW_TYPE.Organizational], ...this.oTypeConfig[this.WORKFLOW_TYPE.Document]],
                num: 0,
                todoNameList: [
                    {
                        id: [...this.oTypeConfig[this.WORKFLOW_TYPE.Authority], ...this.oTypeConfig[this.WORKFLOW_TYPE.Organizational], ...this.oTypeConfig[this.WORKFLOW_TYPE.Document]],
                        name: `全部`,
                        num: 0
                    }
                ]
            },
            [this.WORKFLOW_TYPE.Authority]: {
                name: '权限类',
                id: this.oTypeConfig[this.WORKFLOW_TYPE.Authority],
                num: 0,
                todoNameList: [
                    {
                        id: [],
                        name: `全部`,
                        num: 0
                    }
                ]
            },
            [this.WORKFLOW_TYPE.Organizational]: {
                name: '业务组织架构类',
                id: this.oTypeConfig[this.WORKFLOW_TYPE.Organizational],
                num: 0,
                todoNameList: [
                    {
                        id: [],
                        name: `全部`,
                        num: 0
                    }
                ]
            },
            [this.WORKFLOW_TYPE.Document]: {
                name: '文档类',
                id: this.oTypeConfig[this.WORKFLOW_TYPE.Document],
                num: 0,
                todoNameList: [
                    {
                        id: [],
                        name: `全部`,
                        num: 0
                    }
                ]
            }
        }
    }

    workOrderSearchBtn = this.getInitSearchBtn()

    activeNameBtn: string = this.workOrderSearchBtn.All.todoNameList[0].id.toString() // 保存当前高亮的工单名称按钮

    flowTyleAttrKeys = Object.keys(this.workOrderSearchBtn)
    

    formatWorkOrderSearchBtn = () => {    
        this.flowNamesAllOptions.forEach((item) => {
            const {value} = item;
            this.getFlowCount(value)
        })
    }




    onBatchOpera(b: boolean) {
        // 批量审核开关
        this.isBatchAudit = b
        this.selectedCount = 0
        if (!b) {
            this.initPageOfApprovalStatus();
            this.onSelectAll(b)
        }
    }


    

    /**
     * 批量审核
     */
     onSelectAll(e) {
        const { page } = this.pagination
        this.pageOfApprovalStatus[page].withAll = typeof e !=='undefined' ? e : !this.pageOfApprovalStatus[page].withAll
        this.itemList.forEach((item, index) => {
            if(this.setCheckStatus(item)) {
                this.pageOfApprovalStatus[page].withAll ? (!item.checked && this.selectedCount++) : (this.selectedCount > 0 && this.selectedCount--)
                item.checked = this.pageOfApprovalStatus[page].withAll
                // this.pageOfApprovalStatus[page].checkedIndex[index] = index
                this.pageOfApprovalStatus[page].checkedList[index] = item.checked ? item : null
            }
        })
     }

     /**
      * 设置勾选状态
      * @param item itemList里的item
      * @returns 
      */
     setCheckStatus(item) {
        const { topologyId } = item.flowMetaData
        if (topologyId === 'ius_ext_system_auth_apply' || topologyId === 'ius_ext_system_auth_apply_rpa') {
            // 外部系统权限工单，只有部门负责人审批节点支持批量审批
            return ['30111002', '30111003', '30111004'].includes(
              item.flowMetaData.currentNodeId
            )
        }
        if (topologyId === 'ius_ext_system_auth_clear') {
            // 外部系统权限清退工单，只有系统管理员节点支持批量审批
            return item.flowMetaData.currentNodeId === '30111101'
        }
        return (topologyId ==='ius_entry_apply_topology' || topologyId === 'ius_org_identity_confirm_topology' || topologyId === 'ius_hold_post_apply_topology' || topologyId === 'ius_quit_apply_topology') && this.nodeNamePipeServ.transform(item.flowMetaData.currentNodeId) === '部门负责人审批'
     }

     getWorkPostValue(topologyId, idx) {
        const { flowNodeMessage } = this.itemList[idx] || {flowNodeMessage: {businessObject: {}}};
        const { businessObject } = flowNodeMessage || {businessObject: {}};
        const { applyedIdentityList, newIdentityList } = businessObject || {applyedIdentityList: "[]", newIdentityList: "[]"};

        if (topologyId === 'ius_hold_post_apply_topology') {
            const _applyedIdentityList = JSON.parse(applyedIdentityList || "[]");
            return _applyedIdentityList.map(item => item.postName).join('；') || '无'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            const _newIdentityList = JSON.parse(newIdentityList || "[]");
            return _newIdentityList.map(item => item.postName).join('；') || '无'
        }
        return (this.extraBussinessData[idx] || {}).workPost || '无';
    }

    getWorkDepartValue(topologyId, idx) {
        const { flowNodeMessage } = this.itemList[idx] || {flowNodeMessage: {businessObject: {}}};
        const { businessObject } = flowNodeMessage || {businessObject: {}};
        const { applyedIdentityList, newIdentityList } = businessObject || {applyedIdentityList: "[]", newIdentityList: "[]"};
        if (topologyId === 'ius_hold_post_apply_topology') {
            const _applyedIdentityList = JSON.parse(applyedIdentityList || "[]");
            return _applyedIdentityList[0] ? _applyedIdentityList[0].departmentName.replace(/ \/ /g, '-') : '无'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            const _newIdentityList = JSON.parse(newIdentityList || "[]");
            return _newIdentityList[0] ? _newIdentityList[0].departmentName.replace(/ \/ /g, '-') : '无'
        }
        return (this.extraBussinessData[idx] || {}).department || '无';
    }

    getWorkDepartLabel(topologyId) {
        if (topologyId === 'ius_hold_post_apply_topology') {
            return '兼职部门'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            return '部门（新）'
        }
        return '部门'
    }

    getWorkPostLabel(topologyId) {
        if (topologyId === 'ius_hold_post_apply_topology') {
            return '兼职岗位'
        }
        if (topologyId === 'ius_org_identity_confirm_topology') {
            return '岗位（新）'
        }
        return '岗位'
    }
    doSearch() {
        this.pagination.page = 1;
        this.isBatchAudit = false;
        this.selectedCount = 0
        this.search();
    }
    search() {
        this.router.navigate(['/workflow/dealingList', {
            ...this.searchData,
            ...this.pagination,
            _r: Date.now()
        }]);
    }

    onPageChanged(event) {
        this.pagination.page = Number(event.data.page);
        this.pagination.offset = this.pagination.size
        this.search();
    }

    onSizeChanged(event) {
        this.pagination.page = 1;
        this.pagination.size = Number(event.data.size);
        this.search();
    }

    targetShow(item) {
        item.show = !item.show
    }

    detail(item) {
        if (item.flowMetaData.topologyName === "ius_personal_special_rights") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialFlowApprove/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_special_rights") {
            const flowId = item.flowMetaData.flowId
            if (item.flowNodeMessage.nodeId === '30110101') {
                window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
            } else if (item.flowNodeMessage.nodeId === '30110102') {
                window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
            } else if (item.flowNodeMessage.nodeId === '30110103') {
                if (item.flowNodeMessage.haveChildFlow) {
                    window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
                } else {
                    window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
                }
            } else {
                if (item.flowNodeMessage.haveChildFlow) {
                    window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
                } else {
                    window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
                }
            }
        } else if (item.flowMetaData.topologyName === "ius_special_rights_child") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_entry_apply_topology") {
            const flowId = item.flowMetaData.flowId
            if (item.flowMetaData.currentNodeId === '30110303' || item.flowMetaData.currentNodeId === '9999') {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryApproveFlow/${flowId}`);  
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow/${flowId}`); 
            }
        } else if (item.flowMetaData.topologyName === "ius_quit_apply_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/quitApproveFlow/${flowId}`);
            // if (item.flowMetaData.currentNodeId === '30110503' || item.flowMetaData.currentNodeId === '9999') {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/quitApproveFlow/${flowId}`);  
            // } else {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/quitFlow/${flowId}`);  
            // }
        } else if (item.flowMetaData.topologyName === "ius_hold_post_apply_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/concurrentApproveFlow/${flowId}`);
            // if (item.flowMetaData.currentNodeId === '30110603' || item.flowMetaData.currentNodeId === '9999') {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/concurrentApproveFlow/${flowId}`);  
            // } else {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/concurrentFlow/${flowId}`);  
            // }
        } else if (item.flowMetaData.topologyName === "ius_org_identity_permit_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/identityApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_org_identity_permit_child_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/identityChildApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_org_identity_confirm_topology") {
            const flowId = item.flowMetaData.flowId
            if (item.flowMetaData.currentNodeId === '30110903' || item.flowMetaData.currentNodeId === '9999') {
                window.location.href = encodeURI(`/bflow/icacflow/#/newIdentityApproveFlow/${flowId}`);  
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/newIdentityFlow/${flowId}`);  
            }
        } else if (item.flowMetaData.topologyName === "ius_ext_system_auth_apply") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_ext_system_auth_clear") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemClearFlow/${flowId}`);
        } else {
            this.router.navigate([this.utilService.judgeRouter(item.flowMetaData.topologyName), {
                flowId: item.flowMetaData.flowId,
                topologyId: item.flowMetaData.topologyId,
                enterType: 3
            }]);
        }
    }

    // 点击处理，跳转至成文系统对应文档查看页面12
    toDeal(item: any) {
        this.router.navigate([this.utilService.judgeRouter(item.flowMetaData.topologyName), {
            flowId: item.flowMetaData.flowId,
            topologyId: item.flowMetaData.topologyId,
            enterType: 3
        }]);
    }

    getStatus(item) {
        const businessStat = item.flowNodeMessage.business_status;
        if (businessStat === '-1') {
            return '拒绝';
        } else if (businessStat === '0') {
            return '流转中';
        } else {
            return '通过';
        }
    }

    setDealingNum(num: number = 0) {
        const marqueeEle = (document.getElementsByClassName('J_main_marquee') || [])[0];
        const sideNumEle = (document.getElementsByClassName('J_main_dealing_num') || [])[0];
        if (marqueeEle) {
            marqueeEle.innerHTML = num ? `有${num}个工单待办，请及时处理！！！` : ''
        }

        if (sideNumEle) {
            sideNumEle.innerHTML = num ? `(${num})` : ''
        }
    }

     /**
      * 获取每页勾选的工单
      * @param list
      */
     getAllCheckedList(list) {
        let checkList = []
        list.forEach(ele => {
            const _ele = ele.checkedList.filter(d => d)
            checkList = [...checkList, ..._ele]
        });
        return checkList
     }
     /**
      * 单个checkbox
      * @param event {boolean} 状态
      * @param i index
      */

     handleCheckbox(event, i, item) {
        const obj = this.pageOfApprovalStatus[this.pagination.page]
        if (!event) {
            this.selectedCount--;
            obj.withAll = false
            obj.checkedList[i] = null
            return
        }
        this.selectedCount++
        obj.checkedList[i] = item
     }

    /**
     * 批量审核弹窗
     */
    onActionSelected(v: boolean, item) {
        if (item) item.checked = true
        const list = item ? [item] : this.getAllCheckedList(this.pageOfApprovalStatus)
        // 外部系统权限工单单独处理
        if (['ius_ext_system_auth_apply', 'ius_ext_system_auth_apply_rpa', 'ius_ext_system_auth_clear'].includes(this.searchData.topologyIdList.toString())) {
            if (list.length > 0) {
                this.sharkModalService.open({
                    type: 'dialog',
                    backdrop: 'static',
                    size: 'lg',
                    component: ExternalBatchApproveModal,
                    data: {
                        checkedList: list,
                        topologyId: this.searchData.topologyIdList.toString()
                    }
                }).then((res: any) => {
                    Event.dispatch('nav:refreshDealing', null)
                    const { data } = res
                    const hasError = Object.values(data).some(v => !v)

                    if (hasError) {
                        this.toastr.error('部分审核失败');
                    } else {
                        this.toastr.success(`审批成功`);
                    }
                    setTimeout(() => {
                      this.doSearch()
                      this.workOrderSearchBtn = this.getInitSearchBtn()
                      this.formatWorkOrderSearchBtn()
                    }, 2000)
                    this.initPageOfApprovalStatus()
                })
            } else {
                this.toastr.info('请选择需要进行批量审批的工单！');
            }
            return;
        }

        if (list.length > 0) {
            this.sharkModalService.open({
                type: 'dialog',
                backdrop: 'static',
                size: 'lg',
                component: ApproveModal,
                // data: { approveData: { approved: action === 'resolve' }, checkedList: this.checkedList }
                // 有传参进来代表是单词审核
                // 没有传参代表的是批量审核
                data: { approveData: { approved: typeof v === 'boolean' ? v : null }, checkedList: list }
            }).then((res: any) => {
                Event.dispatch('nav:refreshDealing', null);
                const { error } = res;

                if (!error) return this.doSearch();
                
                
                if (error.length === 0) {
                    this.toastr.success(`审批成功`);
                    setTimeout(() => {
                        this.doSearch();
                        this.workOrderSearchBtn = this.getInitSearchBtn()
                        this.formatWorkOrderSearchBtn();
                    },2000)
                } else {
                    // 如果有失败的 要再Modal展示出来的
                    this.sharkModalService.open({
                        type: 'dialog',
                        backdrop: 'static',
                        size: 'lg',
                        component: FailModal,
                        // data: { approveData: { approved: action === 'resolve' }, checkedList: this.checkedList }
                        // 有传参进来代表是单词审核
                        // 没有传参代表的是批量审核
                        data: { failDataList: error, toDetail:  this.detail}
                    }).then(() => {
                        this.doSearch();
                    })
                }
                this.initPageOfApprovalStatus()
            });
        } else {
            this.toastr.info('请选择需要进行批量审批的工单！');
        }
    }


    // 操作按钮上的通过/驳回
    onAudit(bool: boolean, item) {
        // 通过点击的是通过还是驳回显示不同的Modal内容
        // 应该能和批量操作的Modal复用
        
        this.onActionSelected(bool, item)
    }
}
