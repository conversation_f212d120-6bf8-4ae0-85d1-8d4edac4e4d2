import { formatPersonalSpecialFlowData, formatSpecialFlowData, formatSpecialChildFlowData, formatEntryFlowData, formatQuitFlowData, formatHoldPostFlowData, formatOrgIdentityFlowData, formatOrgIdentityChildFlowData, formatNewOrgIdentityFlowData, formatExternalSystemFlowData, formatExternalSystemClearFlowData } from './../../utils';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Ajax, SharkModalService, SharkToastrService } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';
import { OnAjaxError } from '../../../shared/decorators';
import { TopologyNamePipe } from '../../../shared/pipes/workflow.pipe';
import { UtilService } from '../../../shared/services/util.service';

@Component({
    templateUrl: './list.component.html',
    selector: 'bpm-newmonitorlist'
})
export class MonitorListComponent {
    itemList: any = [];
    searchData: any = {
    };
    // 分页参数
    pagination: any = {
        page: 1,
        size: 10
    };

    flowTypeOptions: any[] = [];

    flowStatusOptions = [
        {
            value: '',
            name: '全部'
        },
        {
            value: '1',
            name: '审批中'
        },
        {
            value: '2',
            name: '完结'
        },
        {
            value: '3',
            name: '驳回'
        },
        {
            value: '4',
            name: '驳回中待确认'
        },
    ]

    constructor(
        private toastr: SharkToastrService,
        private ajax: Ajax,
        private router: Router,
        private aRoute: ActivatedRoute,
        private topoPipeServ: TopologyNamePipe,
        private utilService: UtilService
    ) {
        this.initDataOption();
        this.aRoute.params.subscribe((param) => {
            this.pagination.page = this.utilService.toNumber(param.page, 1);
            this.pagination.size = this.utilService.toNumber(param.size, 10);
            this.searchData.topologyId = param.topologyId || '';
            this.searchData.createUser = param.createUser || '';
            this.getItemList();
        });
    }

    initDataOption() {
        this.flowTypeOptions = [{
            name: '请选择',
            value: ''
        }].concat(this.topoPipeServ.transform('', true));
    }

    @OnAjaxError('通知失败')
    async onTriggerNotify(item: any) {
        const nodeId = item.flowMetaData.currentNodeId;
        const flowId = item.flowMetaData.flowId;
        const status = item.flowMetaData.status;
        //
        // [{

        //     "payload": "{\"flowId\": \"3821508\",\"nodeId\": \"30010402\"}"
        // }]
        let result: any;
        if (status === 200) {
            // end
            result = await this.ajax.postByJson(AjaxUrl.mockNotifyEnd[item.flowMetaData.topologyId], [{
                payload: JSON.stringify({
                    flowId, nodeId:'9999'
                })
            }]);
        } else {
            result = await this.ajax.postByJson(AjaxUrl.mockNotify[item.flowMetaData.topologyId], [{
                payload: JSON.stringify({
                    flowId, nodeId
                })
            }]);
        }
        if (result.code === 200) {
            this.toastr.success('通知成功！');
        }
    }
    // 获取待我审批的工单列表
    getItemList() {
        const param = Object.assign({}, {
            ...this.searchData,
            ...this.pagination
        });
        this.ajax.postByJson(AjaxUrl.bpmflow.queryMonitorList, param).then((res) => {
            this.itemList = res.data.result;
            this.pagination = res.data.pagination;
            this.itemList = this.itemList.map(itemData => {
                if(itemData.flowMetaData.topologyId === "ius_personal_special_rights") {
                    formatPersonalSpecialFlowData(itemData)
                }
                if(itemData.flowMetaData.topologyId === "ius_special_rights") {
                    formatSpecialFlowData(itemData)
                }
                if(itemData.flowMetaData.topologyId === "ius_special_rights_child" || itemData.flowMetaData.topologyId === "ius_personal_special_rights_child") {
                    formatSpecialChildFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_entry_apply_topology") {
                    formatEntryFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_quit_apply_topology") {
                    formatQuitFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_hold_post_apply_topology") {
                    formatHoldPostFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_permit_topology") {
                    formatOrgIdentityFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_permit_child_topology") {
                    formatOrgIdentityChildFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_org_identity_confirm_topology") {
                    formatNewOrgIdentityFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_ext_system_auth_apply") {
                    formatExternalSystemFlowData(itemData)
                }
                if (itemData.flowMetaData.topologyId === "ius_ext_system_auth_clear") {
                    formatExternalSystemClearFlowData(itemData)
                }
                itemData.isNewFlow = this.isNewFlow(itemData.flowMetaData.topologyId)
                return itemData;
            });
        }, (err) => {
            this.toastr.error(`查询工单列表失败:${err.errorCode}`);
        });
    }

    doSearch() {
        this.pagination.page = 1;
        this.search();
    }

    search() {
        this.router.navigate(['/workflow/monitorList', {
            ...this.searchData,
            ...this.pagination,
            _r: Date.now()
        }]);
    }

    onPageChanged(event) {
        this.pagination.page = event.data.page;
        this.search();
    }

    onSizeChanged(event) {
        this.pagination.page = 1;
        this.pagination.size = event.data.size;
        this.search();
    }

    targetShow(item) {
        item.show = !item.show
    }

    detail(item) {
        if (item.flowMetaData.topologyName === "ius_personal_special_rights") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialFlowApprove/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_special_rights") {
            const flowId = item.flowMetaData.flowId
            console.log(item)
            if (item.flowNodeMessage.nodeId === '30110101') {
                window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
            } else if (item.flowNodeMessage.nodeId === '30110102') {
                window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
            } else if (item.flowNodeMessage.nodeId === '30110103') {
                if (item.flowNodeMessage.haveChildFlow) {
                    window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
                } else {
                    window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
                }
            } else {
                if (item.flowNodeMessage.haveChildFlow) {
                    window.location.href = encodeURI(`/bflow/icacflow/#/specialFlowApprove/${flowId}`);
                } else {
                    window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
                }
            }
        } else if (item.flowMetaData.topologyName === "ius_special_rights_child") {
            const flowId = item.flowMetaData.flowId
            console.log(item)
            window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialPermOpen/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_entry_apply_topology") {
            const flowId = item.flowMetaData.flowId
            if (item.flowMetaData.currentNodeId === '30110303' || item.flowMetaData.currentNodeId === '9999') {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryApproveFlow/${flowId}`);  
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow/${flowId}`);  
            }
        } else if (item.flowMetaData.topologyName === "ius_quit_apply_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/quitApproveFlow/${flowId}`);
            // if (item.flowMetaData.currentNodeId === '30110503' || item.flowMetaData.currentNodeId === '9999') {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/quitApproveFlow/${flowId}`);  
            // } else {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/quitFlow/${flowId}`);  
            // }
        } else if (item.flowMetaData.topologyName === "ius_hold_post_apply_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/concurrentApproveFlow/${flowId}`);
            // if (item.flowMetaData.currentNodeId === '30110603' || item.flowMetaData.currentNodeId === '9999') {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/concurrentApproveFlow/${flowId}`);  
            // } else {
            //     window.location.href = encodeURI(`/bflow/icacflow/#/concurrentFlow/${flowId}`);  
            // }
        } else if (item.flowMetaData.topologyName === "ius_org_identity_permit_topology") {
            const flowId = item.flowMetaData.flowId
            window.location.href = encodeURI(`/bflow/icacflow/#/identityApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_org_identity_permit_child_topology") {
            const flowId = item.flowMetaData.flowId
            console.log(item)
            window.location.href = encodeURI(`/bflow/icacflow/#/identityChildApproveFlow/${flowId}`);
        } else if (item.flowMetaData.topologyName === "ius_org_identity_confirm_topology") {
            const flowId = item.flowMetaData.flowId
            if (item.flowMetaData.currentNodeId === '30110903' || item.flowMetaData.currentNodeId === '9999') {
                window.location.href = encodeURI(`/bflow/icacflow/#/newIdentityApproveFlow/${flowId}`);  
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/newIdentityFlow/${flowId}`);  
            }
        } else if (item.flowMetaData.topologyName === 'ius_ext_system_auth_apply') {
            window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemApproveFlow/${item.flowMetaData.flowId}`);
        } else if (item.flowMetaData.topologyName === 'ius_ext_system_auth_clear') {
            window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemClearFlow/${item.flowMetaData.flowId}`);
        } else {
            this.router.navigate([this.utilService.judgeRouter(item.flowMetaData.topologyName), {
                flowId: item.flowMetaData.flowId,
                topologyId: item.flowMetaData.topologyId,
                enterType: 3
            }]);
        }
    }

    getStatus(item) {
        const businessStat = item.flowNodeMessage.business_status;
        if (businessStat === '-1') {
            return '拒绝';
        } else if (businessStat === '0') {
            return '流转中';
        } else {
            return '通过';
        }
    }

    isNewFlow(topologyName: string) {
        return [
            'ius_special_rights',
            'ius_special_rights_child',
            'ius_personal_special_rights',
            'ius_org_identity_confirm_topology',
            'ius_entry_apply_topology',
            'ius_org_identity_permit_topology',
            'ius_org_identity_permit_child_topology',
            'ius_hold_post_apply_topology',
            'ius_quit_apply_topology',
            'ius_ext_system_auth_apply',
            'ius_ext_system_auth_clear',
        ].includes(topologyName)
    }
}
