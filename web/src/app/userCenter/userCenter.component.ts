import { PermDetailModal } from './modal/permDetailModal/permDetail.modal';
import { Component, OnInit, ViewChild } from '@angular/core';
import { SharkToastrService, SharkModalService, SharkLoadingService } from '@shark/shark-angularX';
import { UserCenterService } from './services/userCenter.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { UserService } from '../shared/services/user.service';
import {Ajax} from '@shark/shark-angularX'
import { flowServerHost } from './../config'
import { UserInfoModal } from './modal/userInfoModal/userInfo.modal';
@Component({
    templateUrl: './userCenter.component.html',
    styleUrls: ['./userCenter.component.scss']
})
export class UserCenterComponent implements OnInit {
    @ViewChild('form') form;
    uid: string;
    permTree: any;
    userInfo: any;
    activeTab: number = 1;
    // authorityVisible: boolean = false; // 控制权限弹窗的显示与隐藏
    tab1DataInfo = {
        tab1Data: [],
        isSpin: true,
        length: 0
    } // 传到权限弹窗tab1的数据
    tab2DataInfo = {
        tab2Data: [],
        isSpin: true,
        length: 0
    }; // 传到权限弹窗tab2的数据
    tab3DataInfo = {
        tab3Data: [],
        isSpin: true,
        length: 0
    }; // 传到权限弹窗tab3的数据
    staffModelInfo = {
        allAuthority: this.tab1DataInfo.length,
        orgAuthority: this.tab2DataInfo.length,
        perAuthority: this.tab3DataInfo.length
    }; // 员工信息
    tabKey: number = 0 // 设置权限弹窗打开后 当前激活tab面板的key
    userType = [
        { name: '全部', value: -1 },
        { name: '正式员工', value: 0 },
        { name: '外包', value: 2 },
        { name: '派遣', value: 3 },
        { name: '实习-签三方', value: 4 },
        { name: '实习-未签三方', value: 5 }
    ];
    staffInfo = {
        listData: {
            table: []
        },
        isSpin: false,
        type: 'add'
    }
    roleInfo = {
        roleTab: {}
    }
    rstFromListAll: any[] = []; // 全部权限
    rstFromListPermIdByRoleId: any[] = []; // 角色拥有的权限
    permsForRole: any[] = []; // 全部权限所包含的角色权限
    realTreeData: any[] = []; // 最终数据
    loading: any
    constructor(
        private userServ: UserService,
        private userCenterService: UserCenterService,
        private sharkModalService: SharkModalService,
        private sharkToastrService: SharkToastrService,
        private loadingService: SharkLoadingService,
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private location: Location,
        private ajax: Ajax,
    ) {
        this.permTree = {
            model: [],
            expandAll: false,
            data: []
        };
        this.userInfo = {
            mobile: '',
            yxId: '',
            wxId: '',
            orgs: []
        };
    }

    async ngOnInit() {
        this.loading = this.loadingService.show();
        this.getUserPerm();
        await this.userServ.getUser().then((userInfo) => {
            this.uid = userInfo.email;
        });
        this.getUserInfo();
        this.activatedRoute.queryParams.subscribe((res) => {
            if (res.type) {
                this.activeTab = parseInt(res.type);
            }
        })

        this.getUserAllPerm()
    }

    async getUserPerm() {
        const userPerms = await this.userCenterService.getUserPerm();
        const result = [];
        userPerms.map((item) => {
            result.push({
                id: item.productCode,
                name: item.productName,
                pid: ''
            });
            item.roleBaseVOList.map((tmp) => {
                result.push({
                    id: item.productCode + tmp.roleId,
                    name: tmp.name,
                    pid: item.productCode
                });
            });
        });
        this.permTree.data = result;
    }

    async getUserInfo() {
        const result = await this.userCenterService.getDetailUserInfo(this.uid);
        this.userInfo = result.data;
        const data = result.data;
        let table: any[] = [];
            if (data.orgPosAndStations && data.orgPosAndStations.length > 0) {
                data.orgPosAndStations.forEach((element: any, index: any) => {
                    if (element.stationDTOS && element.stationDTOS.length > 0) {
                        const arr = element.stationDTOS.map((item: any, i: any) => {
                            return {
                                key: `${index}${i}`,
                                type: data.staffStatus,
                                part: element.orgPosDTO.orgPosName,
                                job: item.name
                            };
                        });
                        table = table.concat(arr);
                    } else {
                        const arr = {
                            key: `${index}`,
                            type: data.staffStatus,
                            part: element.orgPosDTO.orgPosFullName,
                            job: ''
                        };
                        table.push(arr);
                    }
                });
            } else {
                table = [];
            }

            data.table = table;
            this.staffInfo = { listData: data, isSpin: false, type: 'add' }
    }

    async getUserAllPerm() {
        const result = await this.userCenterService.getDetailUserInfo(this.uid);
        const paramsData = {
            product: result.data.userName,
            uid: result.data.uid
        };
        const params = {
            orgPosId: -1,
            stationId: -1,
            staffState: -1,
            keyword: this.uid,
            level: -1,
            uid: this.uid,
            page: 1,
            size: 10
        }
        const fuzzOrgPosData = await this.ajax.get(
            `/bflow-base${flowServerHost}/icac/user/uniteOrg/allTeam/fuzzyOrgPosAndStationUsers.json`,
            params
        )
        const userOrgPosdata = fuzzOrgPosData && fuzzOrgPosData.data && fuzzOrgPosData.data.result || []
        const record = {
            name: userOrgPosdata.userName,
            email: userOrgPosdata.uid,
            type: userOrgPosdata.staffStatus,
        }
        // 先获取当前点击tab的数据
        this.getTab1Data(paramsData, record);
        // 获取其他两个tab的数据
        this.getTab2Data(paramsData, record);
        this.getTab3Data(paramsData, record);
    }

    getTab1Data = (params: any, record: any) => {
        this.ajax.get(`/bflow-base${flowServerHost}/icac/groupIdentity/getUserAllProductRoleInfos.json`, params).then(res => {
            const data: any = [];
            if (res && res.code === 200 && res.data) {
                res.data.forEach((ele: any, index: any) => {
                    if (ele.roleBaseDTOS && ele.roleBaseDTOS.length > 0) {
                        ele.roleBaseDTOS.forEach((item: any, i: any) => {
                            data.push({
                                key: `${index}${i}`,
                                system: ele.productName,
                                role: item.name,
                                date: item.expireTime ? item.expireTime : null,
                                roleId: item.roleId,
                                productCode: ele.productCode,
                                tabKey: 1
                            });
                        });
                    } else {
                        data.push({
                            key: `${index}`,
                            system: ele.productName,
                            role: '',
                            roleId: '',
                            productCode: '',
                            tabKey: 1
                        });
                    }
                });
                const d = {
                    ...record,
                    allAuthority: this.staffModelInfo.allAuthority,
                    orgAuthority: this.staffModelInfo.orgAuthority,
                    perAuthority: this.staffModelInfo.perAuthority
                };
                d.allAuthority = res.data.length;
                this.staffModelInfo = d
                this.tab1DataInfo = {
                    tab1Data: data,
                    isSpin: false,
                    length: res.data.length
                }
            } else {
                this.tab1DataInfo = { tab1Data: [], isSpin: false, length: 0 };
            }
            this.loading.finished();
        }).then(e => {
            this.loading.finished();
        })
    }

    // 获取员工权限弹窗 -- 来自组织身份的权限数据
    getTab2Data = (params: any, record: any) => {
        this.ajax.get(`/bflow-base${flowServerHost}/icac/groupIdentity/getUserGroupIdentityProductRoleInfos.json`, params).then(res => {
            const data: any = [];
            if (res && res.code === 200 && res.data) {
                res.data.forEach((ele: any, index: any) => {
                    ele.groupIdentityDTO.forEach((item: any, i: any) => {
                        data.push({
                            key: `${index}${i}`,
                            system: ele.productName,
                            role: item.roleBaseDTO.name,
                            roleId: item.roleBaseDTO.roleId,
                            date: item.roleBaseDTO.expireTime
                                ? item.roleBaseDTO.expireTime
                                : null,
                            staffType: item.groupIdentityDTO.staffStatus,
                            department: item.groupIdentityDTO.orgPosDesc
                                ? item.groupIdentityDTO.orgPosDesc.name
                                : '全部',
                            postName: item.groupIdentityDTO.station
                                ? item.groupIdentityDTO.station.name
                                : '全部',
                            productCode: ele.productCode,
                            tabKey: 2
                        });
                    });
                });
                const d = {
                    ...record,
                    allAuthority: this.staffModelInfo.allAuthority,
                    orgAuthority: this.staffModelInfo.orgAuthority,
                    perAuthority: this.staffModelInfo.perAuthority
                };
                d.orgAuthority = res.data.length;
                this.staffModelInfo = d;
                this.tab2DataInfo = { 
                    tab2Data: data,
                    isSpin: false,
                    length: res.data.length
                }
            } else {
                this.tab2DataInfo = { tab2Data: [], isSpin: false, length: 0 };
            }
        })
    }

    // 获取员工权限弹窗 -- 个人特殊权限数据
    getTab3Data = (params: any, record: any) => {
        this.ajax.get(`/bflow-base${flowServerHost}/icac/groupIdentity/getUserSpecialProductRoleInfos.json`, params).then(res => {
            const data: any = [];
            if (res && res.code === 200 && res.data) {
                res.data.forEach((ele: any, index: any) => {
                    if (ele.roleBaseDTOS && ele.roleBaseDTOS.length > 0) {
                        ele.roleBaseDTOS.forEach((item: any, i: any) => {
                            data.push({
                                key: `${index}${i}`,
                                system: ele.productName,
                                role: item.name,
                                date: item.expireTime ? item.expireTime : null,
                                roleId: item.roleId,
                                productCode: ele.productCode,
                                tabKey: 3
                            });
                        });
                    } else {
                        data.push({
                            key: `${index}`,
                            system: ele.productName,
                            role: '',
                            roleId: '',
                            productCode: '',
                            tabKey: 3
                        });
                    }
                });
                const d = {
                    ...record,
                    allAuthority: this.staffModelInfo.allAuthority,
                    orgAuthority: this.staffModelInfo.orgAuthority,
                    perAuthority: this.staffModelInfo.perAuthority
                };
                d.perAuthority = res.data.length;
                this.staffModelInfo = d;
                this.tab3DataInfo = { 
                    tab3Data: data,
                    isSpin: false,
                    length: res.data.length
                }
            } else {
                this.tab3DataInfo = { tab3Data: [], isSpin: false, length: 0 };
            }
        })
    }

    getTypeName = (options: any) => {
        const obj: any = {};
        options.forEach((ele: { value: string | number; name: any }) => {
            obj[ele.value] = ele.name;
        });
        return obj;
    };

    // 修改用户信息
    async modifyUserInfo() {
        this.form.submit(async () => {
            try {
                const result = await this.userCenterService.modifyUserInfo(
                    this.userInfo.mobile,
                    this.userInfo.yxId,
                    this.userInfo.wxId
                );
                this.sharkToastrService.success('修改用户信息成功!');
            } catch (e) {
                this.sharkToastrService.error('修改用户用户信息!');
            }
        });
    }

    openUserInfoModal() {
        this.ajax.get(`/bflow-base${flowServerHost}/icac/user/inner/getDetailUserInfo.json`, {uid: this.uid}).then(res => {
            const data = res.data;
            let table: any[] = [];
            if (data.orgPosAndStations && data.orgPosAndStations.length > 0) {
                data.orgPosAndStations.forEach((element: any, index: any) => {
                    if (element.stationDTOS && element.stationDTOS.length > 0) {
                        const arr = element.stationDTOS.map((item: any, i: any) => {
                            return {
                                key: `${index}${i}`,
                                type: data.staffStatus,
                                part: element.orgPosDTO.orgPosName,
                                job: item.name
                            };
                        });
                        table = table.concat(arr);
                    } else {
                        const arr = {
                            key: `${index}`,
                            type: data.staffStatus,
                            part: element.orgPosDTO.orgPosFullName,
                            job: ''
                        };
                        table.push(arr);
                    }
                });
            } else {
                table = [];
            }

            data.table = table;
            this.staffInfo = { listData: data, isSpin: false, type: 'add' }

            this.sharkModalService.open({
                type: 'dialog',
                backdrop: 'static',
                size: 'lg',
                component: UserInfoModal,
                data: this.staffInfo
            })
        })
    }

    // 然后将permsForRole平铺的数据转换成antd支持的数据格式
    transformDataForAntd(permData: any) {
        // antd结构数据
        const treeDatas = this.getTreeDataFormPermData(permData);
        const dataMap: any = {};
        treeDatas.forEach((item: any) => {
            dataMap[item.key] = item;
        });
        // 先修正下pid
        treeDatas.forEach((item: any) => {
            item.pId = this.getActuallyPid(item.pId, dataMap, this.rstFromListAll);
        });
        // 修正完后开始,利用深搜算法，处理数据
        this.realTreeData = this.orgTreeData(treeDatas, 0);
        console.log(this.realTreeData);
    }

    // antd结构数据转换
    getTreeDataFormPermData(permData: any) {
        return permData.map((p: any) => ({
            title: p.name,
            key: p.permId,
            pId: p.parentId,
            children: []
        }));
    }

    getActuallyPid: any = (originPid: any, dataMap: any, allPerms: any) => {
        // 如果是0，那就是根节点了，直接返回
        if (originPid === 0) {
            return originPid;
        }
        // 如果map里有，也直接返回
        if (dataMap[originPid]) {
            return originPid;
        }
        // 否则再找到originPid的parentId
        const upParendId = allPerms.find((p: any) => p.permId === originPid).parentId;
        return this.getActuallyPid(upParendId, dataMap, allPerms);
    };

    // 利用深搜算法组装数据
    orgTreeData(treeData: any, id: any) {
        // 先找到顶层的一些数据
        const datas = treeData.filter((td: any) => td.pId === id);
        datas.forEach((d: any) => {
            d.children = this.orgTreeData(treeData, d.key);
        });
        return datas;
    }

    async openPermDetailModal(record, btn) {
        const loading = this.loadingService.show({
            origin: btn
        });
        const permDetailModalTitle = `系统“${record.system}”中已选角色的权限集合详情`
         // 1.获取系统的所有角色
         const roles: any[] = []; // 系统的所有角色(角色名和角色id)
         if (record.tabKey === 1) {
            // 表示从全部tab点击了查看权限按钮，遍历tab1Data
            this.tab1DataInfo.tab1Data.forEach((item: any) => {
                // console.log(item);
                if (item.system === record.system) {
                    const obj: any = {};
                    obj.name = item.role;
                    obj.id = item.roleId;
                    roles.push(obj);
                }
            });
         } else if (record.tabKey === 2) {
            // 表示从来自组织身份的权限tab点击了查看权限按钮，遍历tab2Data
            this.tab2DataInfo.tab2Data.forEach((item: any) => {
                // console.log(item);
                if (item.system === record.system) {
                    const obj: any = {};
                    obj.name = item.role;
                    obj.id = item.roleId;
                    roles.push(obj);
                }
            });
         } else {
             // 表示从个人特殊权限tab点击了查看权限按钮，遍历tab3Data
             this.tab3DataInfo.tab3Data.forEach((item: any) => {
                // console.log(item);
                if (item.system === record.system) {
                    const obj: any = {};
                    obj.name = item.role;
                    obj.id = item.roleId;
                    roles.push(obj);
                }
            });
         }
         const rolelength = roles.length; // 角色的个数
         const tabAllTit: any = `全部（${rolelength}个角色合并）`;
         this.roleInfo = {roleTab: roles}

         // 2.通过listAll.json接口 获取权限树
        await this.ajax.postByJson(`/bflow-base${flowServerHost}/${record.productCode}/perm/listAll.json`).then(res => { 
            this.rstFromListAll = res.data;
            // console.log(this.rstFromListAll);
            // this.rstFromListAll.forEach((item: any) => {
            //     console.log(item.permId);
            // });
        })

        // 3.根据角色Id 获取每个角色tab的treeData
        const roleArr: any[] = [];
        const allRoles: any = [];
        for (const role of roles) {
            await this.ajax.get(`/bflow-base${flowServerHost}/${record.productCode}/user/role/listPermIdByRoleId.json`, {roleId: role.id }).then(res => {
                this.rstFromListPermIdByRoleId = res.data;
                // console.log(this.rstFromListPermIdByRoleId);
                this.rstFromListPermIdByRoleId.forEach(id => {
                    if (allRoles.indexOf(id) === -1) {
                        allRoles.push(id);
                    }
                });
            });
            this.permsForRole = this.rstFromListAll.filter((p: any) =>
            this.rstFromListPermIdByRoleId.includes(p.permId)
            );

            this.transformDataForAntd(this.permsForRole);
            // console.log(this.realTreeData);
            const roleObj: any = {};
            roleObj.roleName = role.name;
            roleObj.treeData = this.realTreeData;
            roleArr.push(roleObj);
        }
        // console.log(roleArr);
        let allTabData: any[] = [];
        if (roleArr.length > 1) {
            // 如果长度大于1说明有多个角色
            allTabData = this.rstFromListAll.filter((p: any) => allRoles.includes(p.permId));
            // console.log(allTabData);
            this.transformDataForAntd(allTabData);
            // 动态追加数据
            roleArr.unshift({ roleName: tabAllTit, treeData: this.realTreeData });
        } else if (roleArr.length === 1) {
            allTabData = roleArr[0].treeData; // 只有一个角色，直接取角色权限树
            // console.log(allTabData);
            // 动态追加数据
            roleArr.unshift({ roleName: tabAllTit, treeData: allTabData });
        }
        // console.log(roleArr);

        // 5.将角色名和treeData 传到权限详情弹窗
        loading.finished();
        this.sharkModalService.open({
            type: 'dialog',
            backdrop: 'static',
            size: 'lg',
            component: PermDetailModal,
            data: { 
                permTreeData : roleArr,
                role: this.roleInfo,
                title: permDetailModalTitle
            }
        })
    }

    /**
     * 切换tab，让地址栏也变动
     * @author: 金炳
     * @data: 2019-01-10 13:46:02
     */
    activeChange($event) {
        this.location.go("/userCenter", "?type=" + $event)
    }
}
