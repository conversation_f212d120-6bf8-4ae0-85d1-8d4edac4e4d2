<div class="modal-header">
    <a class="modal-close" (click)="dismiss()"></a>
    <span class="modal-title">员工信息</span>
</div>
<div class="modal-body">
    <div class="text-lg margin-b-2x">基本信息</div>
    <div class="table-wrap section-block">
        <table class='table table-full text-center'>
            <tbody>
                <tr>
                    <th class="text-nowrap">
                        <span>
                            姓名：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                           {{listData.userName ? listData.userName : ' - '}}
                        </span>
                    </td>
                    <th class="text-nowrap">
                        <span>
                            手机号：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.mobile ? listData.mobile : ' - '}}
                        </span>
                    </td>
                </tr>
                <tr>
                    <th class="text-nowrap">
                        <span>
                            邮箱：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.uid ? listData.uid : ' - '}}
                        </span>
                    </td>
                    <th class="text-nowrap">
                        <span>
                            易信号：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.yxId ? listData?.yxId : ' - '}}
                        </span>
                    </td>
                </tr>
                <tr>
                    <th class="text-nowrap">
                        <span>
                            工号：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.employeeNum ? listData.employeeNum : ' - '}}
                        </span>
                    </td>
                    <th class="text-nowrap">
                        <span>
                            微信号：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.wxId ? listData.wxId : ' - '}}
                        </span>
                    </td>
                </tr>
                <tr>
                    <th class="text-nowrap">
                        <span>
                            OA部门：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.oaDept ? listData.oaDept : ' - '}}
                        </span>
                    </td>
                    <th class="text-nowrap">
                        <span>
                            OA状态：
                        </span>
                    </th>
                    <td class="text-nowrap">
                        <span>
                            {{listData.oaState ? listData?.oaState : ' - '}}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="text-lg margin-b-2x margin-t-2x">组织身份：</div>
    <div class="table-wrap section-block">
        <table class='table table-full text-center'>
            <thead>
                <tr>
                    <th class="text-center">员工类型</th>
                    <th class="text-center">部门</th>
                    <th class="text-center">岗位</th>
                </tr>
            </thead>
            <tbody *ngIf="listData.table.length === 0">
                <tr>
                    <td class="table-no-data" colspan="8">
                        <span class="no-data-tip">暂无数据</span>
                    </td>
                </tr>
            </tbody>
            <tbody *ngIf="listData.table.length > 0">
                <tr *ngFor="let item of listData.table | combine: ['type'];let index = index;">
                    <td class="text-nowrap" name="员工类型" [attr.rowspan]="item.type_rowspan" *ngIf="item.type_display">
                        {{ getTypeName(userType)[item.type] }}
                    </td>
                    <td class="text-nowrap" name="部门">
                        <span>
                            {{ item.part }}
                        </span>
                    </td>
                    <td class="text-nowrap" name="岗位">
                        <span>
                            {{ item.job }}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<div class="modal-footer text-center">
    <button class="btn btn-secondary" type="button" (click)="dismiss()">关闭</button>
</div>