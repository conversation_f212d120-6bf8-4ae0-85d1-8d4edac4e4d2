servers:
  - **************

target: /home/<USER>/deploy/yanxuan-bflow/yanxuan-bflow
before:
  - echo "[yanxuan-bflow] Deploy Start!"
  - echo `whoami`
  - mkdir -p /home/<USER>/deploy/yanxuan-bflow/yanxuan-bflow
scripts:
  - mkdir -p /home/<USER>/app/yanxuan-bflow/yanxuan-bflow/
  - rsync -av /home/<USER>/deploy/yanxuan-bflow/yanxuan-bflow/* /home/<USER>/app/yanxuan-bflow/yanxuan-bflow/
  - pm2 delete yanxuan-bflow || echo "yanxuan-bflow is not exist"
  - cd /home/<USER>/app/yanxuan-bflow/yanxuan-bflow/
  - NODE_ENV=production:online pm2 start /home/<USER>/app/yanxuan-bflow/yanxuan-bflow/src/index.js -n yanxuan-bflow -i max
  - sleep 10
