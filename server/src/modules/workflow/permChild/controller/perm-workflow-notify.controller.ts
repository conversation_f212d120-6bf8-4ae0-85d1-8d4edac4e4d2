import { MpsPayload } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { isAuthedThirdLevelOrg } from '../../../service/permcenter/approvePerm';
import ParentWorkflow from '../../perm/perm-workflow';
import WorkflowQuery from '../../workflow-query';
import { topologyName } from '../perm-config';
import Workflow from '../perm-workflow';
import { addOrgPosUserByType } from '../perm.service';

export interface Notify {
    payload: string;
}
/**
 * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
 */
export type NotifyBody = Notify[];

export type NotifyResult = AjaxResult<string>;

const system = { email: 'system', fullname: '系统' }; // 自动审批
/**
 * 工单通知接口
 */
@RestController
@RequestMapping(`/workflow/${topologyName}`)
export class PermChildWorkflowNotifyController {

    constructor(
        private workflow: Workflow,
        private parentWorkflow: ParentWorkflow,
        private query: WorkflowQuery
    ) { }

    /**
     * 流程流转通知
     * [{"payload":"{\"flowId\": \"4668724\",\"nodeId\":\"30019902\"}"}]
     */
    @PostMapping('/notify.json')
    async notify(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body;
        for (const message of messages) {
            const payload: MpsPayload = JSON.parse(message.payload);
            const { flowMetaData, flowNodeMessage } = await this.workflow.getFlowDataTriggerByMps(payload);

            // 如果子工单是三级部门负责人审批节点，自动通过
            if (flowMetaData.currentNodeId === '30010513') {
                await this.workflow.submit({
                    fullname: '三级部门负责人审批',
                    email: 'system'
                }, flowMetaData, {
                        // 获取不到创建时候的审批结果
                        operateRemark: '审批通过',
                        approved: true
                    });
            } else if (flowMetaData.currentNodeId === '30010514' && !flowNodeMessage.underThirdLevel) {
                // 如果子工单由于涉敏到了二级部门负责人审批节点，需要判断下如果是在二级组织架构中的，在父工单已经过二级部门负责人审批，这里自动通过
                await this.workflow.submit({
                    fullname: '二级部门负责人审批',
                    email: 'system'
                }, flowMetaData, {
                        // 获取不到创建时候的审批结果
                        operateRemark: '审批通过',
                        approved: true
                    });
            } else if (flowMetaData.currentNodeId === '30010518') { // 如果是拒绝，自动到结束节点
                await this.workflow.submit({
                    ...system
                }, flowMetaData, {
                        operateRemark: '拒绝',
                        approved: false
                    });
            } else {
                // 如果是拒绝，自动通过下一个节点
                // 更新工单数据
                await this.workflow.update(flowMetaData, flowNodeMessage, system);

                const createrKeywords = '您的工单状态发生变更';

                await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, createrKeywords);

                const keywords = '业务系统权限申请';
                // 否则，通知审批人
                await this.workflow.notifyApprover(flowMetaData, flowNodeMessage, keywords, {
                    html: 'bflow_workflow_perm_notify_html',
                    text: 'bflow_workflow_perm_notify_text'
                });
            }

        }
        return ctx.body = AjaxResult.success();

    }

    /**
     * 流程结束通知
     * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
     */
    @PostMapping('/notifyEnd.json')
    async notifyEnd(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body[0];
        const payload: MpsPayload = JSON.parse(messages.payload);
        let flowMetaData: any = null;
        let flowNodeMessage: any = null;
        try {
            const flowData = await this.workflow.getFlowDataTriggerByMps(payload);
            flowMetaData = flowData.flowMetaData;
            flowNodeMessage = flowData.flowNodeMessage;
            // 父工单订阅了子工单的notifyEnd.do通知，也会进入这个流程，如果是父工单return false，后续可以看下是否需要订阅
            if (flowMetaData.topologyId === 'nav_personnelchange_perm') {
                return false;
            }
            // 更新业务状态
            flowNodeMessage.businessStatus = 1;
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
        } catch (e) {
            console.error('结束节点数据更新失败', e);
            return ctx.body = AjaxResult.internal('工单更新失败');
        }

        const keywords = '审批结束';
        await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, keywords);
        // 通知跟踪人
        // await this.workflow.notifyTrailer(flowMetaData, flowNodeMessage, '工单已经结束');
        // 结束之后需要更新父工单信息
        try {
            const parentFlowData = await this.query.detailQuery(flowNodeMessage.parentFlowId, '', flowNodeMessage.createUser);
            const rst = await Promise.all(parentFlowData.flowNodeMessage.subList.map((element: any) => {
                return this.query.detailQuery(element, '', flowNodeMessage.createUser);
            }));
            const isClosed = rst.every((element: any) => {
                return element.flowNodeMessage.businessStatus === 1;
            });
            if (isClosed) {
                await this.parentWorkflow.submit({
                    ...system
                }, parentFlowData.flowMetaData, {
                        ...parentFlowData.flowNodeMessage,
                        operateRemark: '审批结束',
                        approved: true
                    });
            }
            ctx.body = AjaxResult.success('');
        } catch (e) {
            console.error(`查询工单详情失败parentId2:`, flowNodeMessage.parentFlowId, flowNodeMessage.parentFlowId, e);
            ctx.body = AjaxResult.internal('查询工单详情失败');
        }
        ctx.body = AjaxResult.success();
    }

}
