import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkBaseModal, SharkModalParams, SharkToastrService } from '@shark/shark-angularX';

interface IlistDataProps {
    userName: string;
    mobile: string;
    uid: string;
    yxId: string;
    employeeNum: string;
    wxId: string;
    oaDept: string;
    oaState: string;
    table: any;
}

@Component({
    templateUrl: './userInfo.modal.html',
    styleUrls: ['./userInfo.modal.scss']
})
export class UserInfoModal extends SharkBaseModal {
    userType = [
        { name: '全部', value: -1 },
        { name: '正式员工', value: 0 },
        { name: '外包', value: 2 },
        { name: '派遣', value: 3 },
        { name: '实习-签三方', value: 4 },
        { name: '实习-未签三方', value: 5 }
    ];
    listData: IlistDataProps;
    constructor(
        private params: SharkModalParams,
    ) {
        super();
        this.listData = this.params.data.listData
    }

    getTypeName = (options: any) => {
        const obj: any = {};
        options.forEach((ele: { value: string | number; name: any }) => {
            obj[ele.value] = ele.name;
        });
        return obj;
    };
}
