import { Service } from '@tiger/boot';
import { AppConfig } from '@tiger/core';

export const topologyName = 'nav_personnelchange_perm';

/**
 * 当前流程的配置文件
 */
@Service
export default class Config {
    /**
     * 应用名称
     */
    appName: string = AppConfig.appName;
    /**
     * 流程拓扑ID
     */
    topologyName: string = topologyName;
    /**
     * 邮件链接审批的节点
     * 二级部门负责人审批，三级部门负责人审批
     */
    // linkApproveNodeIds: string[] = ['30010304', '30010302', '30010303'];
    linkApproveNodeIds: string[] = ['30010503', '30010504', '30010505', '30010506'];
    /**
     * 链接审批加密密码
     */
    linkApprovePassword: string = 'approveemailpassword';

    topologyDescription: string = '业务系统权限申请';
}
