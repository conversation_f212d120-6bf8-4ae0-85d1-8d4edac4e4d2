{"code": 200, "data": [{"id": "59704c45314569cb7097615c", "name": "商品", "showIndex": 1, "icon": "app-icon-2", "background": "app-block-2", "applications": [{"id": "595ca4cae5675164036720e8", "name": "严选商品中心", "productId": "2001", "productCode": "ic", "productName": "严选商品中心", "link": "/yxicnew", "tagid": "59704c45314569cb7097615c", "showIndex": 5, "description": "请填写详细的申请理由", "sensitiveLevel": 1, "businessDepartment": "964", "cmdbProductCode": "ic", "applicable": true, "tag": {"id": "59704c45314569cb7097615c", "name": "商品", "icon": "app-icon-2", "background": "app-block-2", "showIndex": 1, "applications": []}}, {"id": "5ac42cce63208518287efabd", "name": "大麦-商品数据运营平台", "productId": "4341", "productCode": "damai", "productName": "大麦-商品数据运营平台", "link": "/damai ", "tagid": "59704c45314569cb7097615c", "showIndex": 10, "description": "产品经理：毛一凡 hzma<PERSON><PERSON><EMAIL>", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615c", "name": "商品", "icon": "app-icon-2", "background": "app-block-2", "showIndex": 1, "applications": []}}, {"id": "595cc1ad27e47c47e13e0b0b", "name": "哈勃-外域数据应用", "productId": "4431", "productCode": "hubble", "productName": "哈勃-外域数据应用", "link": "/hubble/", "tagid": "59704c45314569cb7097615c", "showIndex": 50, "description": "产品经理：赵华翔 <EMAIL>\n哈勃  http://yx.mail.netease.com/hubble/", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615c", "name": "商品", "icon": "app-icon-2", "background": "app-block-2", "showIndex": 1, "applications": []}}]}, {"id": "59704c45314569cb7097615b", "name": "营销", "showIndex": 10, "icon": "app-icon-3", "background": "app-block-3", "applications": [{"id": "59acafd47b8aae6add45fc66", "name": "运营工作台", "productId": "2051", "productCode": "ops", "productName": "严选运营工作台系统", "link": "/ops/index.html ", "tagid": "59704c45314569cb7097615b", "showIndex": 0, "description": "负责人：陈辉", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5cc7ffca22de0876da366005", "name": "营销中心项目审批平台", "productId": "6911", "productCode": "market", "productName": "市场工作台", "link": "/market", "tagid": "59704c45314569cb7097615b", "showIndex": 1, "description": "营销中心项目审批平台", "icon": "", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5d2f07f6f30edd684e8b60f0", "name": "广告投放系统", "productId": "6081", "productCode": "dsp", "productName": "严选DSP", "link": "/dsp", "tagid": "59704c45314569cb7097615b", "showIndex": 5, "description": "广告投放系统", "icon": "", "sensitiveLevel": 0, "businessDepartment": "", "cmdbProductCode": "", "applicable": false, "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "595ce7e1258ea25e0d63141b", "name": "严选CRM系统", "productId": "5071", "productCode": "yanxuan_crm", "productName": "严选CRM系统", "link": "http://yxcrmweb.mail.netease.com/", "tagid": "59704c45314569cb7097615b", "showIndex": 10, "description": "负责人：马超", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5b5fd229be28a035513ca8cc", "name": "企业CRM系统", "productId": "6211", "productCode": "tob-crm", "productName": "企业CRM系统", "link": "/tob-crm", "tagid": "59704c45314569cb7097615b", "showIndex": 11, "description": "负责人：王尧", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "595c9fa657a0c263e8a5aa89", "name": "严选分销系统", "productId": "2171", "productCode": "distribution-admin", "productName": "分销管理系统", "link": "/distribution", "tagid": "59704c45314569cb7097615b", "showIndex": 100, "description": "负责人：李宪亮", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "595ce81ee9a6035e105803f2", "name": "企业购后台", "productId": "2041", "productCode": "yxtob", "productName": "企业购系统", "link": "/yxtob", "tagid": "59704c45314569cb7097615b", "showIndex": 105, "description": "负责人：王尧", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5a45e7ae8a47b96448852206", "name": "严选优采系统", "productId": "3401", "productCode": "ubuy", "productName": "企业优采系统", "link": "/ubuy", "tagid": "59704c45314569cb7097615b", "showIndex": 110, "description": "王尧", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5bbf2f6a1e10162ed9993741", "name": "严选优采海外版", "productId": "6561", "productCode": "globuy", "productName": "企业优采海外版", "link": "/globuy", "tagid": "59704c45314569cb7097615b", "showIndex": 111, "description": "严选优采海外版", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5af2602db9eb22463d4c2a8a", "name": "严选门店管理系统", "productId": "5961", "productCode": "yanxuan-store", "productName": "线下园区店管理系统", "link": "/yanxuan-store-manage", "tagid": "59704c45314569cb7097615b", "showIndex": 120, "description": "严选线下店运营后台系统测试", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5af2607d321eef4653bd1759", "name": "严选旺铺收银系统", "productId": "5961", "productCode": "yanxuan-store", "productName": "线下园区店管理系统", "link": "/yanxuan-store-reception", "tagid": "59704c45314569cb7097615b", "showIndex": 122, "description": "严选线下店运收银台系统测试", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5ab84d46f587795ce1c49797", "name": "网易推手", "productId": "5011", "productCode": "dealer", "productName": "网易推手", "link": "/dealer/", "tagid": "59704c45314569cb7097615b", "showIndex": 152, "description": "负责人：李宪亮", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "595ce7b0e9ad595e23cd125b", "name": "主站管理后台", "productId": "5281", "productCode": "yanxuan-admin", "productName": "网易严选管理后台", "link": "http://yxadmin.mail.netease.com/", "tagid": "59704c45314569cb7097615b", "showIndex": 180, "description": "负责人：马超", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5afc11f3fa20a4465834b31e", "name": "严选素材管理系统", "productId": "6091", "productCode": "fodder", "productName": "网易严选素材库", "link": "/fodder/index.html", "tagid": "59704c45314569cb7097615b", "showIndex": 185, "description": "董鑫功、蒋文博", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5a53135cfde06364590b4e4f", "name": "神相-用户行为分析平台", "productId": "4221", "productCode": "dp-she<PERSON><PERSON>g", "productName": "神相-用户行为分析平台", "link": "/shenxiang", "tagid": "59704c45314569cb7097615b", "showIndex": 200, "description": "产品经理：赵华翔 <EMAIL>", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5ad9891d321eef4653bd1758", "name": "刑天-推广渠道管理系统", "productId": "4241", "productCode": "<PERSON><PERSON><PERSON>", "productName": "刑天-推广渠道管理系统", "link": "/xingtian", "tagid": "59704c45314569cb7097615b", "showIndex": 201, "description": "张继萍", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}, {"id": "5a28fedc8a47b96448852205", "name": "神农-AB测试平台", "productId": "5021", "productCode": "abtest", "productName": "AB测试系统", "link": "/yxpdc/abtest-view/index.html", "tagid": "59704c45314569cb7097615b", "showIndex": 210, "description": "负责人：陈辉", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615b", "name": "营销", "icon": "app-icon-3", "background": "app-block-3", "showIndex": 10, "applications": []}}]}, {"id": "59704c45314569cb70976159", "name": "供应链", "showIndex": 20, "icon": "app-icon-0", "background": "app-block-0", "applications": [{"id": "5a0a8a659e364664434b00ad", "name": "严选供应商协同系统", "productId": "2131", "productCode": "yxsupplier", "productName": "严选供应商协同系统", "link": "/yxsupplier", "tagid": "59704c45314569cb70976159", "showIndex": 0, "description": "请输入申请的详细理由。\n注：涉及成本、供应商数据，权限仅可授权给正式员工", "sensitiveLevel": 2, "businessDepartment": "854", "cmdbProductCode": "yxsupplier", "applicable": true, "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "5b30613eb9eb22463d4c2a8b", "name": "采购工作台", "productId": "1001", "productCode": "pms", "productName": "采购系统（旧）", "link": "/pmc/", "tagid": "59704c45314569cb70976159", "showIndex": 1, "description": "请输入申请的详细理由。\n注：涉及成本、供应商数据，权限仅可授权给正式员工", "sensitiveLevel": 2, "businessDepartment": "854", "cmdbProductCode": "pmc", "applicable": true, "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "595ba4e40e22890677ebe45a", "name": "严选采购系统（旧）", "productId": "1001", "productCode": "pms", "productName": "采购系统（旧）", "link": "/pms", "tagid": "59704c45314569cb70976159", "showIndex": 2, "description": "请输入申请的详细理由。\n注：涉及成本、供应商数据，权限仅可授权给正式员工", "sensitiveLevel": 2, "businessDepartment": "854", "cmdbProductCode": "pms", "applicable": true, "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "59ed9684fde06364590b4e4e", "name": "仓配工作台", "productId": "3601", "productCode": "ark", "productName": "严选仓配工作台", "link": "/ark", "tagid": "59704c45314569cb70976159", "showIndex": 8, "description": "负责人：邱晟", "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "5ab89bdaa8b3295ce62adb20", "name": "库存中控台", "productId": "5861", "productCode": "kingcobra", "productName": "库存中控台", "link": "/kingcobra/", "tagid": "59704c45314569cb70976159", "showIndex": 10, "description": "负责人：张练", "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "5dfb45e535611a001d9ed7e6", "name": "司南库存优化系统", "productId": "3611", "productCode": "smartipc-thanos", "productName": "SmartIPC仓间调拨产品", "link": "/compass/", "tagid": "59704c45314569cb70976159", "showIndex": 11, "description": "司南库存优化系统", "icon": "", "sensitiveLevel": 2, "businessDepartment": "618", "cmdbProductCode": "smartipc-thanos", "applicable": false, "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "597b399e2a2d4b4a5aaba7cb", "name": "严选销退系统", "productId": "5621", "productCode": "yanxuan-sales-return", "productName": "严选销退系统", "link": "https://salesreturn.mail.netease.com/", "tagid": "59704c45314569cb70976159", "showIndex": 20, "description": "负责人：王梓瑞", "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "5a5dec798a47b96448852208", "name": "品控工作台", "productId": "5471", "productCode": "qc", "productName": "品控工作台", "link": "/qc", "tagid": "59704c45314569cb70976159", "showIndex": 30, "description": "请输入申请的详细理由。\n注：涉及成本、供应商数据，权限仅可授权给正式员工", "sensitiveLevel": 2, "businessDepartment": "854", "cmdbProductCode": "qc", "applicable": true, "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}, {"id": "5a56d4278a47b96448852207", "name": "河洛-供应链协同决策系统", "productId": "5151", "productCode": "smartipc", "productName": "严选供应链调度系统", "link": "/heluo", "tagid": "59704c45314569cb70976159", "showIndex": 200, "description": "请输入申请的详细理由", "icon": "app-icon-1", "sensitiveLevel": 2, "businessDepartment": "854", "cmdbProductCode": "scps", "applicable": true, "tag": {"id": "59704c45314569cb70976159", "name": "供应链", "icon": "app-icon-0", "background": "app-block-0", "showIndex": 20, "applications": []}}]}, {"id": "59704c45314569cb7097615d", "name": "支持", "showIndex": 50, "icon": "app-icon-4", "background": "app-block-4", "applications": [{"id": "5d5a06368d88296854dc5ed9", "name": "客服工作台", "productId": "7071", "productCode": "kefu-workbench", "productName": "客服工作台", "link": "/kf", "tagid": "59704c45314569cb7097615d", "showIndex": 0, "description": "新版智能客服工作台", "icon": "", "sensitiveLevel": 2, "businessDepartment": "618", "cmdbProductCode": "kefu-workbench", "applicable": true, "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "595ce7258ca28d5e286aa09b", "name": "严选客服系统", "productId": "5421", "productCode": "yanxuan-kefu", "productName": "严选客服系统", "link": "/yxkf", "tagid": "59704c45314569cb7097615d", "showIndex": 1, "description": "请输入申请的详细理由", "sensitiveLevel": 2, "businessDepartment": "855", "cmdbProductCode": "yanxuan-kefu", "applicable": true, "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5af00d89fa20a4465834b31d", "name": "客服内部管理系统", "productId": "6121", "productCode": "yanxuan-kefu-ims", "productName": "客服内部管理系统", "link": "/kfims", "tagid": "59704c45314569cb7097615d", "showIndex": 5, "description": "请输入申请的详细理由", "sensitiveLevel": 1, "businessDepartment": "855", "cmdbProductCode": "yanxuan-kefu-ims", "applicable": true, "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "599151d9056fed419354f21e", "name": "客服工单系统", "productId": "2021", "productCode": "tickets", "productName": "严选客服工单系统", "link": "/kefu/", "tagid": "59704c45314569cb7097615d", "showIndex": 6, "description": "请输入申请的详细理由", "sensitiveLevel": 2, "businessDepartment": "855", "cmdbProductCode": "tickets", "applicable": true, "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5b0e5994fa20a4465834b31f", "name": "严选成本核算系统", "productId": "6111", "productCode": "yanxuan-cost", "productName": "成本核算系统", "link": "/yanxuan-cost/index.html", "tagid": "59704c45314569cb7097615d", "showIndex": 10, "description": "严选成本核算系统", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5964519328b2ac57d9204897", "name": "严选财务系统", "productId": "2141", "productCode": "yanxuan-finance", "productName": "严选采购财务系统", "link": "/yanxuan-finance/index.html", "tagid": "59704c45314569cb7097615d", "showIndex": 11, "description": "负责人：王国云", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "59644ae267e5af57c820d77c", "name": "严选销售财务系统", "productId": "2151", "productCode": "yanxuan-finance-sales", "productName": "严选销售财务系统", "link": "/finance-sales/index.html", "tagid": "59704c45314569cb7097615d", "showIndex": 12, "description": "负责人：王国云", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5b98b412be28a035513ca8cd", "name": "严选结算网关", "productId": "6191", "productCode": "yanxuan-settlement-gateway", "productName": "严选结算网关", "link": "/sg-admin/index.html", "tagid": "59704c45314569cb7097615d", "showIndex": 13, "description": "严选结算网关管理后台", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5d9d43cbf30edd684e8b60f1", "name": "严选费控系统", "productId": "2241", "productCode": "yanxuan-finance-hec", "productName": "严选费控系统", "link": "/hec", "tagid": "59704c45314569cb7097615d", "showIndex": 14, "description": "严选费控系统", "icon": "", "sensitiveLevel": 3, "businessDepartment": "", "cmdbProductCode": "yanxuan-finance-hec", "applicable": true, "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5bf7bffc7473f02ee083aa14", "name": "内控工作台", "productId": "6401", "productCode": "audit", "productName": "内控工作台", "link": "/audit", "tagid": "59704c45314569cb7097615d", "showIndex": 100, "description": "负责人： 周明", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "59f3197c9e364664434b00ab", "name": "知识库-WIKI", "productId": "1700", "productCode": "wiki", "productName": "严选WIKI", "link": "/wiki", "tagid": "59704c45314569cb7097615d", "showIndex": 120, "description": "严选新版wiki", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "5de5d9b1f30edd684e8b60f3", "name": "成文管理系统", "productId": "7021", "productCode": "written-doc", "productName": "严选成文管理平台", "link": "/written-doc/", "tagid": "59704c45314569cb7097615d", "showIndex": 125, "description": "成文管理", "icon": "", "sensitiveLevel": 0, "businessDepartment": "", "cmdbProductCode": "written-doc", "applicable": false, "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "59e56763fde06364590b4e4d", "name": "谛听-舆情洞察中心", "productId": "4171", "productCode": "diting", "productName": "谛听-舆情洞察中心", "link": "/diting", "tagid": "59704c45314569cb7097615d", "showIndex": 149, "description": "产品经理：毛一凡 hzma<PERSON><PERSON><EMAIL>", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}, {"id": "59cdadfc8a47b96448852202", "name": "严选风控平台", "productId": "2081", "productCode": "yxrms", "productName": "严选风控系统", "link": "/yxrms", "tagid": "59704c45314569cb7097615d", "showIndex": 150, "description": "负责人：杨文超", "icon": "app-icon-1", "tag": {"id": "59704c45314569cb7097615d", "name": "支持", "icon": "app-icon-4", "background": "app-block-4", "showIndex": 50, "applications": []}}]}, {"id": "59704c45314569cb7097615a", "name": "数据", "showIndex": 80, "icon": "app-icon-1", "background": "app-block-1", "applications": [{"id": "595c97654b0d2d63edd76b0f", "name": "严选有数-敏捷BI平台", "productId": "4201", "productCode": "youdata", "productName": "严选有数", "link": "/youdata", "tagid": "59704c45314569cb7097615a", "showIndex": 1, "description": "产品经理：顾平 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5ac42f8339680e180dcbe038", "name": "VIPApp-移动数据工作台", "productId": "4121", "productCode": "vipapp", "productName": "VipApp", "link": "https://fir.im/4vpx", "tagid": "59704c45314569cb7097615a", "showIndex": 2, "description": "产品经理：张继萍 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5a56d4449e364664434b00af", "name": "轩辕-数据大屏", "productId": "4191", "productCode": "screen", "productName": "轩辕-数据大屏", "link": "/xuanyuan/", "tagid": "59704c45314569cb7097615a", "showIndex": 3, "description": "产品经理：张枝 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5da3e907f30edd684e8b60f2", "name": "数据智能决策平台", "productId": "7061", "productCode": "yanxuan-dis", "productName": "数据智能决策", "link": "/dis", "tagid": "59704c45314569cb7097615a", "showIndex": 40, "description": "智能决策平台", "icon": "", "sensitiveLevel": 0, "businessDepartment": "", "cmdbProductCode": "yanxuan-dis", "applicable": false, "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5af152c1b9eb22463d4c2a89", "name": "数据工作台", "productId": "5911", "productCode": "solar-bap", "productName": "严选数据工作台", "link": "/solar-bap", "tagid": "59704c45314569cb7097615a", "showIndex": 50, "description": "负责人： 陈锋 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "598818f94d65c779ec082306", "name": "夸父-埋点管理系统", "productId": "4131", "productCode": "dbs", "productName": "夸父-埋点管理系统", "link": "/kuafu2", "tagid": "59704c45314569cb7097615a", "showIndex": 60, "description": "产品经理：赵华翔 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5ce3ab6a56dd814edfdaa9a3", "name": "精卫-数据填报系统", "productId": "4421", "productCode": "dfs", "productName": "精卫-数据填报系统", "link": "/jingwei/", "tagid": "59704c45314569cb7097615a", "showIndex": 70, "description": "数据产品", "icon": "", "sensitiveLevel": 0, "businessDepartment": "", "cmdbProductCode": "", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5976b6f52a2d4b4a5aaba7ca", "name": "仓颉-指标管理系统", "productId": "4161", "productCode": "newdds", "productName": "仓颉-指标管理系统", "link": "/cangjie", "tagid": "59704c45314569cb7097615a", "showIndex": 80, "description": "产品经理：张枝 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5a56d4091edc4b645e4c234a", "name": "燧人-自助查询系统", "productId": "4141", "productCode": "dsq", "productName": "自助查询系统", "link": "/suiren/", "tagid": "59704c45314569cb7097615a", "showIndex": 85, "description": "产品经理：张枝 <EMAIL>", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5a9e3c7ec822d26cf36c3d0d", "name": "Holmes数据总线", "productId": "5811", "productCode": "holmes-datahub", "productName": "Holmes数据总线", "link": "/datahubv2/", "tagid": "59704c45314569cb7097615a", "showIndex": 110, "description": "负责人：潘松杜、黄祥为", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5b56f1ae2db043353b14c1a9", "name": "Atom实时计算平台", "productId": "5161", "productCode": "streaming-computing", "productName": "实时计算平台", "link": "/atom/web", "tagid": "59704c45314569cb7097615a", "showIndex": 120, "description": "负责人：潘松杜、黄祥为", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5aeff8cae7be9046426333c1", "name": "Bachelor数据治理", "productId": "5171", "productCode": "bigdata-computing", "productName": "离线计算及工具链", "link": "http://web.bachelor-ns1.service.qz.data.yx.mail.netease.com", "tagid": "59704c45314569cb7097615a", "showIndex": 150, "description": "提供hive表与表之间的关联查询，以及hive表与猛犸任务之间的关联查询", "isLock": false, "businessDepartment": "618", "cmdbProductCode": "bigdata-computing", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5af8f304321eef4653bd175a", "name": "大数据开发平台", "productId": "5171", "productCode": "bigdata-computing", "productName": "离线计算及工具链", "link": "http://zqhue.yx.mail.netease.com/", "tagid": "59704c45314569cb7097615a", "showIndex": 160, "description": "负责：左琴", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5b96249d2db043353b14c1ac", "name": "千寻-搜索中台", "productId": "6571", "productCode": "easy-search", "productName": "搜索中台", "link": "/easy-search", "tagid": "59704c45314569cb7097615a", "showIndex": 200, "description": "搜索中台", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5b8e3a99559f5e354c81a3ae", "name": "严选推荐管理后台", "productId": "6721", "productCode": "yx-comm-rcmd", "productName": "严选通用推荐", "link": "/yx-rcmd-backend/index.html", "tagid": "59704c45314569cb7097615a", "showIndex": 202, "description": "推荐管理后台，记录推荐模板版本变更及标签数据", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5b0e48e7e7be9046426333c3", "name": "严选商品搜索管理后台", "productId": "5361", "productCode": "search", "productName": "严选商品搜索", "link": "/search-center/index.html", "tagid": "59704c45314569cb7097615a", "showIndex": 203, "description": "严选商品搜索管理后台", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5b359142321eef4653bd175c", "name": "严选知识图谱", "productId": "6321", "productCode": "yanxuan-kg", "productName": "严选知识图谱", "link": "/kgmanager", "tagid": "59704c45314569cb7097615a", "showIndex": 204, "description": "关于严选商品及其各种信息的图谱管理系统", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}, {"id": "5b6ab8512db043353b14c1ab", "name": "Alpha机器学习平台", "productId": "6381", "productCode": "alpha", "productName": "机器学习平台", "link": "/alpha", "tagid": "59704c45314569cb7097615a", "showIndex": 210, "description": "机器学习平台", "tag": {"id": "59704c45314569cb7097615a", "name": "数据", "icon": "app-icon-1", "background": "app-block-1", "showIndex": 80, "applications": []}}]}, {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "showIndex": 100, "icon": "app-icon-5", "background": "app-block-5", "applications": [{"id": "59a674267b8aae6add45fc65", "name": "猿-研发工作台", "productId": "2111", "productCode": "ape", "productName": "严选研发工作台", "link": "/ape/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 0, "description": "请详细说明申请原因，相关工作职责", "sensitiveLevel": 1, "businessDepartment": "618", "cmdbProductCode": "ape", "applicable": true, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5c8b5a4b22de0876da366004", "name": "严选CMDB", "productId": "6931", "productCode": "yanxuan-cmdb", "productName": "严选CMDB", "link": "/cmdb", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 1, "description": "严选CMDB", "icon": "", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5d8895c15d5f2f68452cc569", "name": "SNest服务门户", "productId": "7271", "productCode": "snest", "productName": "服务鸟巢", "link": "/snest", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 2, "description": "服务治理平台的门户系统", "icon": "", "sensitiveLevel": 0, "businessDepartment": "", "cmdbProductCode": "snest", "applicable": false, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5d7853148d88296854dc5eda", "name": "严选流程平台", "productId": "6711", "productCode": "flowx", "productName": "严选流程平台", "link": "/flowx/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 5, "description": "流程平台", "icon": "", "sensitiveLevel": 0, "businessDepartment": "618", "cmdbProductCode": "flowx", "applicable": false, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5d26d925f30edd684e8b60ef", "name": "数据库管理平台", "productId": "6231", "productCode": "yxdb", "productName": "严选数据库平台", "link": "/yxdb/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 6, "description": "数据库管理平台", "icon": "", "sensitiveLevel": 0, "businessDepartment": "", "cmdbProductCode": "", "applicable": false, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5a7062ffc822d26cf36c3d0c", "name": "性能监控平台", "productId": "5631", "productCode": "caesar", "productName": "严选监控系统", "link": "/caesar/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 10, "description": "性能监控平台", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5cf7a2e15d5f2f68452cc567", "name": "机器监控平台", "productId": "7161", "productCode": "of", "productName": "严选Falcon监控平台", "link": "https://of.yx.mail.netease.com/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 11, "description": "open-falcon监控平台", "icon": "", "sensitiveLevel": 1, "businessDepartment": "618", "cmdbProductCode": "", "applicable": false, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5cae05ab83534d76cf5a043f", "name": "业务实时监控平台", "productId": "6941", "productCode": "yxarms", "productName": "严选业务实时监控系统", "link": "/goldeneye/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 12, "description": "严选业务实时监控系统", "icon": "", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5b1a2d58fa20a4465834b320", "name": "锦衣卫-报警平台", "productId": "6001", "productCode": "guards", "productName": "锦衣卫-报警平台", "link": "/guards/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 14, "description": "报警平台", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5bad954c1e10162ed9993740", "name": "严选分布式资源管理平台", "productId": "5201", "productCode": "apolloy", "productName": "严选分布式资源管理平台", "link": "/apolloy", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 15, "description": "严选分布式资源管理平台", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5cc7b03083534d76cf5a0440", "name": "严选日志平台", "productId": "6991", "productCode": "log-platform", "productName": "日志平台", "link": "/log/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 16, "description": "严选日志管理平台", "icon": "", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5b88cca4559f5e354c81a3ad", "name": "分布式任务调度中心", "productId": "5371", "productCode": "dschedule", "productName": "分布式调度中心", "link": "/dschedule/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 18, "description": "分布式任务调度中心", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5cd3f3fc22de0876da366006", "name": "Opera发布平台", "productId": "7001", "productCode": "opera", "productName": "严选发布平台", "link": "/yanxuan-opera-web", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 19, "description": "Opera发布平台", "icon": "", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5b98b86fff671e3536b4b469", "name": "PIG自动化发布平台", "productId": "6601", "productCode": "pig", "productName": "PIG自动化发布平台", "link": "/yxpdc/pig-view/index.html", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 20, "description": "PIG自动化发布平台", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5da852765d5f2f68452cc56a", "name": "严选稳定性平台", "productId": "7381", "productCode": "yanxuan-stability-platform", "productName": "严选稳定性平台", "link": "/stability-platform-web", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 30, "description": "严选稳定性平台", "icon": "", "sensitiveLevel": 0, "businessDepartment": "618", "cmdbProductCode": "yanxuan-stability-platform", "applicable": false, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5c88b81a83534d76cf5a043e", "name": "预案平台", "productId": "7041", "productCode": "preplan", "productName": "预案平台", "link": "/preplan/index.html", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 31, "description": "负责人：张伟杰，魏明", "icon": "", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5a952a58bf2c1d6d09fcc1bd", "name": "接口管理平台", "productId": "bee-interface", "productCode": "bee-interface", "productName": "严选接口管理平台", "link": "/bee/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 50, "description": "负责人：周明", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5a4eee1b9e364664434b00ae", "name": "文档部署平台", "productId": "3201", "productCode": "document", "productName": "严选文档部署平台", "link": "/document", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 51, "description": "负责人：吴欣翰", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5b3c83f3e7be9046426333c4", "name": "严选模板服务", "productId": "6291", "productCode": "template", "productName": "严选模板服务", "link": "/templates", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 52, "description": "严选模板服务，用户邮件等模板统一管理和提供，模板组装等", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5acec271321eef4653bd1757", "name": "PE运维管理平台", "productId": "6981", "productCode": "sre", "productName": "运维产品", "link": "/pe/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 101, "description": "负责人：张毅乐", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "59b0fb2721495f6ad80d9da0", "name": "权限管理中心", "productId": "3011", "productCode": "icac", "productName": "权限中心", "link": "/icac/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 200, "description": "请详细说明申请的原因，相关的工作职责", "sensitiveLevel": 2, "businessDepartment": "846", "cmdbProductCode": "icac", "applicable": true, "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5c234be522de0876da366003", "name": "严选开放平台管理后台", "productId": "6641", "productCode": "yanxuan-open-platform", "productName": "严选开放平台", "link": "/yxopen", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 300, "description": "开放平台管理后台", "icon": "", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "5baca0b6a67ced2ed52b19e2", "name": "严选API网关", "productId": "6031", "productCode": "ianus", "productName": "严选API网关", "link": "/ianus", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 400, "description": "负责人：杨文超", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}, {"id": "597349dacb583f17380732d7", "name": "门户管理后台", "productId": "3001", "productCode": "navAdmin", "productName": "严选门户", "link": "/bflow-admin/", "tagid": "5a952a04bf2c1d6d09fcc1bc", "showIndex": 401, "description": "负责人：吴子房", "tag": {"id": "5a952a04bf2c1d6d09fcc1bc", "name": "研发", "icon": "app-icon-5", "background": "app-block-5", "showIndex": 100, "applications": []}}]}, {"name": "未分类", "showIndex": 99999, "applications": []}]}