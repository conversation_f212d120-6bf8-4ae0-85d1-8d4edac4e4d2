import { GetMapping, PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, QueryContext, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { contextPath, xhrPrefix } from '../../conf';
import { fuzzyQueryFullInfo } from '../service/productcenter/productInfo';
import { ApplicationService } from './application.service';

/**
 * 用户信息，给前台的接口
 */
@RestController
@RequestMapping(`${contextPath}${xhrPrefix}/user`)
export class ApplicationController {
    constructor(
        private applicationService: ApplicationService
    ) { }
    /**
     * 查询所有标签，包含应用列表
     */
    @GetMapping('/getTagsWithApplications.do')
    async getTagsWithApplications(ctx: QueryContext<null, AjaxResult<any>>) {
        const email = ctx.openIDInfo.email;
        const tags = await this.applicationService.getTagsWithApplications(email);

        ctx.body = tags;
    }

    /**
     * 查询当前用户的收藏应用
     */
    @GetMapping('/getFavApps.do')
    async getFavApps(ctx: QueryContext<null, AjaxResult<any>>) {
        const favApps = await this.applicationService.getFavApps(ctx.openIDInfo.email);
        ctx.body = favApps;
    }

    /**
     * 收藏
     */
    @PostMapping('/addFavApp.do')
    async addFavApp(ctx: RequestContext<any, AjaxResult<any>>) {
        const vo = ctx.request.body;
        if (isNullOrUndefined(vo)) {
            return AjaxResult.badRequest('参数错误');
        }
        const favApps = await this.applicationService.addFavApp(ctx.openIDInfo.email, vo.appId);
        ctx.body = favApps;
    }

    /**
     * 取消收藏
     */
    @PostMapping('/cancelFavApp.do')
    async cancelFavApp(ctx: RequestContext<any, AjaxResult<any>>) {
        const vo = ctx.request.body;
        if (isNullOrUndefined(vo)) {
            return AjaxResult.badRequest('参数错误');
        }
        const favApps = await this.applicationService.cancelFavApp(ctx.openIDInfo.email, vo.appId);

        ctx.body = favApps;
    }

    /**
     * 获取所有产品列表
     */
    @PostMapping('/getAllProduct.do')
    async getAllProduct(ctx: RequestContext<any, AjaxResult<any>>) {
        const vo = ctx.request.body;
        if (isNullOrUndefined(vo)) {
            return AjaxResult.badRequest('系统异常');
        }
        const list = await this.applicationService.getAllProduct();

        ctx.body = list;
    }

    /**
     * 获取产品详情
     */
    @PostMapping('/fuzzyQueryFullInfo.do')
    async getProductInfo(ctx: RequestContext<any, AjaxResult<any>>) {
        const productInfo = await fuzzyQueryFullInfo(ctx.request.body);

        ctx.body = productInfo;
    }
}
