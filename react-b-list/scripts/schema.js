const fs = require('fs');
const glob = require('glob');
const path = require('path');

function mkdirp(p) {
    if (!fs.existsSync(path.dirname(p))) {
        mkdirp(path.dirname(p));
    }
    if (!fs.existsSync(p)) {
        fs.mkdirSync(p);
    }
}

module.exports = async function (pattern) { // commands/**/*.json
    const allJsonFiles = glob.sync(pattern, {
        ignore: [
            '**/node_modules/**',
            '**/package.json',
        ],
    });
    const quicktypeRunner = require('./quicktype');
    console.log('Generating JSON Schema to TS ...');
    for (const fileName of allJsonFiles) {
        const content = fs.readFileSync(fileName, 'utf-8');

        let json;
        try {
            json = JSON.parse(content);
            if (typeof json.$schema !== 'string' || !json.$schema.startsWith('http://json-schema.org/')) {
                continue;
            }
        } catch {
            continue;
        }
        const tsContent = await quicktypeRunner.generate(fileName);
        const tsPath = path.join(fileName.replace(/\.json$/, '.ts'));
        mkdirp(path.dirname(tsPath));
        fs.writeFileSync(tsPath, tsContent, 'utf-8');
    }
    console.log('Generating JSON Schema to TS DONE');
    return allJsonFiles;
}