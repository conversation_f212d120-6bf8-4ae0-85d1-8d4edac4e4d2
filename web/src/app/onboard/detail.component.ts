import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { WorkFlowData } from '@shark/shark-yx-workflow';

@Component({
    templateUrl: './detail.component.html',
    selector: 'onboard-detail'
})
export class OnboardDetailComponent {
    @Input()
    userInfo: any = null;
    @Input()
    workFlowData: WorkFlowData = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };

    switch: boolean = true;
    constructor(
        private router: Router
    ) {

    }

    async ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
        }
    }

    // 跳到编辑页
    goEditOrder() {
        this.router.navigate(['/workflow/detail', {
            topologyId: this.workFlowData.topologyId,
            flowId: this.workFlowData.flowId,
            isEdit: 1
        }]);
    }
}
