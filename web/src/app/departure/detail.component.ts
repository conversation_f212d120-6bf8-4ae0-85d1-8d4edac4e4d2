import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './detail.component.html',
    selector: 'flow-departuredetail'
})
export class DepartureDetailComponent {
    @Input()
    userInfo: any = null;
    @Input()
    workFlowData: WorkFlowData = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    posTreeList: any[] = [];
    switch: boolean = true;
    constructor(
        private router: Router,
        private ajax: Ajax
    ) {

    }

    async ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
            const originOrgPos = this.flowData.flowNodeMessage.originOrgPos || {};
            for (const orgPosId in originOrgPos) {
                if (originOrgPos.hasOwnProperty(orgPosId)) {
                    this.posTreeList.push(originOrgPos[orgPosId].name);
                }
            }
        }
    }

    // 跳到编辑页
    goEditOrder() {
        this.router.navigate(['/workflow/detail', {
            topologyId: this.workFlowData.topologyId,
            flowId: this.workFlowData.flowId,
            isEdit: 1
        }]);
    }
    /**
     * 打印工单
     */
    printOrder() {
        window.print();
    }
}
