<div class="layout">
    <div class="layout-header">
        <a class="layout-header-logo" title="回到首页" [routerLink]="'/home'" [router-active]="'/home'">
            <img src="http://mailshark.nos-jd.163yun.com/static/yx-logo.svg" style="width:91px;height: 31px;">
        </a>
        <div class="product-name">严选业务门户</div>
        <div *ngIf="currentEvn" class="product-name env-color">{{currentEvn}}</div>
        <!-- <marquee *ngIf="dealingNum" class="m-marquee" behavior="scroll" direction="left">有{{dealingNum}}个工单待办，请及时处理！！！</marquee> -->
        <div class="float-right" style="line-height:61px; margin-right: 140px;">
            <button class="btn btn-link" type="button" (click)="getMyPerm()">我的权限</button>
        </div>
        <div class="layout-header-user-info" shark-toggle-element [target]="'#logout'">
            <div class="layout-header-user-name">欢迎你，{{user?.fullname}}</div>
            <div class="layout-header-arrow-btn"></div>
        </div>
        <ul class="layout-header-info-dropdown" id="logout">
            <li>
                <span class="layout-header-info-dropdown-item" (click)="goLogout()">退出</span>
            </li>
        </ul>
    </div>
    <div class="layout-body scroll-parent layout-single layout-title-icon">
        <div class="layout-aside" shark-menu>
            <ul class="menu">
                <li>
                    <ul class="menu-content menu-content-vertical">
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/home'" [router-active]="'/home'">系统导航</a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/userCenter'" [router-active]="'/userCenter'">个人中心</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <div class="menu-title">
                        <span><i class="icon-liucheng margin-r-1x"></i>工单管理</span>
                    </div>
                    <ul class="menu-content menu-content-vertical">
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/createFlow'" [router-active]="'/workflow/createFlow'">新建工单</a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/createdList'" [router-active]="'/workflow/createdList'">我的发起</a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn m-dealingList" [routerLink]="'/workflow/dealingList'" [router-active]="'/workflow/dealingList'">待办工单<span *ngIf="dealingNum" class="dealingNum">({{dealingNum}})</span></a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/dealedList'" [router-active]="'/workflow/dealedList'">已办工单</a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/trailerList'" [router-active]="'/workflow/trailerList'">跟踪工单</a>
                        </li>
                        <li class="menu-nav" *perm="300100">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/monitorList'" [router-active]="'/workflow/monitorList'">工单监控</a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/docList'" [router-active]="'/workflow/docList'">文档管理</a>
                        </li>
                        <li class="menu-nav">
                            <a class="menu-nav-btn" [routerLink]="'/workflow/trainList'"  [router-active]="'/workflow/trainList'">培训统计</a>
                        </li>
                    </ul>
                </li>
                <li *perm="200">
                    <div class="menu-title">
                        <span>权限管理</span>
                    </div>
                    <ul class="menu-content menu-content-vertical">
                        <li class="menu-nav" *perm="230">
                            <a class="menu-nav-btn" [routerLink]="'userCenterManage/userList'" routerLinkActive="active">用户管理</a>
                        </li>
                        <li class="menu-nav" *perm="220">
                            <a class="menu-nav-btn" [routerLink]="'userCenterManage/roleList'" routerLinkActive="active">角色管理</a>
                        </li>
                        <li class="menu-nav" *perm="210">
                            <a class="menu-nav-btn" [routerLink]="'userCenterManage/permList'" routerLinkActive="active">权限管理</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <div class="layout-main">
            <router-outlet></router-outlet>
        </div>
    </div>
</div>