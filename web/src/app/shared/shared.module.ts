import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Ajax, EventSubject, SharkModule } from '@shark/shark-angularX';
import { YXWorkFlowModule, YXWrokFlowConfig } from '@shark/shark-yx-workflow';
import { UmcManageModule } from '@yx-module/umc-manage';
import { ConfigService as UmcManageConfigService } from '@yx-module/umc-manage';
import { UmcSelectorModule } from '@yx-module/umc-selector';
import { ConfigService as UmcSelectorConfigService } from '@yx-module/umc-selector';
import { AjaxUrl, AppConfig } from '../config';

/***********common service***************/
import { PoPoModule } from '@shark/popo';

/*********** services *************/
import { UserService } from './services/user.service';
import { UtilService } from './services/util.service';
import { PermService } from './services/perm.service';
import { CmdbService } from './services/cmdb.serverice'

/***********common directive***************/
import { ImagePreview } from './directives/image-preview.directive';
import { RouterActiveDirective } from './directives/router-active.directive';

/***************** pipe *********************/
import { EmployeeRoleTypePipe, EmployeeTypePipe, TransferTypePipe } from './pipes/employee.pipe';
import { EmailPrefixPipe, NumberPrefixPipe } from './pipes/util.pipe';
import { FlowBusinessStatePipe, FlowStatePipe, NodeNamePipe, TopologyNamePipe, FlowSpecialBusinessStatePipe, MultiApplySystemPipe, TopologyAuthNamePipe } from './pipes/workflow.pipe';

const modules = [
    CommonModule,
    FormsModule,
    SharkModule,
    YXWorkFlowModule,
    UmcManageModule,
    UmcSelectorModule,
    PoPoModule
];
const services = [UserService, UtilService, PermService, CmdbService];
const directives = [ImagePreview, RouterActiveDirective];
const pipes = [EmployeeTypePipe, EmployeeRoleTypePipe, TopologyNamePipe, EmailPrefixPipe,NumberPrefixPipe, NodeNamePipe,
    FlowBusinessStatePipe, TransferTypePipe, FlowStatePipe, FlowSpecialBusinessStatePipe, MultiApplySystemPipe, TopologyAuthNamePipe];
@NgModule({
    providers: [...services, ...pipes],
    declarations: [...directives, ...pipes],
    exports: [...modules, ...directives, ...pipes]
})
export class SharedModule {
    constructor(
        private ajax: Ajax,
        private utilService: UtilService,
        private umcManageConfigService: UmcManageConfigService,
        private umcSelectorConfigService: UmcSelectorConfigService
    ) {
        // 权限中心模块（用户列表，角色列表，权限列表，统一组织架构，业务组织架构）
        this.umcManageConfigService.productName = 'navAdmin'; // 产品号，找****************************申请
        this.umcManageConfigService.contextPath = AppConfig.contextPath;
        this.umcManageConfigService.workOrg = 'navAdmin'; // 业务组织架构，找****************************申请
        this.umcManageConfigService.cookieName = 'YX_CSRF_TOKEN';
        this.umcManageConfigService.headerName = 'Yx-Csrf-Token';
        // 选择器模块（人员选择器，组织架构选择器）
        this.umcSelectorConfigService.productName = 'navAdmin';
        this.umcSelectorConfigService.contextPath = AppConfig.contextPath;
        this.umcSelectorConfigService.cookieName = 'YX_CSRF_TOKEN';
        this.umcSelectorConfigService.headerName = 'Yx-Csrf-Token';
        // 工作流相关配置
        YXWrokFlowConfig.bpmDataUrl = AjaxUrl.bpmflow.queryFlowMap;
        YXWrokFlowConfig.oplogListUrl = AjaxUrl.bpmflow.queryFlowHistory;
        YXWrokFlowConfig.flowDataUrl = AjaxUrl.bpmflow.queryFlowDetail;

        // 异常处理
        EventSubject.subscribe((message) => {
            if (message.module === 'yx-module-umc') {
                if (message.type === 'ajax') {
                    if (message.data.code === 401 || message.data.code === '401' || message.data.code === 302 || message.data.code === '302') {
                        this.utilService.goLogin();
                    }
                }
            }
        });
    }
}
