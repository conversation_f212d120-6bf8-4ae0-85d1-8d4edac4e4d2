import { BaseWorkflow, FlowMetaData, FlowNodeMessage, SkipParam } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import { getBestOrg, getSecondLevelManagerInfoByOrgId, getSecondLevelPoliticalInfoByOrgId, getThirdLevelManagerInfoByOrgId, isAuthedThirdLevelOrg } from '../../service/permcenter/approvePerm';
import Config from './transfer-config';

@Service
export default class Workflow extends BaseWorkflow {
    constructor(config: Config) {
        super(config);
    }
    /**
     * @description 获取节点的审批人信息。处理各个节点
     * @param flowMetaData 工单元数据
     * @param flowNodeMessage 工单业务数据
     * @returns string[] 审批人uid列表
     */
    async getApproverList(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<string[]> {

        const currentNodeId = flowMetaData.currentNodeId;
        const orgPosId = flowNodeMessage.orgPosId;
        let approverUsers = [];
        switch (currentNodeId) {
            // 代理人 转出
            case '30010402':
                const originOrgPos = flowNodeMessage.originOrgPos;
                const transferOrgPos: any[] = flowNodeMessage.transferOrgPos;
                const thirdPosId = transferOrgPos.find((org) => originOrgPos[org].level === 97);
                approverUsers = await getThirdLevelManagerInfoByOrgId(thirdPosId);
                break;
            // 二级 转出
            case '30010403':
                const { secondManger } = await getBestOrg(flowNodeMessage.transferOrgPos);
                approverUsers = secondManger;
                break;
            // 指导员审批节点 转出
            case '30010407':
                const { potical } = await getBestOrg(flowNodeMessage.transferOrgPos);
                approverUsers = potical;
                break;
            // 代理人 转入
            case '30010404':
                approverUsers = await getThirdLevelManagerInfoByOrgId(orgPosId);
                break;
            // 二级转入
            case '30010405':
                approverUsers = await getSecondLevelManagerInfoByOrgId(orgPosId);
                break;
            // 指导员审批节点 转入
            case '30010408':
                approverUsers = await getSecondLevelPoliticalInfoByOrgId(orgPosId);
                break;
        }
        return approverUsers.map((approver: any) => {
            return approver.uid;
        });
    }

    /**
     * @description 流程跳转条件, 如果有多条件的跳转，需要用户进行重写实现，处理各个节点的跳转条件设置
     * @param flowMetaData
     * @param flowNodeMessage
     */
    async getCustomSkipParam(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<SkipParam> {
        const currentNodeId = flowMetaData.currentNodeId;
        const skipParam: any = { approved: flowNodeMessage.approved!, paramMap: {} };
        // 通过审批的情况下
        if (flowNodeMessage.approved) {
            switch (currentNodeId) {
                // 指导员审批节点 转出
                case '30010407':
                    // 是否有代理审批节点
                    const originOrgPos = flowNodeMessage.originOrgPos;
                    const transferOrgPos: any[] = flowNodeMessage.transferOrgPos;
                    const thirdPosId = transferOrgPos.find((org) => originOrgPos[org].level === 97);
                    skipParam.paramMap.agency = thirdPosId && await isAuthedThirdLevelOrg('transfer', 97, thirdPosId) || false;
                    break;
                // 指导员审批节点 转入
                case '30010408':
                    // 是否有代理审批节点
                    const orgPosId = flowNodeMessage.orgPosId;
                    const level = flowNodeMessage.level;
                    skipParam.paramMap.agency = await isAuthedThirdLevelOrg('transfer', level, orgPosId) || false;
                    break;
            }
        }
        return skipParam;
    }
}
