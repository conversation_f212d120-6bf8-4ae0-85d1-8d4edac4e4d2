import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Watermark } from '@shark/secure';
import { Ajax, SharkModalService, SharkToastrService, Cookie } from '@shark/shark-angularX';
import { IUser, UserService } from './shared/services/user.service';
import { UserPermListService } from '@yx-module/umc-permission';
import { Event } from '@sharkr/utils';
import { AjaxUrl } from './config';
// import { DealingTipModal } from './workflow/list/dealingTipModal/dealingTipModal.component';

@Component({
    selector: '#root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss']
})
export class AppComponent {
    user: IUser;
    currentEvn: string;
    time: Date = new Date();
    returnUrl: string = location.href;
    isAdmin = false;
    dealingNum = 0
    constructor(
        private modal: SharkModalService,
        private toastr: SharkToastrService,
        private userService: UserService,
        private router: Router,
        private cookie: <PERSON>ie,
        private ajax: Ajax,
        private userPermListServices: UserPermListService
    ) {
        this.ajax.setFilterCode((result) => {
            if ((result.code === 302 || result.code === '302') && result.location) {
                window.location.href = 'https://yx.mail.netease.com/openid/login?url=' + encodeURIComponent(location.href);
                return true;
            }
            return result.code === 200 || result.code === '200';
        });
        Event.on('nav:myperm', () => {
            this.getMyPerm();
        });
    }
    async ngOnInit() {
        this.user = await this.userService.getUser();
        this.isAdmin = this.user.email === '<EMAIL>';
        Watermark.init({
            text: this.user.email.split('@')[0]
        });

        // 加入组织架构
        // const inOrg = await this.userService.checkUserIsInOrg();
        // // 测试环境不提示
        // if (!inOrg && location.host !== 'test.yx.mail.netease.com') {
        //     this.modal.alert({
        //         title: '申请加入严选组织架构',
        //         content: `
        //             为进一步完善授权体系，加强系统用户账号和权限的规范化管理，同时为后续各系统安全、有序、稳定运行，防范风险铺垫基础，【严选业务门户】已于2018年10月15日支持员工线上自主提交【申请加入组织架构】、【申请离职工单】及【申请转岗工单】。
        //             <br/>
        //             故即2018年10月15日起网易严选全体员工自主发起线上相应工单申请！
        //             <br/>

        //         `,
        //         okText: '马上申请加入',
        //         cancelText: '已申请/稍后再说'
        //     }).then(() => {
        //         window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow`);
        //     }).catch(() => {
        //         this.toastr.info('稍后再说');
        //     });
        // }
        this.userPermListServices.contextPath="/bflow-base/xhr";
        this.getDealingNum()
        Event.on('nav:refreshDealing', () => {
            this.getDealingNum()
        })
    }

    // 工单待处理弹窗
    // openOrderPeddingModal(num: number) {
    //     const latestDate = new Date();
    //     const latestTime = latestDate.getTime();
    //     const activeTime = this.cookie.getCookie('activeTime');

    //     const openModal = () => {
    //         this.modal.open({
    //             type: 'dialog',
    //             backdrop: 'static',
    //             size: 'sm',
    //             data: {num},
    //             component: DealingTipModal,
    //         });
    //     }
    //     if (!activeTime) {
    //         //first time in
    //         openModal();
    //     } else {
    //         // 已经设置过cookie
    //         const activeDate = new Date(+activeTime);
    //         const newDay = latestDate.getDate();
    //         const activeDay = activeDate.getDate();
    //         if (activeDay !== newDay) {
    //             // 不是同一天
    //             openModal();
    //         }
    //     }
    //     // 过期时间设置为1年
    //     this.cookie.setCookie('activeTime', latestTime + '', latestTime + 31536000000);
    // }

    toDealingList() {
        this.router.navigate(['/workflow/dealingList']);
    }

    getDealingNum() {
    // 获取待我审批的工单列表
        const pagination: any = {
            page: 1,
            size: 10
        };
        const param = Object.assign({}, {
            // 分页参数
            ...pagination
        });
        this.ajax.postByJson(AjaxUrl.bpmflow.queryDealingList, param).then((res) => {
            console.log(res)
            this.dealingNum = res.data.pagination.total
            // if (this.dealingNum) {
            //     this.openOrderPeddingModal(this.dealingNum);
            // }
        });
    }

    getCurrentEnv() {
        if (ENV === 'development') {
            this.currentEvn = '当前开发环境';
        } else {
            if (ENV_TARGET === 'test') {
                this.currentEvn = '当前测试环境';
            }
        }
    }

    goLogout() {
        window.location.href = `https://yx.mail.netease.com/openid/logout`;
    }

    async getMyPerm () {
        this.userPermListServices.lookPermission(this.user.email);
    }
}
