<ng-container *ngIf="workFlowData.flowData">
    <ex-section [head]="'拓扑流程'">
        <workflow-topo [workFlowData]="workFlowData">
        </workflow-topo>
        <div class="margin-4x" *ngIf="this.flowId && this.flowData.flowNodeMessage?.subList?.length">
            <button class="btn btn-link" *ngIf="showChildren" (click)="spread()" type="button">查看子工单</button>
            <button class="btn btn-link" *ngIf="!showChildren" (click)="spread()" type="button">收起子工单</button>
        </div>
        <div *ngIf="!showChildren">
            <ng-container *ngFor="let itemFlowData of sublist;">
                {{itemFlowData.flowData.flowNodeMessage.applicantProductList[0].productName}}{{+itemFlowData.flowData.flowNodeMessage.sensitiveLevel > 1 ? '(涉敏)' : '' }}
                <workflow-topo [workFlowData]="itemFlowData"></workflow-topo>
            </ng-container>
        </div>
    </ex-section>
    <ex-section [head]="'历史记录'" *ngIf="this.flowId" [expandOnInit]=false>
        <workflow-log [workFlowData]="workFlowData" [excludeColumns]="['files']"></workflow-log>
    </ex-section>
    <ex-section *ngIf="this.flowId" [head]="'创建信息'" [expandOnInit]=true>
        <workflow-creator [workFlowData]="workFlowData"></workflow-creator>
    </ex-section>
    <ex-section [head]="'工单信息'" [expandOnInit]=true>
        <flow-permedit [workFlowData]="workFlowData" [userInfo]="userInfo" *ngIf="showEdit()"></flow-permedit>
        <flow-permdetail [workFlowData]="workFlowData" [userInfo]="userInfo" *ngIf="!showEdit()"></flow-permdetail>
        <flow-permapprove [workFlowData]="workFlowData" *ngIf="!showEdit() && showApprove()"></flow-permapprove>
    </ex-section>
</ng-container>