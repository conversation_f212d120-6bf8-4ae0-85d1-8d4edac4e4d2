import { BaseWorkflow, FlowMetaData, FlowNodeMessage, SkipParam } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import { isNullOrUndefined } from 'util';
import { getUpperByOrgPosId, getUpperOrgListByOrgPosId } from '../../service/permcenter/unitOrg';
import Config from './writtendoc-config';
import { EmployeeRoleType } from '../../unitorg/employeeRoleType';

// 组织架构相关类
export interface IOrgPosUniteUser {
    orgId: number;
    orgPosId: number;
    orgPosName: string;
    userId: number;
    uid: string;
    type: number;
    employeeName: string;
    employeeNum: string;
    realName: string;
}

export interface IOrgPosUnite {
    level: number;
    orgPosId: number;
    orgPosName: string;
    parentOrgPosId: number;
    orgPosUniteUsers: IOrgPosUniteUser[];
}

export declare type IOrgList = IOrgPosUnite[];

export interface IUpperOrgPosMap {
    [key: number]: IOrgList;
}

export interface IUpperLealerInfo {
    leader: IOrgPosUniteUser[];
    instructor: IOrgPosUniteUser[];
}


@Service
export default class Workflow extends BaseWorkflow {
    constructor(config: Config) {
        super(config);
    }

    /**
     * @description 获取节点的审批人信息。处理各个节点
     * @param flowMetaData 工单元数据
     * @param flowNodeMessage 工单业务数据
     * @returns string[] 审批人uid列表
     */
    async getApproverList(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<string[]> {
        const currentNodeId = flowMetaData.currentNodeId;
        const approverUsers: any[] = [];
        // 所有获取审批人的操作会放在成文管理系统
        return approverUsers;
    }

    /**
     * @description 流程跳转条件, 如果有多条件的跳转，需要用户进行重写实现，处理各个节点的跳转条件设置
     * @param flowMetaData
     * @param flowNodeMessage
     */
    async getCustomSkipParam(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<SkipParam> {
        const currentNodeId = flowMetaData.currentNodeId;
        const type = flowNodeMessage.content.categoryId;
        const creator = flowNodeMessage.createUser;
        let skipParam: SkipParam = { approved: true, paramMap: {} };
        if (!isNullOrUndefined(flowNodeMessage.approved)) {
            skipParam.approved = flowNodeMessage.approved;
        }
        // 所属部门的ID
        const departmentId = flowNodeMessage.content.departmentId;
        const mainDepartmentId = flowNodeMessage.content.mainDepartmentId;
        const countersignList: string[] = flowNodeMessage.content.countersignList;
        let branch = 0;
        // 通过审批的情况下
        if (flowNodeMessage.approved) {
            switch (currentNodeId) {
                // 内控文档管理员复核
                case '70210102':
                    if (type === 49 || type === 50 || type === 55 || type === 56 || type === 57
                        || type === 40) {
                        // 规章制度类、L5L6，有会签则走会签，无则跳过
                        if (isNullOrUndefined(countersignList) || countersignList.length === 0) {
                            branch = await this.getSkipBranch(departmentId, creator, mainDepartmentId);
                            // 正常——>三级
                            if (branch === 2) {
                                branch = 0;
                            } else if (branch === 5) {
                                // L5L6如果没有三级和二级负责人，直接完结 todo后续应该要改
                                if (type !== 40) {
                                    branch = 3;
                                }
                            }
                            // 三级没有走——>二级
                            skipParam.paramMap = { branch };
                            break;
                        }
                    }
                    skipParam.paramMap = { branch: 2 };
                    break;
                // 会签人员审核
                case '70210103':
                    branch = await this.getSkipBranch(departmentId, creator, mainDepartmentId);
                    // 正常——>三级，三级没有——>二级，二级没有——>审定人
                    if (type === 49 || type === 50 || type === 55 || type === 56 || type === 57) {
                        // L5L6如果没有三级和二级负责人，直接完结 todo后续应该要改
                        if (branch === 5) {
                            branch = 3;
                        }
                    }
                    skipParam.paramMap = { branch };
                    break;
                // 三级部门负责人审批
                case '70210104':
                    // L5L6
                    if (type === 49 || type === 50 || type === 55 || type === 56 || type === 57) {
                        skipParam.paramMap = { branch: 3 };
                    } else {
                        // 如果大于二级那么跳过二级
                        branch = await this.getSecondSkipBranch(departmentId, creator, mainDepartmentId);
                        skipParam.paramMap = { branch };
                    }
                    break;
                // 二级部门负责人审批
                case '70210105':
                    skipParam.paramMap = { branch: 2 };
                    break;
                // 内控/合规部负责人审批
                case '70210108':
                    if (type === 47 || type === 48 || type === 53 || type === 54) {
                        skipParam.paramMap = { branch: 0 };
                    } else {
                        skipParam.paramMap = { branch: 2 };
                    }
                    break;
                // 网易严选CEO审批
                case '70210107':
                    skipParam.paramMap = { branch: 3 };
                    break;
                default:
                    skipParam.paramMap = { branch: 2 };
                    break;
            }
        } else {
            // 撤回or审批不通过
            skipParam.paramMap = { branch: 1 };
        }
        return skipParam;
    }

    /**
     * 获取当前选择的部门的跳转条件
     * @param departmentId 部门ID
     * @param creator 创建人
     * @param mainDepartmentId 主责部门ID
     */
    async getSkipBranch(departmentId: number, creator: string, mainDepartmentId: number): Promise<number> {
        // 拟制部门
        const iOrgList = await getUpperOrgListByOrgPosId(departmentId);
        const level = await this.handleUserOrg(iOrgList, creator);
        // 主责部门
        const mainOrgList = await getUpperOrgListByOrgPosId(mainDepartmentId);
        const mainLevel = await this.handleUserOrg(mainOrgList, creator);

        const resultLevel = Math.min(level, mainLevel);
        // 如果有主管的部门超过了三级，那么跳过三级部门
        if (resultLevel === 98) {
            return 4;
        } else if (resultLevel > 98) {
            return 5;
        }
        return 2;
    }

    /**
     * 获取当前选择的部门的跳转条件
     * @param departmentId 部门ID
     * @param creator 创建人
     * @param mainDepartmentId 主责部门ID
     */
    async getSecondSkipBranch(departmentId: number, creator: string, mainDepartmentId: number): Promise<number> {
        // 拟制部门
        const iOrgList = await getUpperOrgListByOrgPosId(departmentId);
        const level = await this.handleSecondUserOrg(iOrgList, creator);
        // 主责部门
        const mainOrgList = await getUpperOrgListByOrgPosId(mainDepartmentId);
        const mainLevel = await this.handleUserOrg(mainOrgList, creator);

        const resultLevel = Math.min(level, mainLevel);
        // 如果有主管的部门超过了三级，那么跳过三级部门
        if (resultLevel > 98) {
            return 5;
        }
        return 2;
    }


    /**
     * 获取当前登录人选择的二级组织架构信息
     * @param orgList
     * @param creator
     */
    async handleSecondUserOrg(orgList: IOrgList, creator: string): Promise<number> {
        let resultLevel = 0;
        // 遍历主管,从最下级往上找，直到找到有主管的部门
        orgList.sort((a, b) => (a.level - b.level));
        for (const org of orgList) {
            // 获取最下级有负责人的部门
            const filterIOrgList = org.orgPosUniteUsers.filter((user) => (user.type === 2));
            // 如果存在负责人，而且当前部门等级为三级及以上，且负责人不是自己，那么可以返回
            if (filterIOrgList.length !== 0 && org.level >= 98 && filterIOrgList[0].uid !== creator) {
                resultLevel = org.level;
                break;
            }
        }
        // 获取跳转条件
        return resultLevel;
    }

    /**
     * 获取当前登录人选择的组织架构信息
     * @param orgList
     * @param creator
     */
    async handleUserOrg(orgList: IOrgList, creator: string): Promise<number> {
        let resultLevel = 0;
        // 遍历主管,从最下级往上找，直到找到有主管的部门
        orgList.sort((a, b) => (a.level - b.level));
        for (const org of orgList) {
            // 获取最下级有负责人的部门
            const filterIOrgList = org.orgPosUniteUsers.filter((user) => (user.type === 2));
            // 如果存在负责人，而且当前部门等级为三级及以上，且负责人不是自己，那么可以返回
            if (filterIOrgList.length !== 0 && org.level >= 97 && filterIOrgList[0].uid !== creator) {
                resultLevel = org.level;
                break;
            }
        }
        // 获取跳转条件
        return resultLevel;
    }
}
