import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
    name: 'emailprefix'
})
export class EmailPrefixPipe implements PipeTransform {
    transform(value) {
        const regExp = /(^[A-Za-z0-9_-]+)@/;
        const regRst = regExp.exec(value || '');
        if (regRst) {
            return regRst[1];
        } else {
            return '';
        }
    }
}

@Pipe({
    name: 'numberprefix'
})
export class NumberPrefixPipe implements PipeTransform {
    transform(value) {
        if (value) {
            return Number(value) > 9 ? '9+' : '9'
        } else {
            return value
        }
    }
}