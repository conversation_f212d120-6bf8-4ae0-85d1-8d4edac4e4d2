import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute,Router } from '@angular/router';
import { Ajax, Cookie, SharkToastrService, SharkModalService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';
import { uploadTainInfoComponent } from '../workflow/list/uploadTainInfo/uploadTainInfo.component';

@Component({
    templateUrl: './detail.component.html',
    selector: 'flow-doc-authority-projectdetail'
})
export class DocProjectDetailComponent {
    @Input()
    userInfo: any = null;
    @Input()
    workFlowData: WorkFlowData = null;
    @Input() showTrainList: boolean;
    @Output('checked') checkedBack = new EventEmitter<any>();
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    posTreeList: any[] = [];
    switch: boolean = true;
    constructor(
        private router: Router,
        private ajax: Ajax,
        private toastr: SharkToastrService,
        private route: ActivatedRoute,
        private sharkModalService: SharkModalService
    ) {
    }
    ngOnInit() {
        // console.log(this.showTrainList);
    }
    async ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            }
            const originOrgPos = this.flowData.flowNodeMessage.originOrgPos || {};
            for (const orgPosId in originOrgPos) {
                if (originOrgPos.hasOwnProperty(orgPosId)) {
                    this.posTreeList.push(originOrgPos[orgPosId].name);
                }
            }
        }
    }
    // 返回按钮
    dismiss() {
        history.back();
    }

    // 跳到编辑页
    goEditOrder() {
        this.router.navigate(['/workflow/detail', {
            topologyId: this.workFlowData.topologyId,
            flowId: this.workFlowData.flowId,
            isEdit: 1
        }]);
    }
    /**
     * 打印工单
     */
    printOrder() {
        window.print();
    }
    // 成文文件详情跳转
    toDoc(){
        if(window.location.hostname==='bflow.test.yx.mail.netease.com'){
            window.open('http://document.test.yx.mail.netease.com/written-doc/index.html#/doc/'+ this.flowData.flowNodeMessage.content.fileId +'/doc') 
            // window.open('http://test.yx.mail.netease.com/written-doc/index.html#/doc/'+ this.flowData.flowNodeMessage.content.categoryId +'/doc') 
        }else if(window.location.hostname==='dev.test.yx.mail.netease.com'){
            window.open('http://dev.test.yx.mail.netease.com/written-doc/index.html#/doc/' + this.flowData.flowNodeMessage.content.fileId +'/doc') 
        }else if(window.location.hostname==='yx.mail.netease.com'){
            window.open('http://yx.mail.netease.com/written-doc/index.html#/doc/' + this.flowData.flowNodeMessage.content.fileId +'/doc') 
        }else{
            window.open('http://test.yx.mail.netease.com/written-doc/index.html#/doc/' + this.flowData.flowNodeMessage.content.fileId +'/doc')
        }
    }
    // 点击处理按钮 跳转至拟写成文文件页面
    toEdit() {
        // console.log(this.flowData.flowNodeMessage.content.fileId);
        if (window.location.hostname === 'bflow.test.yx.mail.netease.com') {
            window.open('http://document.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
        } else if (window.location.hostname === 'dev.test.yx.mail.netease.com') {
            window.open('http://dev.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
        } else if (window.location.hostname === 'yx.mail.netease.com') {
            window.open('http://yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
        } else {
            window.open('http://test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
        }
    }
    
    // 撤回工单
    async revoke(){
        const params = {
            flowMetaData: this.flowData.flowMetaData,
            flowNodeMessage: this.flowData.flowNodeMessage
        }
        this.sharkModalService.confirm(
            {
                title: '撤回工单',
                content: '<p>确定要撤回此工单吗？</p>',
                okText: '确定',
                cancelText: '取消'
            }).then(async () => {
                await this.ajax.postByJson(AjaxUrl.docManagement.revoke, params).then(res => {
                    if(res.code === 200){
                        this.toastr.success('撤回成功！');
                        window.location.reload();
                    }
                },(err) =>{
                    this.toastr.error('撤回失败！');
                })
            },() => {
                this.toastr.success('取消撤回！');
            });
    }
    // 撤回作废工单
    async revokeDel() {
        const params = {
            flowMetaData: this.flowData.flowMetaData,
            flowNodeMessage: this.flowData.flowNodeMessage
        }
        this.sharkModalService.confirm(
            {
                title: '撤回工单',
                content: '<p>确定要撤回此工单吗？</p>',
                okText: '确定',
                cancelText: '取消'
            }).then(async () => {
                await this.ajax.postByJson(AjaxUrl.docManagement.revokeDel, params).then(res => {
                    if(res.code === 200){
                        this.toastr.success('撤回成功！');
                        window.location.reload();
                    }
                },(err) => {
                    this.toastr.error('撤回失败！');
                })
            }, () => {
                this.toastr.success('取消撤回！');
            });
        }

    uploadTainInfo(){
        this.sharkModalService.open({
            type: 'dialog',
            backdrop: 'static',
            size: 'md',
            component: uploadTainInfoComponent,
            data: {item: this.flowData}
        }).then((res: any) => {
            if(res === 'ok'){
                window.location.reload();
            }
        }, () => {
            
        });

    }
   
    // 处理按钮跳转
    // toEdit(item:any){
    //     console.log(item);
    //     if(window.location.hostname==='bflow.test.yx.mail.netease.com'){
    //         // editdocs/:fileId
    //         window.open('http://document.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId + '&template=0&editType=2');
    //         // window.open('http://test.yx.mail.netease.com/written-doc/index.html#/editdocs?id='+ this.flowData.flowNodeMessage.content.categoryId +'&template=0&editType=2')
    //     }else if(window.location.hostname==='dev.test.yx.mail.netease.com'){
    //         window.open('http://dev.test.yx.mail.netease.com/written-doc/index.html#/editdocs?id='+ this.flowData.flowNodeMessage.content.categoryId +'&template=0&editType=2')
    //     }else if(window.location.hostname==='yx.mail.netease.com'){
    //         window.open('http://yx.mail.netease.com/written-doc/index.html#/editdocs?id='+ this.flowData.flowNodeMessage.content.categoryId +'&template=0&editType=2')
    //     }else{
    //         window.open('http://test.yx.mail.netease.com/written-doc/index.html#/editdocs?id='+ this.flowData.flowNodeMessage.content.categoryId +'&template=0&editType=2')
    //     }
    // }
}
