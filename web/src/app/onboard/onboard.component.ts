import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Ajax } from '@shark/shark-angularX';
import { WorkFlowData, YXWorkFlowDetailBase } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';
import { UserService } from '../shared/services/user.service';

@Component({
    templateUrl: './onboard.component.html'
})
export class OnboardComponent implements YXWorkFlowDetailBase {
    workFlowData: WorkFlowData = null;
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };

    userInfo: any = {};

    fromEdit: number = 0; // 0: 非编辑页， 1：编辑页
    constructor(
        private userServ: UserService,
        private ajax: Ajax,
        private aRoute: ActivatedRoute
    ) {
        this.userServ.getUser().then((userInfo) => {
            this.userInfo = userInfo;
        });
        this.aRoute.params.subscribe((params) => {
            this.fromEdit = params.isEdit !== undefined ? Number(params.isEdit) : 0;
        });
    }
    ngOnInit() {
        document.title = '严选业务门户-入职工单';
    }
    ngOnDestroy() {
        document.title = '严选业务门户';
    }
    onFlowDataChange(workflowData: WorkFlowData) {
        this.workFlowData = workflowData;
        this.flowData = this.workFlowData.flowData || {
            flowMetaData: {},
            flowNodeMessage: {}
        };
    }

    showEdit() {
        const uid = this.userInfo.email;
        if (this.workFlowData.nodeId === '30010301') {
            // 工单创建页面
            return true;
        }
        if (this.fromEdit === 1) {
            // 如果有编辑页标识,需要判断当前用户是否是工单创建人，且工单是否是拒绝状态
            if (this.flowData.flowNodeMessage.businessStatus === -1 && uid === this.flowData.flowNodeMessage.createUser) {
                return true;
            }
        }
        return false;
    }

    showApprove() {
        const uid = this.userInfo.email;
        if ((this.flowData.flowNodeMessage.acceptorList || []).includes(uid)) {
            return true;
        } else {
            return false;
        }
    }
}
