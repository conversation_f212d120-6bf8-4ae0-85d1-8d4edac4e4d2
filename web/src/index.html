<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>严选业务门户</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta content="yes" name="apple-touch-fullscreen" />
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="Author" content="netease">
    <meta name="Version" content="1.0">
    <link rel="shortcut icon" href="//you.163.com/favicon.ico?r=gold" type="image/x-icon">
    <script>
        window.__puzzle_env = location.hostname === 'yx.mail.netease.com' ? 'prod' : 'test';
        !function (e, n, t, s, c) { if (!e[s]) { var r = void 0 !== t && t.resolve, a = e[s]; (a = e[s] = function () { this.modules = {} }).callbacks = [], a.ready = r ? function () { return a.instance ? t.resolve(a.instance.vars()) : new t(function (e) { return a.callbacks.push(e) }) } : function (e) { return a.instance ? e(a.instance.vars()) : a.callbacks.push(e) }; var i = n.createElement(c), u = n.getElementsByTagName(c)[0]; i.async = !0, i.src = "https://yanxuan-sdk.nosdn.127.net/yanxuan-puzzle/puzzle@000042" + (window.__puzzle_env === 'test' ? '-test' : '') + ".js", u.parentNode.insertBefore(i, u) } }(window, document, window.Promise, "puzzle", "script");
    </script>
</head>

<body>
    <app-root id="root"></app-root>
</body>

</html>