import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BpmFlowNames } from '../config';
import { SharedModule } from '../shared/shared.module';
import { PermApproveComponent } from './approve.component';
import { PermComponent } from './perm.component';
import { PermDetailComponent } from './detail.component';
import { PermEditComponent } from './edit.component';
import { ExpansionModule } from '@shark/expansion';
import { YXWorkFlowModule } from '@eagle/workflow';

// 定义常量 路由
const routes: any = [
    {
        path: 'workflow/permDetail',
        component: PermComponent
    }
];

@NgModule({
    imports: [
        ExpansionModule,
        SharedModule,
        YXWorkFlowModule,
        RouterModule.forChild(routes)
    ],
    declarations: [
        PermComponent,
        PermEditComponent,
        PermDetailComponent,
        PermApproveComponent
    ],
    entryComponents: [PermComponent]
})
export class PermModule {
    constructor() {
    }
}
