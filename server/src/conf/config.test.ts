import { ITigerProxyOption } from '@tiger/proxy';
import { join } from 'path';
import BaseConfig from './config.base';
import { AppModuleConfig } from './types';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
    loggerPath: string = join('/home/<USER>/', this.serviceCode);

    // 产品相关信息
    productDomain = 'test.yx.mail.netease.com';
    productPort = 80; // null表示使用默认端口

    // 权限中心服务域名
    iusDomain = 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas';

    // cmdb服务域名
    cmdbDomain = 'http://127.0.0.1:8550/proxy/test-v2.yanxuan-cmdb-api.service.mailsaas/api/v2';

    // 严选消息通知服务相关配置
    notifyUrl = `http://127.0.0.1:8550/proxy/test.yanxuan-msg.service.mailsaas/notify/${this.productCode}/multi/send.json`;

    // 邮件模版服务相关配置
    templateDomain = 'http://127.0.0.1:8550/proxy/test.yanxuan-template.service.mailsaas';

    umcProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(new RegExp(`^${this.contextPath}${this.xhrPrefix}/userCenterManage`), '');
        }
    };

    icacProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(new RegExp(`^${this.contextPath}${this.xhrPrefix}/icac`), '');
        }
    };
    reminders: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^/written-doc/xhr/proxy`
                ),
                ''
            );
        }
    }
    umcPermProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.umc-permission.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(new RegExp(`^${this.contextPath}/xhr/umc-permission/xhr`), '');
        }
    };

    // cmdb转发
    cmdbProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test-v2.yanxuan-cmdb.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(`${this.contextPath}${this.xhrPrefix}/cmdbProduct`, '');
        }
    };

    authedSecondLevelPosIds = {
        depature: [190, 170],
        onboard: [190, 170, 216],
        transfer: [190, 170],
        perm: [190, 170],
        permChild: [190, 170]
    };

    // app自己的转发配置，需要开发自己改成自己应用的 TODO
    appProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-nav-bflow.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(`${this.xhrPrefix}/api`, '');
        }
    };

    flowServerOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/flowServer`
                ),
                ''
            );
        }
    };

    bpmOrderOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-bpmorder.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/bpmOrder`
                ),
                ''
            );
        }
    };

    modules: AppModuleConfig = {
        '@tiger/security': {
            enable: true,
            options: {
                'csrf': true,
                'Strict-Transport-Security': true,
                'X-Frame-Options': true
            }
        },
        '@tiger/swagger': {
            enable: false
        }
    };
}
