import { Service } from '@tiger/boot';
import { AppConfig } from '@tiger/core';

export const topologyName = 'nav_personnelchange_onboard';

/**
 * 当前流程的配置文件
 */
@Service
export default class Config {
    /**
     * 应用名称
     */
    appName: string = AppConfig.appName;
    /**
     * 流程拓扑ID
     */
    topologyName: string = topologyName;
    /**
     * 邮件链接审批的节点
     * 指导员审批、代理人审批、二级部门负责人审批
     */
    linkApproveNodeIds: string[] = ['30010304', '30010302', '30010303'];
    /**
     * 链接审批加密密码
     */
    linkApprovePassword: string = 'approveemailpassword';
}
