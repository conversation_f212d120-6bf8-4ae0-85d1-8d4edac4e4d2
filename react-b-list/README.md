# Getting Started With tool

This repository is a basic Schematic implementation that serves as a starting point to create and publish Tools to NPM.

### Testing

To test locally, install `@muses/cli` globally and use the `muse tool` command line tool. That tool acts the same as the `generate` command of the muse CLI, but also has a debug mode.

Check the documentation with

```bash

muse tool --help

```
### Publishing

To publish, simply do:

```bash
npm run build
npm publish
```

That's it!
 