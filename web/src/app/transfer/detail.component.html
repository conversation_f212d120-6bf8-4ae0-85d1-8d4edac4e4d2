<div class="section" [ngClass]="{'section-fold': !switch}">
    <div class="section-header section-header-bordered" (click)="switch = !switch" style="cursor: pointer;">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block" *ngIf="switch">
        <form class="form form-inline">
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        工单编号：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowMetaData?.globalId}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        工单类型：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowMetaData?.topologyName | toponamepipe}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8 word-break">
                        申请人：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.applerName}} {{flowData?.flowNodeMessage?.applerUid}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        角色类型：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowNodeMessage?.applerRoleType | employeeRoleTypePipe}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        员工类型：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowNodeMessage?.applerType | employeeTypePipe}}
                    </div>
                </div>

                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        职责描述：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.dutyDesc}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4 word-break">
                        所在组织架构：
                    </div>
                    <div class="form-item-text col-20 word-break">
                        <div *ngFor="let orgPos of originOrgPosList">{{orgPos}}</div>
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        转出组织架构：
                    </div>
                    <div class="form-item-text col-20 word-break">
                        <div *ngFor="let orgPos of transferOrgPosList">{{orgPos}}</div>
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        转入组织架构：
                    </div>
                    <div class="form-item-text col-20 word-break">
                        {{flowData?.flowNodeMessage?.orgPosPath}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        转岗类型:
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.transferType | transferType}}
                    </div>
                </div>
            </div>

            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        转岗说明:
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.remark}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained" *ngIf="flowData?.flowNodeMessage?.businessStatus === -1 && userInfo?.uid === flowData?.flowNodeMessage?.createUser">
                <div class="form-item col-12">
                    <button class="btn btn-primary" (click)="goEditOrder()">编辑</button>
                </div>
            </div>
        </form>
    </div>
</div>