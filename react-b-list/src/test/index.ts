import { apply, applyTemplates, chain, mergeWith, move, Rule, ToolContext, Tree, url } from '@muses/cli/tools';
import { Schema } from './schema';


export function Test(options: Schema): Rule {
  return (tree: Tree, context: ToolContext) => {

    // redner 模板
    const source = apply(url(`./files/`),
      [
        applyTemplates({
          ...options,
        }),
        move(options.name || './')
      ])

    // do sth before render files template
    const sth = () => {
      console.log('do sth ting before')
      context.logger.info(tree.root.path)
    }

    return chain([
      () => {
        sth();
      },
      mergeWith(source),
      // do sth after render files template
      (tree) => {
        tree.create('tree-create.js', 'dddd')
      }
    ])
  };
}
