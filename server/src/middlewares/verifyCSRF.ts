import { Environment } from '@tiger/core';
import filter from '@tiger/filter';
import Boom = require('boom');
import { Context } from 'koa';
import { env } from '../conf';

export const verifyCSRF = filter(
    async (ctx: any, next: () => Promise<null>) => {
        if ('GET' !== ctx.method && env !== Environment.dev.code) {
            const tokenFromCookie = ctx.cookies.get('YX_CSRF_TOKEN');
            const tokenFromHeader = ctx.headers['Yx-Csrf-Token'.toLowerCase()];
            if (tokenFromHeader !== tokenFromCookie) {
                throw Boom.forbidden('CSRF 校验失败');
            } else {
                await next();
            }
        } else {
            await next();
        }
    }
);
