import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { CanActiveGuard } from '@yx-module/umc-manage';

import { SharedModule } from '../shared/shared.module';
import { WorkFlowCreateComponent } from './create/create.component';
// import { CreateListComponent } from './list/created/list.component';
import { CreatedListComponent } from './list/create/list.component';
import { DealedListComponent } from './list/dealed/list.component';
import { ApproveModal } from './list/dealing/approve.modal';
import { uploadTainInfoComponent } from './list/uploadTainInfo/uploadTainInfo.component';
import { DealingListComponent } from './list/dealing/list.component';
import { MonitorListComponent } from './list/monitor/list.component';
import { TrailerListComponent } from './list/trailer/list.component';
import { DocListComponent } from './list/doc/docList.component';
import { TrainListComponent } from './list/train/trainList.component';
import { UserInfoModal } from './../userCenter/modal/userInfoModal/userInfo.modal';
import { PermDetailModal } from './../userCenter/modal/permDetailModal/permDetail.modal';
import { DealingTipModal } from './list/dealingTipModal/dealingTipModal.component';
import { UrgeBtn } from './components/urgeBtn/urgeBtn.component';
import { WithdrawBtn } from './components/withdrawBtn/withdrawBtn.component';
import { FailModal } from './list/dealing/fail.modal'
import { ExternalBatchApproveModal } from './list/dealing/externalBatchApprove.modal';

const routes: Routes = [
    {
        component: WorkFlowCreateComponent,
        path: 'workflow/createFlow'
    },
    {
        component: CreatedListComponent,
        path: 'workflow/createdList'
    },
    {
        component: DealedListComponent,
        path: 'workflow/dealedList'
    },
    {
        component: DealingListComponent,
        path: 'workflow/dealingList'
    },
    {
        component: TrailerListComponent,
        path: 'workflow/trailerList'
    },
    {
        component: MonitorListComponent,
        path: 'workflow/monitorList',
        canActivate: [CanActiveGuard]
    },
    {
        component: DocListComponent,
        path: 'workflow/docList'
    },
    {
        component: TrainListComponent,
        path: 'workflow/trainList'
    },
];
const modals: any[] = [ApproveModal, uploadTainInfoComponent, UserInfoModal, PermDetailModal, DealingTipModal, FailModal, ExternalBatchApproveModal];

const components = [...modals, UrgeBtn, WithdrawBtn, CreatedListComponent, DealedListComponent,
    DealingListComponent, TrailerListComponent, MonitorListComponent, WorkFlowCreateComponent, DocListComponent, TrainListComponent];

@NgModule({
    imports: [
        SharedModule,
        RouterModule.forChild(routes)
    ],
    declarations: [...components],
    entryComponents: [...modals]
})
export class WorkflowModule {
    constructor() {

    }
}
