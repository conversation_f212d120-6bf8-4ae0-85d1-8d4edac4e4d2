import { Component, Input } from '@angular/core';
import { SharkModalService, SharkToastrService, Ajax, Cookie } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';

@Component({
  selector: 'urge-btn',
  templateUrl: './urgeBtn.component.html'
})

export class UrgeBtn {
  @Input() data: any = {}; // 工单数据
  @Input() disabled: boolean = false; // 是否禁用按钮
  @Input() sucCb: any; // 成功回调
  @Input() parent: any; // 父组件实例
  show: boolean = false; // 是否展示

  constructor(
    private sharkModalService: SharkModalService,
    private sharkToastrService: SharkToastrService,
    private ajax: Ajax,
    private cookie: Cookie,
  ) {
  }

  ngOnInit() {
    this.configShowStatus();
  }

  // 初始化展示状态
  configShowStatus() {
    const { flowMetaData, flowNodeMessage } = this.data;
    const userUid = this.cookie.getCookie('yx_username');
    const { status } = flowMetaData || { status: 100 };
    const { nodeId, createUser } = flowNodeMessage || { nodeId: '', createUser: '' };
    // 成功和取消
    if (nodeId !== '9999' && status !== -1 && createUser === userUid) {
      this.show = true;
    }
  }

  // 催办操作
  onUrge() {
    this.sharkModalService.confirm({
      title: '确定发起催办吗？一小时内只能发起一次催办哦',
      okText: '确定',
      cancelText: '取消'
    }).then(() => {
      const { flowNodeMessage } = this.data;
      const {
        nodeId,
        flowId
      } = flowNodeMessage || { flowId: '', nodeId: '' };
      this.ajax.get(AjaxUrl.createFlow.urge, { flowId, nodeId }).then((res) => {
        this.parent.getItemList();
        this.sharkToastrService.success('操作成功')
      }, () => {
        this.sharkToastrService.error('操作失败')
      })
    });
  }
}