import { MpsPayload } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { isAuthedThirdLevelOrg } from '../../../service/permcenter/approvePerm';
import { topologyName } from '../transfer-config';
import Workflow from '../transfer-workflow';
import { disassociateUser, lockUser, moveUser } from '../transfer.service';

export interface Notify {
    payload: string;
}
/**
 * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
 */
export type NotifyBody = Notify[];

export type NotifyResult = AjaxResult<string>;

const system = { email: 'system', fullname: '系统' };
/**
 * 工单通知接口
 */
@RestController
@RequestMapping(`/workflow/${topologyName}`)
export class TransferWorkflowNotifyController {

    constructor(private workflow: Workflow) { }

    /**
     * 流程流转通知
     * [{"payload":"{\"flowId\": \"4668724\",\"nodeId\":\"30019902\"}"}]
     */
    @PostMapping('/notify.json')
    async notify(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body;
        for (const message of messages) {
            const payload: MpsPayload = JSON.parse(message.payload);

            const { flowMetaData, flowNodeMessage } = await this.workflow.getFlowDataTriggerByMps(payload);
            const orgPosId = flowNodeMessage.orgPosId;
            const level = flowNodeMessage.level;
            const originOrgPos = flowNodeMessage.originOrgPos;
            const transferOrgPos: any[] = flowNodeMessage.transferOrgPos;
            const thirdPosId = transferOrgPos.find((org) => originOrgPos[org].level === 97);
            if ((flowMetaData.currentNodeId === '30010403' && thirdPosId && await isAuthedThirdLevelOrg('transfer', 97, thirdPosId)) || (flowMetaData.currentNodeId === '30010405' && await isAuthedThirdLevelOrg('transfer', level, orgPosId))) {
                await this.workflow.submit({
                    fullname: flowNodeMessage.acceptorNameList[0],
                    email: flowNodeMessage.acceptorList[0]
                }, flowMetaData, {
                        ...flowNodeMessage,
                        operateRemark: '自动审批通过',
                        approved: true
                    });
                return ctx.body = AjaxResult.success();
            }
            //  TODO 这里可以做些什么
            // 更新工单数据
            await this.workflow.update(flowMetaData, flowNodeMessage, system);

            const createrKeywords = '您的工单状态发生变更';
            // 单子被拒绝，通知创建人

            await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, createrKeywords);

            const keywords = '入职申请';
            // 否则，通知审批人
            await this.workflow.notifyApprover(flowMetaData, flowNodeMessage, keywords);

        }
        return ctx.body = AjaxResult.success();

    }

    /**
     * 流程结束通知
     * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
     */
    @PostMapping('/notifyEnd.json')
    async notifyEnd(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body[0];
        const payload: MpsPayload = JSON.parse(messages.payload);
        let flowMetaData = null;
        let flowNodeMessage = null;
        try {
            const flowData = await this.workflow.getFlowDataTriggerByMps(payload);
            flowMetaData = flowData.flowMetaData;
            flowNodeMessage = flowData.flowNodeMessage;
            // 更新业务状态
            flowNodeMessage.businessStatus = 1;
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
        } catch (e) {
            console.error('入职申请工单，结束节点数据更新失败', e);
            return ctx.body = AjaxResult.internal('工单更新失败');
        }
        // 业务处理

        try {
            // 调用ius的接口，更新用户转岗数据数据
            const results = await moveUser({
                originOrgPosIds: flowNodeMessage.transferOrgPos,
                targetOrgPosId: flowNodeMessage.orgPosId,
                uid: flowNodeMessage.applerUid,
                targetType: flowNodeMessage.applerRoleType,
                duty: flowNodeMessage.dutyDesc,
                staffStatus: flowNodeMessage.applerType
            });
            let error = false;
            for (const res of results) {
                if (res.data.code !== 200) {
                    // error
                    error = true;
                    console.error(`moveUser error: ${res.request.path}: `, res.data.errorCode);
                } else {
                    console.info(`moveUser success: ${res.request.path}`);
                }
            }
            if (error) {
                return ctx.body = AjaxResult.internal('转岗失败');
            }
            if (flowNodeMessage.transferType === 1) {
                console.info(`${flowNodeMessage.applerUid}-转岗类型${flowNodeMessage.transferType}，清理权限。`);
                // 将用户与各个产品中的角色取消关联
                await disassociateUser({
                    uid: flowNodeMessage.applerUid
                });
                // 锁定用户在各个产品中的状态
                await lockUser({
                    uid: flowNodeMessage.applerUid
                });
            } else {
                console.info(`${flowNodeMessage.applerUid}-转岗类型${flowNodeMessage.transferType}，权限不变。`);
            }
        } catch (e) {
            console.error(`${flowNodeMessage.applerUid}-转岗申请工单申请完成后，更新用户信息失败`, e);
            return ctx.body = AjaxResult.internal('更新用户信息失败');
            return;
        }

        const keywords = '审批结束';
        // 通知创建人
        await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, keywords);
        // 通知跟踪人
        // await this.workflow.notifyTrailer(flowMetaData, flowNodeMessage, '工单已经结束');
        ctx.body = AjaxResult.success();
    }

}
