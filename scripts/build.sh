#!/bin/bash
set -o errexit

npm run web:build && 
npm run server:build &&
cp deploy/env/setenv_dev.sh server/dist/setenv.sh &&
mkdir -p server/dist/web && 
cp -rf web/build/app server/dist/web &&
mkdir -p server/web &&
cp -rf web/build/app server/web &&
cp -rf web/build/mimg server/web &&
cp -rf web/build/mimg server/dist/web && 
cp server/package.json server/dist &&
cd server/dist && npm install --production --registry http://npm.mail.netease.com/registry/
