import { IAppConfig, IPlugin, NodeEnv } from '@tiger/core';
import { ITigerProxyOption } from '@tiger/proxy';
import { join } from 'path';
import { AppModuleConfig } from './types';

const rootPath = join(__dirname, '../../');
const contextPath = '/bflow-base';
const xhrPrefix = '/xhr';
const productCode = 'navAdmin';
const productId = 3001;
const appName = 'yanxuan-portal-base';
const serviceCode = 'yanxuan-portal-base';

export default abstract class BaseConfig implements IAppConfig {
    // 应用的名字
    appName: string = appName;
    // contextPath，一般表示应用域名的后面一位，比如,yx.mail.netease.com/ape，contextPath就是ape
    contextPath: string = contextPath;
    // 一般将接口进行前缀区分，一般是/xhr
    xhrPrefix: string = xhrPrefix;
    // 应用研发工作台申请的productCode，申请地址：http://yx.mail.netease.com/ape/#/edit/project?from=new
    productCode: string = productCode;
    productId: number = productId;
    plugins: IPlugin[] = [];
    // cmdb service code
    serviceCode: string = serviceCode;
    // 使用apollo配置中心的时候用
    // @Value('logPath', '/home')
    // logPath!: string;

    viewPath: string = join(rootPath, 'views');
    // 应用的根目录
    rootPath: string = rootPath;
    // 日志的目录
    abstract loggerPath: string;
    // 前端index.html,ico,fonts等资源目录
    webAppPath: string = join(rootPath, 'web/app');
    // 前端静态资源的存放目录
    webStaticPath: string = join(rootPath, 'web/mimg');
    // server监听端口
    port: number = 8080;
    // 环境
    env: string = NodeEnv.Current.env.code;

    // app自己的转发配置，需要开发自己改成自己应用的 TODO
    abstract appProxyOptions: ITigerProxyOption;
    // 权限中心的转发配置
    abstract umcProxyOptions: ITigerProxyOption;

    // 产品相关信息
    abstract productDomain: string;
    abstract productPort: number;

    // 权限中心服务域名
    abstract iusDomain: string;

    // cmdb服务域名
    abstract cmdbDomain: string;

    // 严选消息通知服务相关配置
    abstract notifyUrl: string;

    // 邮件模版服务相关配置
    abstract templateDomain: string;

    // 权限中心
    abstract icacProxyOptions: ITigerProxyOption;

    // workFlow
    abstract flowServerOptions: ITigerProxyOption;

    // bpmOrder
    abstract bpmOrderOptions: ITigerProxyOption;

    abstract umcPermProxyOptions: ITigerProxyOption;
    // cmdb转发
    abstract cmdbProxyOptions: ITigerProxyOption;
    // 催办转发
    abstract reminders: ITigerProxyOption;
    
    abstract authedSecondLevelPosIds: {
        'depature': number[];
        'onboard': number[];
        'transfer': number[];
        'perm': number[]
    };

    // 外部模块配置
    modules: AppModuleConfig = {
        '@tiger/security': {
            enable: true,
            options: {
                'csrf': true,
                'Strict-Transport-Security': true,
                'X-Frame-Options': true
            }
        },
        '@tiger/swagger': {
            enable: false,
            options: {
                appModule: join(__dirname, '../modules/index.ts'),
                swaggerDefinition: {
                    host: 'local.yx.mail.netease.com'
                }
            }
        }
    };
}
