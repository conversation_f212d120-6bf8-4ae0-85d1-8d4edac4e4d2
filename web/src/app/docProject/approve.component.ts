import { Component, Input, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './approve.component.html',
    selector: 'flow-doc-authority-projectapprove'
})

export class DocProjectApproveComponent {
    @ViewChild('form') form;
    @Input() workFlowData: WorkFlowData;
    approveData: any = {
        approved: true,
        operateRemark: ''
    };
    switch: boolean = true;
    submiting: boolean = false;
    approveChoices: Array<{
        label: string,
        value: boolean,
        disabled?: boolean
    }> = [{
        label: '通过',
        value: true
    }, {
        label: '不通过',
        value: false
    }];
    topologyId:string = '';
    constructor(
        private ajax: Ajax,
        private toastr: SharkToastrService,
        private router: Router,
        private route: ActivatedRoute
    ) {
        this.route.params.subscribe((params) => {
            console.log(params);
            this.topologyId = params.topologyId;
        });
        console.log(this.topologyId);
    }
    setSwitch() {
        this.switch = !this.switch;
    }
    async save() {
        if (this.topologyId === 'written_doc_project'){
            this.form.submit(async () => {
                try {
                    this.submiting = true;
                    await this.ajax.postByJson(AjaxUrl.docManagement.submitTrain, {
                        flowMetaData: this.workFlowData.flowData.flowMetaData,
                        flowNodeMessage: {
                            ...this.approveData
                        }
                    });
                    this.submiting = false;
                    this.router.navigate(['/workflow/dealingList']);
                } catch (e) {
                    this.submiting = false;
                    this.toastr.error(`审批失败:${e.errorCode}`);
                }
            });
        } else if (this.topologyId === 'written_doc_delete_project'){
            this.form.submit(async () => {
                try {
                    this.submiting = true;
                    await this.ajax.postByJson(AjaxUrl.docManagement.submitTrainDel, {
                        flowMetaData: this.workFlowData.flowData.flowMetaData,
                        flowNodeMessage: {
                            ...this.approveData
                        }
                    });
                    this.submiting = false;
                    this.router.navigate(['/workflow/dealingList']);
                } catch (e) {
                    this.submiting = false;
                    this.toastr.error(`审批失败:${e.errorCode}`);
                }
            });
        }
    }
}
