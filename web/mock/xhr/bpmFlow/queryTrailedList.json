{"code": 200, "data": {"pagination": {"page": 1, "size": 10, "total": 148, "totalPage": 15, "offset": 0}, "result": [{"flowMetaData": {"product": "ius", "createTime": 1599223944949, "globalId": "IUS_SPECIAL_1599223915033", "topologyId": "ius_personal_special_rights_child", "updateTime": 1599224038609, "nodeId": "30110201", "flowId": "10698859", "version": 4, "latest": true, "status": 200, "topologyName": "ius_personal_special_rights_child", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, "businessStatus": 2, "createUserName": "胡尧", "haveChildFlow": false, "currUserName": "胡尧", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "applyRoleList": [{"expireTime": 0, "roleId": 7, "roleName": "xxxx"}], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}], "thirdOwnerTrackList": ["<EMAIL>"], "acceptorNameList": [], "acceptorList": [], "roleList": [{"expireTime": 0, "roleId": 3, "roleName": "12345"}, {"expireTime": 0, "roleId": 4, "roleName": "中台架构师"}, {"expireTime": 0, "roleId": 5, "roleName": "超级管理员"}, {"expireTime": 0, "roleId": 9, "roleName": "普通测试用户"}, {"expireTime": 0, "roleId": 7, "roleName": "xxxx"}], "delRoleList": []}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "dd", "acceptorNameList": ["胡尧", "张佳俊"], "acceptorList": ["<EMAIL>", "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "nodeId": "9999", "flowId": "10698859", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599223944948, "globalId": "IUS_SPECIAL_1599223915033", "topologyId": "ius_personal_special_rights_child", "updateTime": 1599223946086, "nodeId": "0", "flowId": "10698865", "version": 0, "latest": true, "status": 0, "topologyName": "ius_personal_special_rights_child", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}, "businessStatus": 1, "createUserName": "胡尧", "haveChildFlow": true, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": ["丁应应"], "acceptorList": ["<EMAIL>"], "nodeId": "30110201", "flowId": "10701185", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599223915804, "globalId": "IUS_SPECIAL_1599223915033", "topologyId": "ius_personal_special_rights", "updateTime": 1599223944750, "nodeId": "30110104", "flowId": "10701185", "version": 4, "latest": true, "status": 200, "topologyName": "ius_personal_special_rights", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, "businessStatus": 0, "createUserName": "胡尧", "haveChildFlow": true, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "111"}, {"applySystem": "cradle-test-new", "applySystemName": "cradle-test-new", "applySystemDesc": "2222"}]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": ["丁应应"], "acceptorList": ["<EMAIL>"], "nodeId": "30110102", "flowId": "10701185", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599001208092, "nextNodeList": ["30110302"], "globalId": "ENRTY_1599001207201", "topologyId": "ius_entry_apply_topology", "updateTime": 1599001209494, "nodeId": "30110301", "flowId": "10678268", "version": 0, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30110302"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 0, "businessObject": {"applyType": 0, "uid": "<EMAIL>", "employeeNum": "HZS16794", "employeeType": "02", "leaderName": "杨其鑫", "phoneNum": "", "oaDepartment": "D103002030003", "remark": "系统自动发起", "leaderUid": "<EMAIL>", "oaState": 1, "username": "夏敏"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "operateRemark": "第一个节点自动通过", "acceptorNameList": ["夏敏"], "conditionParamMap": {"applyType": 0, "employeeNum": "HZS16794", "code": "entry_create", "phoneNum": "", "oaDepartment": "D103002030003", "remark": "系统自动发起", "uid": "<EMAIL>", "employeeType": "02", "operateResult": "agree", "leaderName": "杨其鑫", "leaderUid": "<EMAIL>", "oaState": 1, "username": "夏敏"}, "acceptorList": ["<EMAIL>"], "nodeId": "30110301", "flowId": "10678268"}}, {"flowMetaData": {"product": "ius", "createTime": 1598997607971, "nextNodeList": ["30110302"], "globalId": "ENRTY_1598997607122", "topologyId": "ius_entry_apply_topology", "updateTime": 1598997609351, "nodeId": "30110301", "flowId": "10677877", "version": 0, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30110302"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 0, "businessObject": {"applyType": 0, "uid": "<EMAIL>", "employeeNum": "H12195", "employeeType": "01", "leaderName": "朴元日", "phoneNum": "", "oaDepartment": "D103002029007", "remark": "系统自动发起", "leaderUid": "<EMAIL>", "oaState": 0, "username": "邵诚凯"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "operateRemark": "第一个节点自动通过", "acceptorNameList": ["邵诚凯"], "conditionParamMap": {"applyType": 0, "employeeNum": "H12195", "code": "entry_create", "phoneNum": "", "oaDepartment": "D103002029007", "remark": "系统自动发起", "uid": "<EMAIL>", "employeeType": "01", "operateResult": "agree", "leaderName": "朴元日", "leaderUid": "<EMAIL>", "oaState": 0, "username": "邵诚凯"}, "acceptorList": ["<EMAIL>"], "nodeId": "30110301", "flowId": "10677877"}}, {"flowMetaData": {"product": "ius", "createTime": 1598994008133, "nextNodeList": ["30110302"], "globalId": "ENRTY_1598994007207", "topologyId": "ius_entry_apply_topology", "updateTime": 1598994009640, "nodeId": "30110301", "flowId": "10674548", "version": 0, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30110302"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 0, "businessObject": {"applyType": 0, "uid": "<EMAIL>", "employeeNum": "H23102", "employeeType": "01", "leaderName": "王姜维", "phoneNum": "", "oaDepartment": "D103006064004", "remark": "系统自动发起", "leaderUid": "<EMAIL>", "oaState": 0, "username": "马凌"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "operateRemark": "第一个节点自动通过", "acceptorNameList": ["马凌"], "conditionParamMap": {"applyType": 0, "employeeNum": "H23102", "code": "entry_create", "phoneNum": "", "oaDepartment": "D103006064004", "remark": "系统自动发起", "uid": "<EMAIL>", "employeeType": "01", "operateResult": "agree", "leaderName": "王姜维", "leaderUid": "<EMAIL>", "oaState": 0, "username": "马凌"}, "acceptorList": ["<EMAIL>"], "nodeId": "30110301", "flowId": "10674548"}}, {"flowMetaData": {"product": "ius", "createTime": 1598990408178, "nextNodeList": ["30110302"], "globalId": "ENRTY_1598990407392", "topologyId": "ius_entry_apply_topology", "updateTime": 1598990409769, "nodeId": "30110301", "flowId": "10677097", "version": 0, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30110302"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 0, "businessObject": {"applyType": 0, "uid": "<EMAIL>", "employeeNum": "H23053", "employeeType": "01", "leaderName": "尹俊", "phoneNum": "", "oaDepartment": "D103006064005", "remark": "系统自动发起", "leaderUid": "<EMAIL>", "oaState": 0, "username": "陆军军"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "operateRemark": "第一个节点自动通过", "acceptorNameList": ["陆军军"], "conditionParamMap": {"applyType": 0, "employeeNum": "H23053", "code": "entry_create", "phoneNum": "", "oaDepartment": "D103006064005", "remark": "系统自动发起", "uid": "<EMAIL>", "employeeType": "01", "operateResult": "agree", "leaderName": "尹俊", "leaderUid": "<EMAIL>", "oaState": 0, "username": "陆军军"}, "acceptorList": ["<EMAIL>"], "nodeId": "30110301", "flowId": "10677097"}}, {"flowMetaData": {"product": "ius", "createTime": 1598986808061, "nextNodeList": ["30110302"], "globalId": "ENRTY_1598986807166", "topologyId": "ius_entry_apply_topology", "updateTime": 1598986809680, "nodeId": "30110301", "flowId": "10676663", "version": 0, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30110302"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 0, "businessObject": {"applyType": 0, "uid": "<EMAIL>", "employeeNum": "H23440", "employeeType": "01", "leaderName": "张伊炜", "phoneNum": "", "oaDepartment": "D103001091006", "remark": "系统自动发起", "leaderUid": "<EMAIL>", "oaState": 0, "username": "梁李江"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "operateRemark": "第一个节点自动通过", "acceptorNameList": ["梁李江"], "conditionParamMap": {"applyType": 0, "employeeNum": "H23440", "code": "entry_create", "phoneNum": "", "oaDepartment": "D103001091006", "remark": "系统自动发起", "uid": "<EMAIL>", "employeeType": "01", "operateResult": "agree", "leaderName": "张伊炜", "leaderUid": "<EMAIL>", "oaState": 0, "username": "梁李江"}, "acceptorList": ["<EMAIL>"], "nodeId": "30110301", "flowId": "10676663"}}, {"flowMetaData": {"product": "ius", "createTime": 1598983207869, "nextNodeList": ["30110302"], "globalId": "ENRTY_1598983207030", "topologyId": "ius_entry_apply_topology", "updateTime": 1598983209315, "nodeId": "30110301", "flowId": "10676187", "version": 0, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30110302"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 0, "businessObject": {"applyType": 0, "uid": "<EMAIL>", "employeeNum": "H23175", "employeeType": "01", "leaderName": "许翔", "phoneNum": "", "oaDepartment": "D103004049001", "remark": "系统自动发起", "leaderUid": "<EMAIL>", "oaState": 0, "username": "雷蕾"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "operateRemark": "第一个节点自动通过", "acceptorNameList": ["雷蕾"], "conditionParamMap": {"applyType": 0, "employeeNum": "H23175", "code": "entry_create", "phoneNum": "", "oaDepartment": "D103004049001", "remark": "系统自动发起", "uid": "<EMAIL>", "employeeType": "01", "operateResult": "agree", "leaderName": "许翔", "leaderUid": "<EMAIL>", "oaState": 0, "username": "雷蕾"}, "acceptorList": ["<EMAIL>"], "nodeId": "30110301", "flowId": "10676187"}}, {"flowMetaData": {"product": "ius", "createTime": 1598968082261, "nextNodeList": [], "globalId": "ENRTY_1598968081430", "topologyId": "ius_entry_apply_topology", "updateTime": 1598968110536, "nodeId": "30110303", "flowId": "10673000", "version": 3, "latest": true, "status": 200, "topologyName": "ius_entry_apply_topology", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 2, "code": "entry_create", "workOrderType": "entry_create", "businessStatus": 2, "createUserName": "丁应应", "currUserName": "胡尧", "businessObject": {"applyType": 1, "uid": "<EMAIL>", "employeeNum": "DS0429", "employeeType": 2, "identityList": "[{\"department\":306,\"departmentName\":\"测试部门\",\"post\":\"\",\"postName\":\"\",\"level\":98}]", "phoneNum": "***********", "remark": "申请入职", "state": "在职", "username": "丁应应"}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "conditionParamMap": {"applyType": 1, "uid": "<EMAIL>", "employeeNum": "DS0429", "employeeType": 2, "code": "entry_create", "operateResult": "agree", "identityList": "[{\"department\":306,\"departmentName\":\"测试部门\",\"post\":\"\",\"postName\":\"\",\"level\":98}]", "phoneNum": "***********", "remark": "申请入职", "state": "在职", "username": "丁应应"}, "nodeId": "30110303", "flowId": "10673000", "createUid": "<EMAIL>"}}]}}