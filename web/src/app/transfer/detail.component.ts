import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';

@Component({
    templateUrl: './detail.component.html',
    selector: 'transfer-detail'
})
export class TransferDetailComponent {
    @Input()
    userInfo: any = null;
    @Input()
    workFlowData: WorkFlowData = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };

    // 原始组织架构列表
    originOrgPosList: any[] = [];
    // 需要转出的组织架构
    transferOrgPosList: any[] = [];

    switch: boolean = true;
    constructor(
        private router: Router,
        private ajax: Ajax
    ) {

    }

    async ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
            this.initData();
        }
    }

    async initData() {
        this.setOriginOrgPosList();
        this.setTransferOrgPosList();
    }

    setOriginOrgPosList() {
        const originOrgPosMap = this.flowData.flowNodeMessage.originOrgPos || {};
        const originOrgList = [];
        for (const orgPosId in originOrgPosMap) {
            if (originOrgPosMap.hasOwnProperty(orgPosId)) {
                originOrgList.push(originOrgPosMap[orgPosId].name);
            }
        }
        this.originOrgPosList = originOrgList;
    }

    setTransferOrgPosList() {
        const originOrgPosMap = this.flowData.flowNodeMessage.originOrgPos || {};
        const transferOrgPosIds = this.flowData.flowNodeMessage.transferOrgPos || [];
        const transferOrgPosList = [];
        for (const orgPosId of transferOrgPosIds) {
            transferOrgPosList.push(originOrgPosMap[orgPosId].name);
        }
        this.transferOrgPosList = transferOrgPosList;
    }

    // 跳到编辑页
    goEditOrder() {
        this.router.navigate(['/workflow/detail', {
            topologyId: this.workFlowData.topologyId,
            flowId: this.workFlowData.flowId,
            isEdit: 1
        }]);
    }
}
