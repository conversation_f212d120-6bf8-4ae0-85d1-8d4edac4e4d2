
export class UtilService {

    constructor() {

    }

    /**
     * 参数转换
     * @param value paramValue
     * @param defaultValue default
     */
    toNumber(value: string, defaultValue: number = 0): number {
        if (typeof value === 'undefined') {
            return defaultValue;
        }
        try {
            return parseInt(value, 10);
        } catch (error) {
            return defaultValue;
        }
    }

    /**
     * 变量是否定义了
     * @param value paramValue
     */
    isDefined(value: any): boolean {
        if (typeof value === 'undefined') {
            return false;
        }
        return true;
    }
    goLogin() {
        window.location.href = 'https://yx.mail.netease.com/openid/login?url=' + encodeURIComponent(location.href);
    }
    /**
     * 所有列表页跳转时候，区分是业务系统权限还是其他，跳转不同路由
     */
    judgeRouter(topologyName) {
        if(topologyName === 'nav_personnelchange_perm' || topologyName === 'nav_personnelchange_perm_child') {
            return '/workflow/permDetail'
        } else {
            return '/workflow/detail'
        }
    }
}
