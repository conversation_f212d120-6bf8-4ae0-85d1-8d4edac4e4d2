import { NgModule } from '@angular/core';
import { YXWrokFlowConfig } from '@shark/shark-yx-workflow';

import { BpmFlowNames } from '../config';
import { SharedModule } from '../shared/shared.module';
import { DepartureApproveComponent } from './approve.component';
import { DepartureComponent } from './departure.component';
import { DepartureDetailComponent } from './detail.component';
import { DepartureEditComponent } from './edit.component';

@NgModule({
    imports: [SharedModule],
    declarations: [
        DepartureComponent,
        DepartureEditComponent,
        DepartureDetailComponent,
        DepartureApproveComponent
    ],
    entryComponents: [DepartureComponent]
})
export class DepartureModule {
    constructor() {
        YXWrokFlowConfig.addDetailComponent(BpmFlowNames.NAV_PERSONNELCHANGE_DEPARTURE, {
            component: DepartureComponent,
            excludeNodeIdList: ['30010210']
        });
    }
}
