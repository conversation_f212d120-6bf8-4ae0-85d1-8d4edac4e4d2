<form class="form  ng-untouched ng-pristine ng-valid" novalidate="" shark-validform #form="shark-validform">
    <div class="form-item">
        <label class="form-item-label col-4">是否通过申请<span class="icon-infocirlceo margin-l-1x" shark-tooltip
                [direction]="'bottom'" [template]="'用户申请的部分权限若不适合，则选择不通过予以打回，让用户重新提交申请'"></span>：</label>
        <div class="form-item-control col-20">
            <shark-radio-group [(model)]="approveData.approved" [radios]="approveChoices" shark-valid
                [validRule]="'required'"></shark-radio-group>
        </div>
    </div>
    <div class="form-item"
        *ngIf="approveData.approved && this.workFlowData.flowData.flowMetaData.currentNodeId === '30010517'">
        <label class="form-item-label col-4">开通的角色名称： </label>
        <div class="form-item-control col-6">
            <shark-textarea shark-valid [validRule]="'required'" [(ngModel)]="roleName" name="roleName">
            </shark-textarea>
        </div>
    </div>
    <div class="form-item" *ngIf="!approveData.approved">
        <label class="form-item-label col-4">备注信息：</label>
        <div class="form-item-control col-20">
            <textarea class="input input-textarea input-w-lg ng-untouched ng-pristine ng-valid" name="operateRemark"
                [(ngModel)]="approveData.operateRemark" placeholder="请输入备注信息" shark-valid
                [validRule]="'required'"></textarea>
        </div>
    </div>
    <div class="form-item" *ngIf="approveData.approved">
        <label class="form-item-label col-4">备注信息：</label>
        <div class="form-item-control col-20">
            <textarea class="input input-textarea input-w-lg ng-untouched ng-pristine ng-valid" name="operateRemark"
                [(ngModel)]="approveData.operateRemark" placeholder="请输入备注信息"></textarea>
        </div>
    </div>
    <div class="form-item">
        <div class="form-item-control col-offset-4 col-12">
            <button class="btn btn-primary" [disabled]="submiting" (click)="save()">确定</button>
        </div>
    </div>
</form>