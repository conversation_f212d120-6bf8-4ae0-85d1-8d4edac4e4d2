import { MpsPayload } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { isAuthedThirdLevelOrg } from '../../../service/permcenter/approvePerm';
import { topologyName } from '../onboard-config';
import Workflow from '../onboard-workflow';
import { addOrgPosUserByType } from '../onboard.service';

export interface Notify {
    payload: string;
}
/**
 * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
 */
export type NotifyBody = Notify[];

export type NotifyResult = AjaxResult<string>;

const system = { email: 'system', fullname: '系统' };
/**
 * 工单通知接口
 */
@RestController
@RequestMapping(`/workflow/${topologyName}`)
export class OnboardWorkflowNotifyController {

    constructor(private workflow: Workflow) { }

    /**
     * 流程流转通知
     * [{"payload":"{\"flowId\": \"4668724\",\"nodeId\":\"30019902\"}"}]
     */
    @PostMapping('/notify.json')
    async notify(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body;
        for (const message of messages) {
            const payload: MpsPayload = JSON.parse(message.payload);

            const { flowMetaData, flowNodeMessage } = await this.workflow.getFlowDataTriggerByMps(payload);
            if (flowMetaData.currentNodeId === '30010303' && await isAuthedThirdLevelOrg('onboard', flowNodeMessage.level, flowNodeMessage.orgPosId)) {
                await this.workflow.submit({
                    fullname: flowNodeMessage.acceptorNameList[0],
                    email: flowNodeMessage.acceptorList[0]
                }, flowMetaData, {
                        ...flowNodeMessage,
                        operateRemark: '自动审批通过',
                        approved: true
                    });
                return ctx.body = AjaxResult.success();
            }
            //  TODO 这里可以做些什么
            // 更新工单数据
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
            const createrKeywords = '您的工单状态发生变更';
            // 单子被拒绝，通知创建人
            await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, createrKeywords);

            const keywords = '入职申请';
            // 否则，通知审批人
            await this.workflow.notifyApprover(flowMetaData, flowNodeMessage, keywords, {
                html: 'bflow_workflow_perm_onboard_html',
                text: 'bflow_workflow_perm_onboard_txt'
            });

        }
        return ctx.body = AjaxResult.success();

    }

    /**
     * 流程结束通知
     * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
     */
    @PostMapping('/notifyEnd.json')
    async notifyEnd(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body[0];
        console.info('body:', ctx.request.body);
        let payload: MpsPayload;
        try {
            console.info('payload:', messages.payload);
            payload = JSON.parse(messages.payload);
        } catch (e) {
            console.error('payload parse error', e);
            return ctx.body = AjaxResult.internal('mps parse error');
        }
        let flowMetaData = null;
        let flowNodeMessage = null;
        try {
            const flowData = await this.workflow.getFlowDataTriggerByMps(payload);
            flowMetaData = flowData.flowMetaData;
            flowNodeMessage = flowData.flowNodeMessage;
            // 更新业务状态
            flowNodeMessage.businessStatus = 1;
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
        } catch (e) {
            console.error('入职申请工单，结束节点数据更新失败', e);
            return ctx.body = AjaxResult.internal('工单更新失败');
        }
        // 业务处理
        try {
            // 调用ius的接口，更新用户入职数据
            const addOrgParam = {
                orgInfo: {
                    uid: flowNodeMessage.applerUid,
                    orgPosId: flowNodeMessage.orgPosId,
                    realName: flowNodeMessage.applerName,
                    type: flowNodeMessage.applerRoleType,
                    duty: flowNodeMessage.dutyDesc,
                    staffStatus: flowNodeMessage.applerType
                },
                contactInfo: {
                    uid: flowNodeMessage.applerUid,
                    mobile: flowNodeMessage.applerPhoneNum
                }
            };
            await addOrgPosUserByType(addOrgParam);
        } catch (e) {
            console.error('入职申请工单申请完成后，更新用户信息失败', e);
            return ctx.body = AjaxResult.internal('更新用户信息失败');
        }

        const keywords = '审批结束';
        // 通知创建人
        await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, keywords);
        // 通知跟踪人
        // await this.workflow.notifyTrailer(flowMetaData, flowNodeMessage, '工单已经结束');
        ctx.body = AjaxResult.success();
    }

}
