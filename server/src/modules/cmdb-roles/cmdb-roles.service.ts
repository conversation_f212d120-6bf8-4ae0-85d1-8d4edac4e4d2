import { Service } from '@tiger/boot';
import { getServiceUrl } from '@tiger/core';
import axios from '@tiger/request';

const cmdbDomains = {
    dev: 'http://10.246.71.51:8300/api/v2',
    test: 'http://127.0.0.1:8550/proxy/test-v2.yanxuan-cmdb.service.mailsaas/api/v2',
    online: 'http://127.0.0.1:8550/proxy/online-v2.yanxuan-cmdb-api.service.mailsaas/api/v2'
};


@Service
export class Cmdbservice {

    async getRoles(uid: string) {
        const serviceUrl = getServiceUrl(cmdbDomains) + '/role/user';
        const res = await axios.get(serviceUrl, { params: { uid } });
        return res.data;
    }
}
