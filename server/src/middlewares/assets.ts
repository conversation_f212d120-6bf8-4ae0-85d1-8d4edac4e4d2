/**
 * @description
 *   此中间件的作用是处理index.html、前端静态资源的请求
 */

import Router from 'koa-router';
import send = require('koa-send');

import { Context } from 'koa';
import { contextPath, webAppPath, webStaticPath } from '../conf';

const router = new Router({
    prefix: contextPath
});

const index = async (ctx: any) => {
    await send(ctx, 'index.html', {
        root: webAppPath
    });
};

// 处理index.html
router.get('/', index);
router.get('/index.html', index);

// 处理静态资源，css、js、img。
router.get('/(css|img|js)/*', async (ctx: any, next) => {
    const contextPathReg = new RegExp(`^${contextPath}/`);
    const reqPath = ctx.path.replace(contextPathReg, '');
    await send(ctx, reqPath, {
        root: webStaticPath
    });
});

router.get('/favicon.ico', async (ctx: any, next) => {
    await send(ctx, 'favicon.ico', {
        root: webAppPath
    });
});

router.get('/fonts/*', async (ctx: any, next) => {
    const contextPathReg = new RegExp(`^${contextPath}/`);
    const reqPath = ctx.path.replace(contextPathReg, '');
    await send(ctx, reqPath, {
        root: webAppPath
    });
});

export default router.routes();
