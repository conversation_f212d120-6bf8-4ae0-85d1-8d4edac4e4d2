<div class="root">
    <div class="m-index">
        <div class="u-collapse" [ngClass]="{'collapsed': isFavFolded}">
            <div class="padding-top" (click)="isFavFolded = !isFavFolded">
                <p class="icon">
                    <a href="javascript:void(0)">我的收藏({{favAppList.length}})</a>
                    <span>{{isFavFolded ? '展开' : '收起'}}
                        <i [ngClass]="{'icon-down': isFavFolded, 'icon-up': !isFavFolded}"></i>
                        &nbsp;&nbsp;
                    </span>
                </p>
            </div>
            <div class="collapse-block">
                <tag-block *ngIf="favAppList.length > 0" [isFav]="true" [cols]="cols" [tagName]="" [appList]="favAppList"
                    (favClick)="$event.isFav ? rmFav($event) : addToFav($event)"></tag-block>
                <div *ngIf="isLoaded && favAppList.length === 0" class="m-fav-description">
                    <p class="text-error">还没有收藏过应用？可以点击应用图标右上角的收藏按钮，马上添加！</p>
                    <img src="//mailshark.nos-jd.163yun.com/document/static/9FAB3CFF9A10327D660405659EB1FAE6.jpg">
                </div>
            </div>
        </div>
        <div class="u-collapse" [ngClass]="{'collapsed': isFolded}">
            <div class="padding-top" (click)="isFolded = !isFolded">
                <p class="icon">
                    <a href="javascript:void(0)">业务门户</a>
                    <span>{{isFolded ? '展开' : '收起'}}
                        <i [ngClass]="{'icon-down': isFolded, 'icon-up': !isFolded}"></i>
                        &nbsp;&nbsp;
                    </span>
                </p>
            </div>
            <div class="collapse-block">
                <div *ngFor="let tag of tagList; ">
                    <tag-block *ngIf="tag.applications.length > 0" [cols]="cols" [tagName]="tag.name" [appList]="tag.applications"
                        (favClick)="$event.isFav ? rmFav($event) : addToFav($event)"></tag-block>
                </div>
            </div>
        </div>
    </div>
</div>