import { MpsPayload } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { isAuthedThirdLevelOrg } from '../../../service/permcenter/approvePerm';
import Config, { topologyName } from '../departure-config';
import Workflow from '../departure-workflow';
import { departureExtendOperate } from '../departure.service';

export interface Notify {
    payload: string;
}
/**
 * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
 */
export type NotifyBody = Notify[];

export type NotifyResult = AjaxResult<string>;

const system = { email: 'system', fullname: '系统' };
/**
 * 工单通知接口
 */
@RestController
@RequestMapping(`/workflow/${topologyName}`)
export class DepartureWorkflowNotifyController {

    constructor(
        private workflow: Workflow,
        private config: Config
    ) { }

    /**
     * 流程流转通知
     * [{"payload":"{\"flowId\": \"4668724\",\"nodeId\":\"30019902\"}"}]
     */
    @PostMapping('/notify.json')
    async notify(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body;
        for (const message of messages) {
            const payload: MpsPayload = JSON.parse(message.payload);

            const { flowMetaData, flowNodeMessage } = await this.workflow.getFlowDataTriggerByMps(payload);

            if (flowMetaData.currentNodeId === '30010203' && await isAuthedThirdLevelOrg('depature', flowNodeMessage.level, flowNodeMessage.orgPosId)) {
                await this.workflow.submit({
                    fullname: flowNodeMessage.acceptorNameList[0],
                    email: flowNodeMessage.acceptorList[0]
                }, flowMetaData, {
                        ...flowNodeMessage,
                        operateRemark: '自动审批通过',
                        approved: true
                    });
                return ctx.body = AjaxResult.success();
            }
            //  TODO 这里可以做些什么
            // 更新工单数据
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
            const createrKeywords = '您的工单状态发生变更';
            // 通知审批人
            const keywords = '离职申请';
            await this.workflow.notifyApprover(flowMetaData, flowNodeMessage, keywords, {
                html: 'bflow_workflow_perm_departure_html',
                text: 'bflow_workflow_perm_departure_text'
            });
            // 单子被拒绝，通知创建人
            await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, createrKeywords);

        }
        return ctx.body = AjaxResult.success();

    }

    /**
     * 流程结束通知
     * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
     */
    @PostMapping('/notifyEnd.json')
    async notifyEnd(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body[0];
        const payload: MpsPayload = JSON.parse(messages.payload);
        let flowMetaData = null;
        let flowNodeMessage = null;
        try {
            const flowData = await this.workflow.getFlowDataTriggerByMps(payload);
            flowMetaData = flowData.flowMetaData;
            flowNodeMessage = flowData.flowNodeMessage;
            // 更新业务状态
            flowNodeMessage.businessStatus = 1;
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
        } catch (e) {
            console.error('工单，结束节点数据更新失败', e);
            return ctx.body = AjaxResult.internal('工单更新失败');
        }
        // 业务处理
        try {
            const params = {
                uid: flowNodeMessage.applerUid,
                type: -1
            };
            await departureExtendOperate(params);
        } catch (e) {
            console.error('离职后续操作失败', e);
            ctx.body = AjaxResult.internal('工单更新失败');
            return;
        }

        const keywords = '审批结束';
        // 通知创建人
        await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, keywords);
        // 通知跟踪人
        // await this.workflow.notifyTrailer(flowMetaData, flowNodeMessage, '工单已经结束');
        // 通知产品经理
        await this.workflow.notifyProductMangager(flowMetaData, flowNodeMessage);
        ctx.body = AjaxResult.success();
    }

}
