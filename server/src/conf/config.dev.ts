import { ITigerProxyOption } from '@tiger/proxy';
import { join } from 'path';
import BaseConfig from './config.base';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
    loggerPath: string = join(this.rootPath, 'log');
    appProxyOptions: ITigerProxyOption = {
        target: 'http://local.yx.mail.netease.com:8080',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(`${this.xhrPrefix}/api`, '');
        }
    };

    // 产品相关信息
    productDomain = 'local.yx.mail.netease.com';

    productPort = 8080;

    // 权限中心服务域名
    iusDomain = 'http://dev.yx.localhost:8550/proxy/dev.yanxuan-ius.service.mailsaas';
    // iusDomain = 'http://yxius.you.163.com';

    // cmdb服务域名
    cmdbDomain = 'http://**************:8300/api/v2';

    // 严选消息通知服务相关配置
    notifyUrl = `http://yxmsg.you.163.com/notify/${this.productCode}/multi/send.json`;

    // 邮件模版服务相关配置
    templateDomain = 'http://yxtemplate.test.you.163.com';

    // 权限中心的转发配置
    umcProxyOptions: ITigerProxyOption = {
        target: 'http://dev.yx.localhost:8550/proxy/dev.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: { Host: 'dev.yx.localhost:8550' },
        rewrite: (path: string) => {
            return path.replace(new RegExp(`^${this.contextPath}${this.xhrPrefix}/userCenterManage`), '');
        }
    };
    //催办转发
    reminders: ITigerProxyOption = {
        target: 'http://dev.yx.localhost:8550/proxy/dev.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^/written-doc/xhr/proxy`
                ),
                ''
            );
        }
    }


    // 权限中心
    icacProxyOptions: ITigerProxyOption = {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(`${this.contextPath}${this.xhrPrefix}/icac`, '');
        }
    };

    umcPermProxyOptions: ITigerProxyOption = {
        target: 'http://umc-permission.test.you.163.com',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(`${this.contextPath}/xhr/umc-permission/xhr`, '');
        }
    };

    // cmdb转发
    cmdbProxyOptions: ITigerProxyOption = {
        target: 'http://***************',
        changeOrigin: true,
        autoRewrite: true,
        headers: { Host: 'test.yx.mail.netease.com' },
        rewrite: (path: string) => {
            return path;
        }
    };

    flowServerOptions: ITigerProxyOption = {
        target: 'http://dev.yx.localhost:8550/proxy/dev.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/flowServer`
                ),
                ''
            );
        }
    };

    bpmOrderOptions: ITigerProxyOption = {
        target: 'http://dev.yx.localhost:8550/proxy/dev.yanxuan-bpmorder.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/bpmOrder`
                ),
                ''
            );
        }
    };

    authedSecondLevelPosIds = {
        depature: [190, 170],
        onboard: [190, 170, 216],
        transfer: [190, 170],
        perm: [190, 170]
    };

}
