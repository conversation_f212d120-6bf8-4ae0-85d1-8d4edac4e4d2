<div class="section" [ngClass]="{'section-fold': !switch}">
    <div class="section-header section-header-bordered" (click)="switch = !switch" style="cursor: pointer;">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block" *ngIf="switch">
        <form class="form form-inline">
            <div class="form-item-group form-item-group-plained">

                <div class="form-item col-12">
                    <div class="form-item-label col-8 word-break">
                        离职人员信息：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.applerName}} {{flowData?.flowNodeMessage?.applerUid}}
                    </div>
                </div>
                <div class="form-item col-12">
                    <div class="form-item-label col-8">
                        工单编号:
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowMetaData?.globalId}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-24">
                    <div class="form-item-label col-4 word-break">
                        所在组织架构：
                    </div>
                    <div class="form-item-text col-20 word-break">
                        <div *ngFor="let item of posTreeList">{{item}}</div>
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-12">
                    <div class="form-item-label col-8 word-break">
                        离职说明：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.remark}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained" *ngIf="flowData?.flowNodeMessage?.businessStatus === -1 && userInfo?.uid === flowData?.flowNodeMessage?.createUser">
                <div class="form-item col-12">
                    <button class="btn btn-primary" (click)="goEditOrder()">编辑</button>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained noprint" *ngIf="flowData?.flowNodeMessage?.businessStatus === 1 && userInfo?.uid === flowData?.flowNodeMessage?.createUser">
                <!-- 完结状态下，打印按钮-->
                <div class="form-item col-12">
                    <button class="btn btn-primary" (click)="printOrder()">打印工单</button>
                </div>
            </div>
        </form>
    </div>
</div>