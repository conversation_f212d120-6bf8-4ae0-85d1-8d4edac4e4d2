import { GetMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, QueryContext } from '@tiger/core';
import { contextPath } from '../../conf';
import { ErrorService } from './error.service';

/**
 * 统一信息页面
 */
@RestController
@RequestMapping(`${contextPath}`)
export class ErrorController {
    constructor(
        private errorService: ErrorService,

    ) { }
    /**
     * 错误页面，支持404，403两个code
     */
    @GetMapping('/error/:code')
    async error(ctx: QueryContext<null, null, { code: string }>) {
        switch (Number(ctx.params.code)) {
            case 404:
            case 401002:
                await this.errorService.renderNotFound(ctx);
                break;
            case 403:
            case 401004:
                await this.errorService.renderUnauthorized(ctx);
                break;
            case 401003:
                await this.errorService.renderNotFoundOrg(ctx);
                break;
            default:
                break;
        }
    }
}
