<div class="modal-header m-modal-approve">
        <a class="modal-close" (click)="dismiss()"></a>
        <!-- 批量处理 -->
        <span class="modal-title" *ngIf="approveData.approved === null else single">批量处理</span>
        <ng-template #single>
            <!-- 单次处理 -->
            <span class="modal-title">{{ approveData.approved ? '通过' : '驳回'}}</span>
        </ng-template>
    </div>
    <div class="modal-body">
        <div class="form form-horizontal" shark-validform #form="shark-validform">
            <div class="form-item" >
                <label class="form-item-label col-3">备注信息：</label>
                <div class="form-item-control col-18">
                    <shark-textarea  [size]="'lg'" [(ngModel)]="approveData.operateRemark" [placeholder]="'审批说明'"></shark-textarea>
                </div>
            </div>
            <!-- 责任条款 -->
            <div class="m-liability-clause">
                <div class="m-liability-content">
                    <p>责任条款</p>
                    <p>本人已知悉所涉内容为保密信息并确认此工单内容是此申请人工作所必须权限。本人承诺并保证在商业秘密保护及信息安全等方面，会对申请人做好宣贯与管理工作。本人清楚“谁主管，谁主责”的问责原则，如果本部门员工存在信息泄露或其他违规责任，本人愿承担相应责任。同时，本人保证遵守公司信息安全及商业秘密保护的各项规定，协助并配合公司各项措施和要求。如果有信息泄露或其他违规操作，本人愿承担相应责任。</p>
                </div>
                本人已认真阅读上述内容并确认遵守：<shark-switch labelOn="" labelOff="" [(ngModel)]="value" (focus)="onFocus($event)" (blur)="onBlur($event)"></shark-switch>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <!-- <button class="btn btn-primary " type="button" (click)="dismiss()">通过</button>
        <button class="btn btn-secondary " type="button" (click)="submit()">驳回</button> -->

        <!-- 批量操作的按钮 -->
        <ng-container *ngIf="approveData.approved === null else singleOpera">
            <button class="btn btn-primary " type="button" (click)="onsubmit(true)">通过</button>
            <button class="btn btn-secondary " type="button" (click)="onsubmit(false)">驳回</button>
        </ng-container>
        <!-- 单次操作的按钮 根据是什么操作 -->
        <ng-template #singleOpera>
            <!-- 单次处理 -->
            <button *ngIf="approveData.approved === true" class="btn btn-primary " type="button" (click)="onsubmit(true)">通过</button>
            <button *ngIf="approveData.approved === false" class="btn btn-primary " type="button" (click)="onsubmit(false)">驳回</button>
        </ng-template>

       
    </div>