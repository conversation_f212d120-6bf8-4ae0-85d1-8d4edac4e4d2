{"name": "@tiger/template", "version": "2.0.0", "description": "node server & web dev template", "keywords": ["node", "koa", "angularx", "framework", "front-end", "web"], "license": "LGPL", "author": "tiger", "scripts": {"server": "nodemon --config ./src/nodemon.json ./src/index.ts", "tslint": "tslint -c ./tslint.json './src/**/*.ts'", "clean": "rm -rf dist && rm -rf build", "build": "sh scripts/build/build.sh", "build:test": "sh scripts/build/build-test.sh", "build:online": "sh scripts/build/build-online.sh"}, "dependencies": {"@eagle/common-service-node": "4.0.2", "@eagle/workflow-node": "^4.0.0", "@tiger/boot": "^4.0.0", "@tiger/cache": "^4.0.0", "@tiger/core": "^4.0.0", "@tiger/ejs": "^4.0.22", "@tiger/error": "^4.0.0", "@tiger/filter": "^4.0.0", "@tiger/health": "^4.0.22", "@tiger/info": "^4.0.22", "@tiger/logger": "^4.0.55", "@tiger/microconfig": "0.0.3", "@tiger/monitor": "^4.0.42", "@tiger/openid": "^4.0.0", "@tiger/permission": "^4.0.0", "@tiger/proxy": "^4.0.0", "@tiger/request": "^4.0.0", "@tiger/session": "^4.0.0", "@tiger/swagger": "^4.0.0", "@tiger/tiger-extract-header": "0.0.4", "boom": "^7.1.1", "ejs": "^2.6.1", "koa": "^2.5.3", "koa-body": "^4.0.4", "koa-compose": "^4.0.0", "koa-etag": "^3.0.0", "koa-router": "^7.3.0", "koa-send": "^4.1.2", "path-to-regexp": "^1.1.1", "request-promise": "^4.2.5"}, "devDependencies": {"@types/boom": "^7.2.0", "@types/ejs": "^2.6.0", "@types/koa": "^2.0.43", "@types/koa-compose": "^3.2.2", "@types/koa-etag": "^3.0.0", "@types/koa-router": "^7.0.32", "@types/koa-send": "^4.1.1", "@types/node": "^12.0.0", "@types/request-promise": "^4.1.45", "@types/supertest": "^2.0.6", "@vscode-snippets/tiger": "^1.0.0", "fs-extra": "^7.0.0", "get-port": "^3.2.0", "nodemon": "^2.0.4", "supertest": "^3.3.0", "ts-node": "^6.1.1", "typescript": "^3.0.0", "yargs": "^7.0.2"}, "engines": {"node": ">=8.6.0"}}