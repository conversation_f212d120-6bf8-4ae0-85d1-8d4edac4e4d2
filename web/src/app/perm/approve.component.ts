import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './approve.component.html',
    selector: 'flow-permapprove'
})

export class PermApproveComponent {
    @ViewChild('form') form;
    @Input() workFlowData: any;
    approveData: any = {
        approved: true,
        operateRemark: ''
    };
    roleName: string
    switch: boolean = true;
    submiting: boolean = false;
    approveChoices: Array<{
        label: string,
        value: boolean,
        disabled?: boolean
    }> = [{
        label: '通过',
        value: true
    }, {
        label: '不通过',
        value: false
    }];
    constructor(
        private ajax: Ajax,
        private toastr: SharkToastrService,
        private router: Router
    ) {

    }
    setSwitch() {
        this.switch = !this.switch;
    }
    async save() {
        this.form.submit(async () => {
            try {
                let flowNodeMessage = {
                    approved: this.approveData.approved,
                    operateRemark: this.approveData.operateRemark,
                    extend: {
                        roleName: this.roleName
                    }
                }
                let permUrl = AjaxUrl.submitFlow.perm

                // 需要区分子工单的审核还是父工单的审核
                if (this.workFlowData.flowData.flowMetaData.topologyId === 'nav_personnelchange_perm_child') {
                    permUrl = AjaxUrl.submitFlow.permChild
                }
                await this.ajax.postByJson(AjaxUrl.submitFlow.perm, {
                    flowMetaData: this.workFlowData.flowData.flowMetaData,
                    flowNodeMessage: flowNodeMessage
                });
                this.submiting = false;
                this.toastr.success('审批成功')
                this.router.navigate(['/workflow/dealingList']);
            } catch (e) {
                this.submiting = false;
                this.toastr.error(`审批失败:${e.errorCode}`);
            }
        });
    }
}
