import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkBaseModal, SharkModalParams, SharkToastrService } from '@shark/shark-angularX';
import { isEmptyObject } from '@shark/shark-angularX/components/shark-ui/common/core';

@Component({
    templateUrl: './permDetail.modal.html',
    styleUrls: ['./permDetail.modal.scss']
})
export class PermDetailModal extends SharkBaseModal {
    model = [];
    activekey: number = 0;
    tabpanes1: any[]  = []
    tabpanes: any[]  = []
    title = ''
    modalTitle = ''
    constructor(
        private params: SharkModalParams,
    ) {
        super();
        this.tabpanes1=  params.data.role.roleTab; // 获取权限树前给用户展示角色tab
        // this.tabpanes= params.data.permTreeData; // 获取到权限树后显示角色tab和treeData
        this.modalTitle = params.data.title
        this.delayeringArr(params.data.permTreeData)
        // 角色合并标题
        const roles: any[] = [];
        this.tabpanes1.map((item: any) => {
            return roles.push(item.name);
        });
        const tit = roles.join('”、“');
        this.title = `以下显示的是角色“${tit}”合并后的最终权限详情:`;
    }

    delayeringArr(permTreeData) {
        const arr = []
        if (permTreeData && permTreeData.length) {
            permTreeData.forEach(element => {
                const item = {
                    ...element,
                    treeData: this.formatTreeData(element.treeData)
                }
                arr.push(item)
            });
        }
        console.log('arr', arr)
        this.tabpanes = arr
    }

    formatTreeData(treeData) {
        const arr = []
        if (treeData && treeData.length) {
            treeData.forEach(element => {
                const item = {
                    id: element.key,
                    name: element.title,
                    pid: element.pId
                }
                arr.push(item)
                if (element.children && element.children.length) {
                    arr.push(...this.formatTreeData(element.children))
                }
            });
        }
        return arr
    }
}
