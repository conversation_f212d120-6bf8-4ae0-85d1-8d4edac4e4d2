import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, HostListener } from '@angular/core';
import { SharkToastrService } from '@shark/shark-angularX';
import { IAppDataFromIcac } from './app.interface';
import { ApplicationService } from './application.service';
import { UserCenterService } from '../../userCenter/services/userCenter.service'
import { PermService } from './../../shared/services/perm.service';

export interface IApp {
    color: string;
    description: string;
    isLock: boolean;
    link: string;
    name: string;
    path: string;
    showIndex: number;
    tagid: string;
    id: string;
    icon: string;
    productCode?: string;
    isFav: boolean;
    tag: TagApps;
    perm: boolean;
}

interface TagApps {
    applications: IApp[];
    name: string;
    icon: string;
    background: string;
    id: string;
}

@Component({
    selector: 'application-list',
    styleUrls: ['./list.component.scss'],
    templateUrl: './list.component.html'
})
export class ApplicationListComponent implements OnInit {
    tagList: TagApps[] = [];
    favAppList: IApp[] = [];
    productCodes: string[] = [];
    isFavFolded: boolean = false;
    isFolded: boolean = false;
    isLoaded: boolean = false;
    cols: number = 5;

    appsWithPerm: IAppDataFromIcac[] = [];

    constructor(
        private appService: ApplicationService,
        private toast: SharkToastrService,
        private userService: UserCenterService,
        private permService: PermService
    ) { }

    tagidFn(index, item) {
        return item.tagid;
    }

    async ngOnInit() {

        const appsRes = await this.permService.list();
        this.tagList = appsRes.data;

        const favRes = await this.appService.getFavApps();
        this.favAppList = favRes.data;

        const perms = await this.userService.getUserPerm();
        this.productCodes = perms.map((perm) => {
            return perm.productCode;
        })

        this.formatData();
        // 

        this.isLoaded = true;
        // 是否默认收起
        if (this.favAppList.length === 0) {
            this.isFavFolded = true;
        }
        this.isFolded = false;
        this.onResize('');
    }

    ngOnDestroy() {
        document.body.style.background = '#f4f4f4';
    }

    formatData() {
        this.favAppList.forEach((app) => {
            app.isFav = true;
            app.perm = app.productCode ? this.productCodes.includes(app.productCode) : false;
        })
        const favAppIds = this.favAppList.map((app) => {
            return app.id;
        })

        this.tagList.forEach((tag) => {
            tag.applications.forEach((app) => {
                app.isFav = favAppIds.includes(app.id);
                app.perm = app.productCode ? this.productCodes.includes(app.productCode) : false;
            })
        })
    }

    async addToFav(app) {
        try {
            const res = await this.appService.addFavApp(app.id);
            this.favAppList = res.data;
            this.formatData();
            this.toast.success('已添加到我的收藏');
        } catch (error) {
            console.error(error);
            this.toast.error('添加收藏失败，请重试');
        }
    }

    async rmFav(app) {
        try {
            const res = await this.appService.deleteFavApp(app.id);

            this.favAppList = res.data;
            this.formatData();
            this.toast.success('取消收藏成功');

        } catch (error) {
            console.error(error);
            this.toast.error('取消收藏失败，请重试');
        }
    }


    @HostListener('window:resize', ['$event'])
    onResize(event) {
        const rootWidth: any = document.getElementsByClassName('root')[0].clientWidth;
        if (rootWidth && rootWidth < 1400) {
            this.cols = 4;
        } else {
            this.cols = 5;
        }
    }
}
