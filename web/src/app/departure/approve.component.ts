import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './approve.component.html',
    selector: 'flow-departureapprove'
})

export class DepartureApproveComponent {
    @ViewChild('form') form;
    @Input() workFlowData: WorkFlowData;
    approveData: any = {
        approved: true,
        operateRemark: ''
    };
    switch: boolean = true;
    submiting: boolean = false;
    approveChoices: Array<{
        label: string,
        value: boolean,
        disabled?: boolean
    }> = [{
        label: '通过',
        value: true
    }, {
        label: '不通过',
        value: false
    }];
    constructor(
        private ajax: Ajax,
        private toastr: SharkToastrService,
        private router: Router
    ) {

    }
    setSwitch() {
        this.switch = !this.switch;
    }
    async save() {
        this.form.submit(async () => {
            try {
                this.submiting = true;
                await this.ajax.postByJson(AjaxUrl.submitFlow.departure, {
                    flowMetaData: this.workFlowData.flowData.flowMetaData,
                    flowNodeMessage: {
                        ...this.approveData
                    }
                });
                this.submiting = false;
                this.router.navigate(['/workflow/dealingList']);
            } catch (e) {
                this.submiting = false;
                this.toastr.error(`审批失败:${e.errorCode}`);
            }
        });
    }
}
