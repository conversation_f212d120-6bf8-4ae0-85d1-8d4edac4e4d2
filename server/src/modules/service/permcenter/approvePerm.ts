import { authedSecondLevelPosIds } from '../../../conf';
import { getUpperByOrgPosId, getUpperByOrgUserId, getUserEmployeeNum, getUserExtInfo } from './unitOrg';
// 二级部门领导授权的审批人
const agencyMap: any = {
    56: { uid: '<EMAIL>', realName: '吴子房' }
};

interface IUserInfo { uid: string; realName: string; }

/**
 * 部门是否有审批代理人节点
 * @param orgPosId 架构
 */
export async function isOrgAgency(orgPosId: number) {
    const upperOrgMap = await getUpperByOrgPosId(orgPosId);
    const secondLevel = upperOrgMap[98] || {};
    return agencyMap.hasOwnProperty(secondLevel.orgPosId);
}

/**
 * 部门是否有三级部门代理审批节点
 * @param orgPosId 架构
 */
export async function isAuthedThirdLevelOrg(type: 'depature' | 'onboard' | 'transfer' | 'perm', level: number, orgPosId: number) {
    const upperOrgMap = await getUpperByOrgPosId(orgPosId);
    const secondLevel = upperOrgMap[98] || {};
    return level < 98 && authedSecondLevelPosIds[type].includes(secondLevel.orgPosId);
}
/**
 * 获取审批代理人信息
 * @param orgPosId 架构
 */
export async function getOrgAgencyUser(orgPosId: number) {
    const upperOrgMap = await getUpperByOrgPosId(orgPosId);
    const secondLevel = upperOrgMap[98] || {};
    if (agencyMap.hasOwnProperty(secondLevel.orgPosId)) {
        return agencyMap[secondLevel.orgPosId];
    }
}

/**
 * 获取二级部门
 * @param originOrgPos 申请人
 */
export async function getBestOrg(originOrgPos: any): Promise<{
    orgPosId: number, secondManger: IUserInfo[], potical: IUserInfo[]
}> {
    for (const originOrgPosId of originOrgPos) {
        const orgPosId = Number.parseInt(originOrgPosId, 10);
        const upperOrgMap = await getUpperByOrgPosId(orgPosId);
        const secondLevel = upperOrgMap[98] || {};
        const secondManger = getManagerInfo(secondLevel.orgPosUniteUsers || [], 2);
        const potical = getManagerInfo(secondLevel.orgPosUniteUsers || [], 1);
        if (secondManger.length > 0 && potical.length > 0) {
            console.info(`系统自动选择了迁出审批部门：${orgPosId} ${secondLevel.orgPosName}, 指导员：${potical}, 负责人：${secondManger}`);
            return { orgPosId, secondManger, potical };
        }
    }
    console.error(`尴尬！系统找不到适合迁出的审批部门！`);
    return null as any;
}

/**
 * 获取三级部门负责人
 * @param orgPosId 申请的部门id
 */
export async function getThirdLevelManagerInfoByOrgId(orgPosId: number): Promise<any> {
    const upperOrgMap = await getUpperByOrgPosId(orgPosId);
    const thirdLevel = upperOrgMap[97] || {};
    return getManagerInfo(thirdLevel.orgPosUniteUsers || [], 2);
}

/**
 * 获取二级部门审批人信息
 * @param orgPosId 申请的部门id
 */
export async function getSecondLevelManagerInfoByOrgId(orgPosId: number): Promise<any> {
    const upperOrgMap = await getUpperByOrgPosId(orgPosId);
    const secondLevel = upperOrgMap[98] || {};
    return getManagerInfo(secondLevel.orgPosUniteUsers || [], 2);
}

/**
 * 获取二级部门指导员信息
 * @param orgPosId 申请的部门id
 */
export async function getSecondLevelPoliticalInfoByOrgId(orgPosId: number): Promise<any> {
    const upperOrgMap = await getUpperByOrgPosId(orgPosId);
    const secondLevel = upperOrgMap[98] || {};
    return getManagerInfo(secondLevel.orgPosUniteUsers || [], 1);
}
/**
 * 获取用户工号信息
 *
 * @export
 * @param {string} uid
 * @returns {Promise<any>}
 */
export async function getUSerEmployeeNumByUid(uid: string): Promise<any> {
    const { employeeNum } = await getUserEmployeeNum(uid);
    return employeeNum;
}
/**
 * 获取人员列表
 * @param orgPosUniteUsers 负责人
 * @param userType 类型 1:指导员，2:负责人
 */
export function getManagerInfo(orgPosUniteUsers: any[], userType: number): IUserInfo[] {
    const managerInfos = [];
    for (const user of orgPosUniteUsers) {
        if (user.type === userType) {
            managerInfos.push({
                uid: user.uid,
                realName: user.realName
            });
        }
    }
    return managerInfos;
}

export function isOrgHasManager(orgPosUniteUsers: any[]) {
    return (orgPosUniteUsers || []).some((unitUser) => {
        return unitUser.type === 2;
    });
}
