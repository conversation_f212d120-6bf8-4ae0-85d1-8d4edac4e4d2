<div class="section" [ngClass]="{'section-fold': !switch}">
    <div class="section-header section-header-bordered" (click)="switch = !switch" style="cursor: pointer;">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block" *ngIf="switch">
        <form class="form form-inline">
            <div class="table-wrap">
                <table class='table table-full text-center'>
                    <thead>
                        <tr>
                            <th>工单ID</th>
                            <th>文件编号</th>
                            <th>版本号</th>
                            <th>文件名称</th>
                            <th>文件密级</th>
                            <th>文件类型</th>
                            <th>流程层级</th>
                            <th>上层流程</th>
                            <th>主责部门</th>
                            <th>相关责任部门</th>
                            <th>拟制人</th>
                            <th>拟制部门</th>
                            <th>审核人</th>
                            <th>文件状态</th>
                            <th>提交时间</th>
                            <th>生效时间</th>
                            <th *ngIf='flowData?.flowNodeMessage?.content?.passDate'>下发时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="text-nowrap" name="工单ID">{{flowData?.flowMetaData?.flowId}}</td>
                            <td class="text-nowrap" name="文件编号">{{flowData?.flowNodeMessage?.content?.number}}</td>
                            <td class="text-nowrap" name="版本号">
                                <span *ngIf="flowData?.flowNodeMessage?.content?.version">{{flowData?.flowNodeMessage?.content?.version}}</span>
                            </td>
                            <td class="text-nowrap" name="文件名称">
                                {{flowData?.flowNodeMessage?.content?.name}}
                            </td>
                            <td class="text-nowrap" name="文件密级">
                                <span [ngSwitch]="flowData?.flowNodeMessage?.content?.confidentiality">
                                    <span *ngSwitchCase="1">
                                        R.01绝密级
                                    </span>
                                    <span *ngSwitchCase="2">
                                        R.02机密级
                                    </span>
                                    <span *ngSwitchCase="3">
                                        R.03秘密级
                                    </span>
                                    <span *ngSwitchCase="4">
                                        R.04普一级
                                    </span>
                                    <span *ngSwitchCase="5">
                                        R.05普二级
                                    </span>
                                    <span *ngSwitchCase="6">
                                        R.06普三级
                                    </span>
                                    <span *ngSwitchDefault>

                                    </span>
                                </span>
                            </td>
                            <td class="text-nowrap" name="文件类型">
                               <span
                                   *ngIf='this.flowData?.flowNodeMessage?.content?.categoryList'>{{this.flowData?.flowNodeMessage?.content?.categoryList[0]}}</span>
                            </td>
                            <td class="text-nowrap" name="流程层级">
                               <span
                                   *ngIf='this.flowData?.flowNodeMessage?.content?.categoryList'>{{this.flowData?.flowNodeMessage?.content?.categoryList[1]}}</span>
                            </td>
                            <td class="text-nowrap" name="上层流程">
                                <span *ngIf='this.flowData?.flowNodeMessage?.content?.categoryList'>{{this.flowData?.flowNodeMessage?.content?.categoryList[2]}}</span>
                            </td>
                            <td class="text-nowrap" name="主责部门">
                                {{flowData?.flowNodeMessage?.content?.mainDepartment}}
                            </td>
                            <td class="text-nowrap" name="相关责任部门">
                                <span *ngIf="flowData?.flowNodeMessage?.content?.responsibleDepartmentList?.length > 0">
                                    {{flowData?.flowNodeMessage?.content?.responsibleDepartmentList}}
                                </span>
                                <span *ngIf="flowData?.flowNodeMessage?.content?.responsibleDepartmentList?.length === 0">/</span>
                            </td>
                            <td class="text-nowrap" name="拟制人">
                                {{flowData?.flowNodeMessage?.content?.createUser}}
                                <p>{{flowData?.flowNodeMessage?.content?.creatorUId}}</p>
                            </td>
                            <td class="text-nowrap" name="拟制部门">
                                {{flowData?.flowNodeMessage?.content?.department}}
                            </td>
                            <td class="text-nowrap" name="审核人">
                                <popo *ngFor="let uid of flowData?.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="flowData?.flowNodeMessage?.acceptorList.length === 0">/</span>
                            </td>
                            <td class="text-nowrap" name="文件状态">/</td>
                            <td class="text-nowrap" name="提交时间">
                                {{flowData?.flowNodeMessage.createTime | date: 'yyyy-MM-dd HH:mm:ss'}}
                            </td>
                            <td class="text-nowrap" name="生效时间">
                                <span *ngIf="flowData?.flowNodeMessage?.content?.effectStartTime && 
                                flowData?.flowNodeMessage?.content?.effectEndTime">
                                    {{flowData?.flowNodeMessage?.content?.effectStartTime | date: 'yyyy-MM-dd'}}
                                    —— {{flowData?.flowNodeMessage?.content?.effectEndTime | date: 'yyyy-MM-dd'}}
                                </span>                
                            </td>
                            <td class="text-nowrap" name="下发日期" *ngIf='flowData?.flowNodeMessage?.content?.passDate'>
                                {{flowData?.flowNodeMessage?.content?.passDate | date: 'yyyy-MM-dd HH:mm:ss'}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="form-item">
                <div class="form-item-text">
                    <button class="btn btn-link" (click)='toDoc()'>成文文件详情</button>
                </div>
            </div>
            <!-- <div class="form-item-group form-item-group-plained" *ngIf="flowData?.flowNodeMessage?.businessStatus === -1 && '<EMAIL>' === flowData?.flowNodeMessage?.createUser">
                <div class="form-item col-12">
                    <button class="btn btn-primary" (click)="goEditOrder()">编辑</button>
                </div>
            </div> -->
            
            <div class="text-center" style="margin:50px 0;">
                <button *ngIf="!showTrainList" class="btn btn-secondary btn-secondary-primary" style="margin-right: 50px"
                    (click)="dismiss()">返回</button>
                <!-- 如果当前节点为拟写成文文件、审批不通过、登录人是创建人 则显示处理按钮 -->
                <button class="btn btn-primary" (click)="toEdit()" *ngIf="this.flowData.flowNodeMessage?.content?.approved === false
                    && this.flowData.flowMetaData.currentNodeId === '70210101' && userInfo?.email ===
                    flowData?.flowNodeMessage?.createUser">处理</button>
                <!-- 从文档作废工单进去的处理按钮 -->
                <button *ngIf="this.flowData.flowMetaData.currentNodeId === '70210301' && 
                flowData.flowNodeMessage?.content?.approved === false && userInfo?.email ===
                flowData?.flowNodeMessage?.createUser" class="btn btn-primary" type="button"
                    (click)="toDoc()">处理</button>
                <!-- 成文下发工单撤回后详情页面显示编辑按钮 -->
                <button *ngIf="this.flowData.flowMetaData.currentNodeId === '70210101' && 
                flowData.flowNodeMessage?.content?.approved === undefined" class="btn btn-primary" type="button"
                (click)="toEdit()">编辑</button>
                <button *ngIf="flowData?.flowMetaData?.topologyId === 'written_doc_delete_project' && 
                flowData?.flowMetaData?.currentNodeId !== '70210301' &&
                flowData?.flowMetaData?.currentNodeId !== '70210303' && flowData?.flowMetaData?.currentNodeId !== '9999' && userInfo?.email ===
                flowData?.flowNodeMessage?.createUser" class="btn btn-primary" type="button"
                (click)="revokeDel()">撤回</button>
                <button *ngIf=" flowData?.flowMetaData?.topologyId === 'written_doc_project' && 
                flowData?.flowMetaData?.currentNodeId !== '70210101' &&
                flowData?.flowMetaData?.currentNodeId !== '70210107' && flowData?.flowMetaData?.currentNodeId !== '9999' && userInfo?.email ===
                flowData?.flowNodeMessage?.createUser" class="btn btn-primary" type="button"
                (click)="revoke()">撤回</button>
                <button *ngIf="flowData?.flowMetaData?.currentNodeId === '70210107' && userInfo?.email === flowData?.flowNodeMessage?.createUser"
                class="btn btn-primary" type="button"
                (click)="uploadTainInfo()">上传培训记录</button>
            </div> 

            <!-- <div class="form-item-group form-item-group-plained"
                *ngIf="flowData?.flowNodeMessage?.businessStatus === 1 && userInfo?.email === flowData?.flowNodeMessage?.createUser && !flowData?.flowNodeMessage?.content.approved">
                <div class="form-item col-12">
                    <button class="btn btn-primary  margin-t-4x" (click)="toEdit()">处理</button>
                </div>
            </div> -->
            <!-- 完结状态下，打印按钮-->
            <!-- <div class="form-item-group form-item-group-plained noprint"
                *ngIf="flowData?.flowNodeMessage?.businessStatus === 1 && userInfo?.email === flowData?.flowNodeMessage?.createUser &&flowData?.flowNodeMessage?.content.approved">
                
                <div class="form-item col-12">
                    <button class="btn btn-primary" (click)="printOrder()">打印工单</button>
                </div>
            </div> -->
        </form>
    </div>
</div>