import { Directive, ElementRef, Renderer2 } from '@angular/core';
import { SharkPreviewService } from '@shark/shark-angularX';

@Directive({
    selector: '[image-preview]',
    exportAs: 'image-preview'
})
export class ImagePreview {
    private listener;

    constructor(
        private elementRef: ElementRef,
        private renderer2: Renderer2,
        private previewService: SharkPreviewService
    ) { }

    ngOnInit() {
        this.listener = this.renderer2.listen(this.elementRef.nativeElement, 'click', () => {
            this.previewService.open(this.elementRef.nativeElement.getAttribute('src'));
        });
    }

    ngOnDestroy() {
        this.listener();
    }
}
