import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkModalService, SharkToastrService } from '@shark/shark-angularX';
import { UtilService } from '../../../shared/services/util.service';
import { AjaxUrl } from '../../../config';
import { ExcelDownload } from './excel-download'
import { OrgSelectorService } from '@yx-module/umc-selector';
@Component({
    selector: 'doc-list',
    templateUrl: './docList.component.html',
    providers: [ExcelDownload]
})
export class DocListComponent {
    // 列表
    public itemList: any = [];
    // 搜索条件
    //public  flowMetaData.flowId: number; //工单ID
    public number: number; //文档编号
    public name: string = ''; // 文档名称
    public confidentialityName: string;//文档密级，有枚举confidentiality
    // public stampName: string; //文档类型，有枚举stamp
    public createUser: string;//创建人
    public createUserName: string;//创建人uid
    //  public approverName:[];//审批人列表
    //flowNodeMessage.businessStatus//0处理中 1完结
    public passDate: number;//创建时间
    relatedResponsibilities: any[] = [];
    departmentId: number;

    // testDate: any = [
    //     { "序号": 1, "文档编号": "0019", "文档名称": "测试一下新建", "文档密级": "R.01绝密级", "文档类型": "规章制度和决议", "所属部门": "提报测试", "拟制人": "严选内部系统", "审核人": ["相关部门1", "相关部门2", "相关部门3", "相关部门4"], "工单状态": "进行中", "当前节点": "70210105", "拟制日期": 1552458432574 },
    //      { "序号": 2, "文档编号": "YX-RUS-0019", "文档名称": "测试新的流程", "文档密级": "R.01绝密级", "文档类型": "规章制度和决议", "所属部门": "提报测试", "拟制人": "严选内部系统", "审核人": ["grp.gyhtest1008", "严选内部系统"], "工单状态": "进行中", "当前节点": "70210107", "拟制日期": 1552462490369 },
    //     { "序号": 3, "文档编号": "YX-RUS-0020", "文档名称": "测试测试", "文档密级": "R.01绝密级", "文档类型": "规章制度和决议", "所属部门": "提报测试", "拟制人": "严选内部系统", "审核人": ["grp.gyhtest1008"], "工单状态": "进行中", "当前节点": "70210105", "拟制日期": 1552463630149 },
    //      { "序号": 4, "文档编号": "YX-RUS-0016", "文档名称": "测试异步新建成文文档", "文档密级": "R.01绝密级", "文档类型": "规章制度和决议", "所属部门": "提报测试", "拟制人": "严选内部系统", "审核人": ["相关部门1", "相关部门2", "相关部门3", "相关部门4"], "工单状态": "已完结", "当前节点": "70210103", "拟制日期": 1552379496368 }, 
    //     { "序号": 5, "文档编号": "YX-RUS-0017", "文档名称": "再次测试异步新建成文文档", "文档密级": "R.01绝密级", "文档类型": "规章制度和决议", "所属部门": "提报测试", "拟制人": "严选内部系统", "审核人": ["相关部门1", "相关部门2", "相关部门3", "相关部门4"], "工单状态": "已完结", "当前节点": "70210102", "拟制日期": 1552380170073 }, 
    //     { "序号": 6, "文档编号": "YX-RUS-0018", "文档名称": "第三次异步新建成文markdown", "文档密级": "R.01绝密级", "文档类型": "规章制度和决议", "所属部门": "提报测试", "拟制人": "严选内部系统", "审核人": ["相关部门1", "相关部门2", "相关部门3", "相关部门4"], "工单状态": "已完结", "当前节点": "70210102", "拟制日期": 1552380768938 }
    // ]
    // 文档密级
    public confidentiality: any = {
        value: '',
        data: [
            { value: '', name: '全部' },
            { value: '1', name: '绝密级' },
            { value: '2', name: '机密级' },
            { value: '3', name: '秘密级' },
            { value: '4', name: '普一级' },
            { value: '5', name: '普二级' },
            { value: '6', name: '普三级' }
        ]
    };
    //文档类型
    docType: any = {
        // value: '',
        // data: [
        //     { value: '', name: '全部' },
        //     { value: '0', name: '规章制度和决议' },
        //     { value: '1', name: '流程类文件' },
        //     { value: '2', name: '操作指导书及规范' },
        //     { value: '3', name: '会议文件' },
        //     { value: '4', name: '资料性文件' },
        //     { value: '5', name: '培训类文件' },
        //     { value: '6', name: '记录类文件' }
        // ]
        value: '',
        data: [
            { name: '全部', id: '', pid: undefined },
            { name: '规章制度类', id: 40, pid: undefined },
            { name: '流程文件-流程类', id: 41, pid: undefined, disabled: true },
            { name: '流程文件-规范操作类', id: 42, pid: undefined, disabled:true},
            { name: 'L1流程分类', id: 45, pid: 41 },
            { name: 'L2流程组', id: 46, pid: 41 },
            { name: 'L3流程', id: 47, pid: 41 },
            { name: 'L4子流程', id: 48, pid: 41 },
            { name: 'L5活动', id: 49, pid: 41 },
            { name: 'L6任务', id: 50, pid: 41 },
            { name: 'L1流程分类', id: 51, pid: 42 },
            { name: 'L2流程组', id: 52, pid: 42 },
            { name: 'L3流程', id: 53, pid: 42 },
            { name: 'L4子流程', id: 54, pid: 42 },
            { name: 'L5活动', id: 55, pid: 42 },
            { name: 'L6任务', id: 56, pid: 42 },
            { name: '流程文件-模板类', id: 99, pid: undefined, disabled:true},
            { name: 'L6任务', id: 57, pid: 99 },
            { name: '记录类', id: 43, pid: undefined },
            { name: '外来文件类', id: 44, pid: undefined }
        ]
    };
    // 工单状态
    public businessStatus: any = {
        value: '',
        data: [
            { value: '', name: '全部' },
            { value: '0', name: '处理中' },
            { value: '1', name: '完结' }
        ]
    };

    // 节点状态
    public currentNode: any = {
        value: '',
        data: [
            { value: '', name: '全部' },
            { value: '70210101', name: '拟写成文文件' },
            { value: '70210102', name: '内控文档管理员复核' },
            { value: '70210103', name: '会签人员审核' },
            { value: '70210104', name: '三级部门负责人审批' },
            { value: '70210105', name: '二级部门负责人审批' },
            { value: '70210106', name: '网易严选CEO审批' },
            { value: '70210107', name: '工单完结' },
            { value: '70210108', name: '内控/合规部负责人审批' }
        ]
    }
    isDisabled: boolean = false;

    // 分页参数
    pagination: any = {
        page: 1,
        size: 10
    };

    // 时间区间
    public applyDateRange: number[] = [];
    createEndTime: number;
    createStartTime: number;

    constructor(
        private ajax: Ajax,
        private router: Router,
        private sharkModalService: SharkModalService,
        private sharkToastrService: SharkToastrService,
        private utilService: UtilService,
        private excelDownload: ExcelDownload,
        private orgSelectorService: OrgSelectorService,

    ) {
        this.searchDocList();
    }

    // 选择部门
    selectOrg() {
        this.orgSelectorService.selectOrg({
            list: this.relatedResponsibilities
        }, res => {
            this.ajax.postByJson(AjaxUrl.docManagement.getOrgName, { 'orgPosIds': res }).then(data => {
                this.relatedResponsibilities = data.data;
                this.departmentId = this.relatedResponsibilities[0].orgPosId;
            });
        });
    }
    // 删除相关部门
    deleteRelatedResponsibilities(item: any, index: number) {
        this.relatedResponsibilities.splice(index, 1);
        if (this.relatedResponsibilities.length !== 0) {
            this.departmentId = this.relatedResponsibilities[0].orgPosId;
        } else {
            this.departmentId = undefined;
        }
    }
    // 导出Excel
    expExcel() {
        const postDate = {
            'name': this.name,
            'createUser': this.createUser,
            'confidentiality': this.confidentiality.value,
            'stamp': this.docType.value,
            'departmentId': this.departmentId,
            'businessStatus': this.businessStatus.value,
            'currentNodeId': this.currentNode.value,
            'createEndTime': this.applyDateRange[1] ? this.applyDateRange[1] : this.createEndTime,
            'createStartTime': this.applyDateRange[0] ? this.applyDateRange[0] : this.createStartTime
        };
        this.ajax.postByJson(AjaxUrl.docManagement.export, postDate, false).then((res) => {
            // 获取时间
            const tmpDate = Date.todaytime();
            const year = tmpDate.getFullYear();
            const month = tmpDate.getMonth() + 1;
            const day = tmpDate.getDate();
            const hour = tmpDate.getHours();
            const min = tmpDate.getMinutes();
            const second = tmpDate.getSeconds();

            // 更改文件名称
            const fileName = '文档列表' + year + month + day + hour + min + second;
            this.excelDownload.saveAsExcelFile(fileName, res.data);
            this.sharkToastrService.success('数据导出成功');
        });
    }
    // 查询
    search() {
        //校验 选择部门只能选一个
        if (this.relatedResponsibilities.length > 1) {
            this.sharkToastrService.error('最多只能选择一个部门作为筛选条件');
            return false;
        }
        this.pagination.page = 1;
        this.searchDocList();
    }
    // 获取文档管理列表
    searchDocList() {
        const searchData = {
            'page': this.pagination.page,
            'size': this.pagination.size,

            'name': this.name,
            'createUser': this.createUser,
            'confidentiality': this.confidentiality.value,
            'stamp': this.docType.value,
            'departmentId': this.departmentId,
            'businessStatus': this.businessStatus.value,
            'currentNodeId': this.currentNode.value,
            'createEndTime': this.applyDateRange[1] ? this.applyDateRange[1] : this.createEndTime,
            'createStartTime': this.applyDateRange[0] ? this.applyDateRange[0] : this.createStartTime

        };
        this.ajax.postByJson(AjaxUrl.docManagement.queryDocumentList, searchData).then((res) => {
            this.itemList = res.data.result;
            this.pagination = res.data.pagination;
        }, (err) => {

        })
    }
    //查看详情页
    checkDetail(item: any) {
        this.router.navigate(['/workflow/detail', {
            flowId: item.flowMetaData.flowId,
            topologyId: item.flowMetaData.topologyId,
            enterType: 3,
            trainStatus: item.flowNodeMessage.content.trainStatus,
            currentNodeId: item.flowMetaData.currentNodeId,
            businessStatus: item.flowNodeMessage.businessStatus,
            isDetail: true
        }]);
    }
    // 工单状态为完结，当前节点显示为全部,不能再选（禁用）
    isFinish($event) {
        if ($event === '1') {
            this.currentNode.value = '';
            this.isDisabled = true;
        } else {
            this.isDisabled = false;
        }
    }
    onPageChanged($event) {
        this.pagination.page = $event.data.page;
        this.searchDocList();
    }

    onSizeChanged($event) {
        this.pagination.page = 1;
        this.pagination.size = $event.data.size;
        this.searchDocList();
    }

}
