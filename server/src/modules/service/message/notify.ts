import axios from '@tiger/request';
import querystring from 'querystring';
import { notifyUrl, productCode, templateDomain } from '../../../conf';

export async function notify(info: any, module: string, topic: string) {
    const msg: string = querystring.stringify({
        module: `${module}`,
        topic: `${topic}`,
        message: JSON.stringify(info)
    });
    return await axios.post(notifyUrl, msg, {
        headers: {
            'NTES-TRACEID': getTraceId(module, topic),
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    });
}

export async function getEmailContent(param: any) {
    const url = `${templateDomain}/templates/${productCode}/template/assemble.json`;
    return await axios.post(`${url}`, param, {
        headers: {
            'Content-Type': 'application/json'
        }
    });
}

export function getTraceId(module: string, topic: string) {
    return `${productCode}_${module}_${topic}_${Date.now()}`;
}
