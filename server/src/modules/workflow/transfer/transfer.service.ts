
export { disassociateUser, lockUser } from '../../service/permcenter/unitOrg';

import axios from '@tiger/request';
import querystring from 'querystring';
import { iusDomain, productCode } from '../../../conf';

export async function moveUser(param: any) {
    const originOrgPosIds = param.originOrgPosIds || [];
    const targetOrgPosId = param.targetOrgPosId;
    const uid = param.uid;
    const promises = [];
    if (originOrgPosIds.length === 0 || !targetOrgPosId) {
        throw new Error(`参数有误`);
    }
    for (const orgPosId of originOrgPosIds) {
        const queryParam = {
            orgPosId,
            targetOrgPosId,
            uid,
            targetType: param.targetType,
            duty: param.duty,
            staffStatus: param.staffStatus
        };
        const queryStr = querystring.stringify(queryParam);
        const promise = axios.get(`${iusDomain}/${productCode}/user/uniteOrg/allTeam/moveIcacUser.json?${queryStr}`);
        promises.push(promise);
    }
    return await Promise.all(promises);
}

// 转岗预校验接口，用于门户转岗工单，提交转岗工单之前调用。
export async function checkMoveIcacUser(param: {
    uid: string;
    // 组织职位Id
    orgPosIds: string,
    // 组织职位Id
    targetOrgPosId: number;
    // 移动后用户类型
    targetType: number;
}) {
    return await axios.get(`${iusDomain}/${productCode}/user/uniteOrg/inner/checkMoveIcacUser.json`, { params: param });
}
