import { Component, ViewChild, Input } from '@angular/core';
import { SharkBaseModal, SharkModalParams, SharkModalService, SharkToastrService, SharkValidForm } from '@shark/shark-angularX';

@Component({
    templateUrl: './fail.modal.html'
})
export class FailModal extends SharkBaseModal {
    @ViewChild('form', { read: SharkValidForm }) form: SharkValidForm;
    @Input() order;
    @Input() asc;
    @Input() orderChange;

    // 审核失败列表
    failDataList: any = []
    // toDetail: Function = () => {}
    constructor(
        private params: SharkModalParams,
        private sharkModalService: SharkModalService,
        private sharkToastrService: SharkToastrService
    ) {
        super();
        
        this.failDataList = this.params.data.failDataList;

        // this.toDetail = this.params.data.toDetail
    }

    // 截取指定字符后面的内容
    getCaption(charString, char: string) {
        var index = charString.lastIndexOf(char);
        return charString.substring(index+1, charString.length);
    }

    gotoDetail(index: number) {
        const { workOrderlink } = this.failDataList[index]
        const cutLink = this.getCaption(workOrderlink, '/#/')
       
        window.location.href = encodeURI(`/bflow/icacflow/${cutLink}`);
        this.close()
    }
    

    confirm() {
        this.close({ data: 'ok' });
    }
}
