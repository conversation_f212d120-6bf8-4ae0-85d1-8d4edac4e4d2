# yanxuan-bflow

严选门户项目

## 项目描述

严选门户是一个基于 Node.js、Koa 和 Angular 的前端框架项目。

## 安装

```bash
npm install
```

## 开发

### 前端开发

```bash
npm run dev
```

### 后端开发

```bash
npm run server
```

### 开发环境启动服务

```bash
npm run dev:server
```

## 构建

### 完整构建

```bash
npm run build
```

### 前端构建

```bash
npm run web:build
```

### 后端构建

```bash
npm run server:build
```

### 测试环境构建

```bash
npm run build:test
```

### 测试环境前端构建

```bash
npm run web:build:test
```

### 测试环境后端构建

```bash
npm run server:build:test
```

### 生产环境构建

```bash
npm run build:online
```

### 生产环境前端构建

```bash
npm run web:build:online
```

### 生产环境后端构建

```bash
npm run server:build:online
```

## 清理

```bash
npm run clean
```

## 依赖

- moment: ^2.29.1

## 许可证

LGPL