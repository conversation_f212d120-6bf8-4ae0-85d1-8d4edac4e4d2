const schema = require('./schema')
const fs = require('fs');
const path = require('path');
const glob = require('glob');
const child_process = require('child_process');

const main = async () => {
    // step1: transform schema to ts file, return the old jsonFiles paths
    await schema('**/**/*.json');
    // step2: compile source ts  
    build()
}

function exec(command, args, opts) {
    const {
        status,
        error,
        stderr,
        stdout
    } = child_process.spawnSync(command, args, {
        stdio: 'inherit',
        ...opts,
    });

    if (status != 0) {
        console.error(`Command failed: ${command} ${args.map(x => JSON.stringify(x)).join(', ')}`);
        if (error) {
            console.error('Error: ' + (error ? error.message : 'undefined'));
        } else {
            console.error(`STDOUT:\n${stdout}`);
            console.error(`STDERR:\n${stderr}`);
        }
        throw error;
    }
}

function copyResource() {
    console.log('Copying json...')
    const allJsonFiles = glob.sync('src/**/*.json')
    if (allJsonFiles && allJsonFiles.length > 0) {
        allJsonFiles.forEach(f => {
            console.log('copy...', f)
            copy(f, path.join(__dirname, '../dist', f))
        })
    }
    console.log('Copying md...')
    const allMdfiles = glob.sync('src/**/*.md');
    if (allMdfiles && allMdfiles.length > 0) {
        allMdfiles.forEach(mf => {
            console.log('copy...', mf)
            copy(mf, path.join(__dirname, '../dist', mf))
        })
    }
    console.log('Copying package.json...')
    copy(path.join(__dirname, '../package.json'), path.join(__dirname, '../dist/package.json'))
}

function build() {
    console.log('Building...');
    exec('node', [
        require.resolve('typescript/bin/tsc'),
        '-p',
        'tsconfig.json',
    ], {});
    console.log('Build ok...');
}

function mkdirp(p) {
    if (!fs.existsSync(path.dirname(p))) {
        mkdirp(path.dirname(p));
    }
    fs.mkdirSync(p);
}

try {
    main()
} catch (error) {
    console.log(error)
    process.exit(1)
}