import {NgModule} from '@angular/core';
import {YXWrokFlowConfig} from '@shark/shark-yx-workflow';

import {BpmFlowNames} from '../config';
import {SharedModule} from '../shared/shared.module';
import {DocProjectApproveComponent} from './approve.component';
import {DocProjectComponent} from './docProject.component';
import {DocProjectDetailComponent} from './detail.component';
import {DocProjectEditComponent} from './edit.component';
import {TrainInfoComponent} from './trainInfo.component'

@NgModule({
    imports: [
        SharedModule
    ],
    declarations: [
        DocProjectComponent,
        DocProjectEditComponent,
        DocProjectDetailComponent,
        DocProjectApproveComponent,
        TrainInfoComponent
    ],
    entryComponents: [DocProjectComponent]
})
export class DocProjectModule {
    constructor() {
        YXWrokFlowConfig.addDetailComponent(BpmFlowNames.WRITTEN_DOC_PROJECT, {
            component: DocProjectComponent,
            excludeLinkResourceIdList: ['SequenceFlow_0m741mr',
                'SequenceFlow_0jgyzcv',
                'SequenceFlow_0stfhxj',
                'SequenceFlow_0ukemgw',
                'SequenceFlow_1r2gxt4',
                'SequenceFlow_1wyfog0',
                'SequenceFlow_0frdpdx',
                'SequenceFlow_0ju0trl',
                'SequenceFlow_1b1sqsc',
                'SequenceFlow_1izp790',
                'SequenceFlow_14o0hgr',
                'SequenceFlow_17aa8b6',
                'SequenceFlow_190gn0i',
                'SequenceFlow_12yc64m',
                'SequenceFlow_1fxs4oz']
        });
        YXWrokFlowConfig.addDetailComponent(BpmFlowNames.WRITTEN_DOC_DELETE_PROJECT, {
            component: DocProjectComponent,
            // excludeNodeIdList: ['70210107']
        });
    }
}
