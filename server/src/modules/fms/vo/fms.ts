/// <reference types="node" />
export interface Files {
    [key: string]: File;
}
export interface File {
    size: number;
    path: string;
    name: string;
    type: string;
    lastModifiedDate?: Date;
    hash?: string;
    toJSON(): {};
}
export interface IFileUploadOption {
    /**
     * 主题， 强烈建议简单明了，方便以后统计组统计数据
     */
    topic: string;
    /**
     * 文件名 type =2时必须
     */
    fileName?: string;
    /**
     * 文件大小 type =2时必须
     */
    fileSize?: number;
    /**
     * 数据流，type为2时必需，可放入Request body中
     */
    inputStream?: Buffer;
    /**
     * 文件的url type=3时必须
     */
    url?: string;
    /**
     * 请求方式（即期望 fms 获取url文件的请求方式） type=3时可指定
     */
    getOrPost?: string;
}
export declare type IFileInfo = string[];
export interface IFileVO {
    /**
     * 文件名
     */
    fileName: string;
    /**
     * 文件key
     */
    fileKey: string;
}
export declare type IFilesInfo = IFileVO[];
export interface IFileUploadOpt extends IFileUploadOption {
    /**
     * 文件对象
     */
    file: File;
}
export interface IFilesUploadOpt extends IFileUploadOption {
    /**
     * 文件对象
     */
    files: Files;
}
export interface IFilesDownloadOpt {
    /**
     * 文件key
     */
    fileKey: string;
    /**
     * 主题
     */
    topic: string;
}
