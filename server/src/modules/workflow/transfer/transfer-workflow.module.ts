import { TgModule } from '@tiger/boot';

import { TransferWorkflowLinkAuditController } from './controller/transfer-workflow-link-audit.controller';
import { TransferWorkflowNotifyController } from './controller/transfer-workflow-notify.controller';
import { TransferWorkflowController } from './controller/transfer-workflow.controller';

@TgModule({
    imports: [],
    controllers: [TransferWorkflowLinkAuditController, TransferWorkflowNotifyController, TransferWorkflowController]
})
export class TransferWorkflowModule { }
