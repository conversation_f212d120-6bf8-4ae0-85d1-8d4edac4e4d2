import { Injectable } from '@angular/core';
import { UserCenterAjax } from './ajax.service';
import {Ajax} from '@shark/shark-angularX'
import { AjaxUrl } from '../../config';
@Injectable()
export class UserCenterService {
    constructor(private ajax: UserCenterAjax,
        private orginAjax:Ajax) { }

    // 获取自己的权限
    async getUserPerm() {
        return await this.ajax.get(
            '/icac/webapi/xhr/user/listProductRoleByUid.do',
            {}
        );
    }

    // 获取用户的信息
    async getUserInfo() {
        return await this.ajax.get('/icac/webapi/xhr/user/getUserInfo.do', {});
    }

    // 根据uid查询用户详细信息，包括工号、联系方式、所在部门链、最上级leader等
    async getDetailUserInfo(uid: string) {
        const data =  await this.orginAjax.get(AjaxUrl.user.getDetailUserInfo, { uid });
        return data.data;
    }

    async getUserInfoByUid(uid) {
        return await this.ajax.get('/icac/webapi/xhr/user/getUserInfo.do', {
            email: uid
        });
    }

    // 修改用户的信息
    async modifyUserInfo(mobile: string, yxId: string, wxId: string) {
        const params = {
            mobile,
            yxId,
            wxId
        };
        return await this.ajax.postByJson(
            '/icac/webapi/xhr/user/modifyUserInfo.do',
            params
        );
    }
}
