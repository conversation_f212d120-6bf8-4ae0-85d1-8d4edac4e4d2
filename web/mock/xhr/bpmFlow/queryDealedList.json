{"code": 200, "data": {"pagination": {"page": 1, "size": 10, "total": 9, "totalPage": 1, "offset": 0}, "result": [{"flowMetaData": {"product": "ius", "createTime": 1599619936338, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599619935615", "topologyId": "ius_special_rights", "updateTime": 1599619937562, "nodeId": "30110101", "flowId": "10719849", "version": 0, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 0, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "sss"}]}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "workOrderId": "IUS_SPECIAL_1599619935615", "nodeId": "30110101", "flowId": "10719849", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "navAdmin", "createTime": 1610968740957, "nextNodeList": ["30010303"], "globalId": "navAdmin_1610968740797", "topologyId": "ius_entry_apply_topology", "updateTime": 1611198257277, "nodeId": "30010304", "flowId": "11610488", "version": 3, "latest": true, "status": 100, "topologyName": "ius_entry_apply_topology", "currentNodeId": "30010303"}, "flowNodeMessage": {"applerPhoneNum": "***********", "applerType": 2, "parentOrgPosId": 306, "applerUid": "<EMAIL>", "level": 97, "trackFullList": [], "createUserName": "王士瑜", "businessStatus": 0, "updateTime": 1611198257243, "orgPosId": 601, "dutyDesc": "软件测试", "applerName": "王士瑜", "observerList": ["<EMAIL>", "<EMAIL>", "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "applerRoleType": 0, "orgPosPath": "严选事业部>测试部门>质量组", "trackList": ["<EMAIL>", "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "createTime": 1610968740797, "acceptorNameList": ["wb.<PERSON><PERSON><PERSON><PERSON><PERSON>"], "createUser": "<EMAIL>", "orgPosName": "质量组", "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"]}}, {"flowMetaData": {"product": "ius", "createTime": 1599619595718, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599619594789", "topologyId": "ius_special_rights", "updateTime": 1599619600476, "nodeId": "30110101", "flowId": "10722556", "version": 2, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "aa", "applySystemName": "aa", "applySystemDesc": "zzz"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599619594789", "nodeId": "30110102", "flowId": "10722556", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599619412288, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599619411531", "topologyId": "ius_special_rights", "updateTime": 1599619417028, "nodeId": "30110101", "flowId": "10719776", "version": 2, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "fff"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599619411531", "nodeId": "30110102", "flowId": "10719776", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599619174005, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599619173270", "topologyId": "ius_special_rights", "updateTime": 1599619178714, "nodeId": "30110101", "flowId": "10722542", "version": 2, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "cdcc"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599619173270", "nodeId": "30110102", "flowId": "10722542", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599618997213, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599618996497", "topologyId": "nav_personnelchange_transfer", "updateTime": 1599619002100, "nodeId": "30110101", "flowId": "10722526", "version": 2, "latest": true, "status": 100, "topologyName": "nav_personnelchange_transfer", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "ggg"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "ggg"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599618996497", "nodeId": "30110102", "flowId": "10722526", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599618719789, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599618719069", "topologyId": "nav_personnelchange_departure", "updateTime": 1599618724535, "nodeId": "30110101", "flowId": "10719580", "version": 2, "latest": true, "status": 100, "topologyName": "nav_personnelchange_departure", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "eeee"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "www"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599618719069", "nodeId": "30110102", "flowId": "10719580", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599618294726, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599618293978", "topologyId": "nav_personnelchange_onboard", "updateTime": 1599618299742, "nodeId": "30110101", "flowId": "10719564", "version": 2, "latest": true, "status": 100, "topologyName": "nav_personnelchange_onboard", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "hhhyy"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599618293978", "nodeId": "30110102", "flowId": "10719564", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599617945492, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599617944631", "topologyId": "written_doc_delete_project", "updateTime": 1599617950300, "nodeId": "30110101", "flowId": "10719512", "version": 2, "latest": true, "status": 100, "topologyName": "written_doc_delete_project", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "fff"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1599617944631", "nodeId": "30110102", "flowId": "10719512", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599616795051, "globalId": "IUS_SPECIAL_1599616794314", "topologyId": "written_doc_project", "updateTime": 1599617021158, "nodeId": "30110103", "flowId": "10719459", "version": 7, "latest": true, "status": 200, "topologyName": "written_doc_project", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "cccc"}, "businessStatus": 2, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "胡尧", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "applyRoleList": [{"expireTime": 0, "roleId": 6, "roleName": "权限角色（测试）"}], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "cccc"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "cccc"}], "notifyedList": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "acceptorNameList": [], "acceptorList": [], "roleList": [{"expireTime": 0, "roleId": 4, "roleName": "中台架构师"}, {"expireTime": 0, "roleId": 5, "roleName": "超级管理员"}, {"expireTime": 0, "roleId": 9, "roleName": "普通测试用户"}, {"expireTime": 0, "roleId": 6, "roleName": "权限角色（测试）"}], "delRoleList": [{"expireTime": 0, "roleId": 2, "roleName": "123457"}, {"expireTime": 0, "roleId": 3, "roleName": "12345"}]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "ccc", "acceptorNameList": [], "acceptorList": [], "workOrderId": "IUS_SPECIAL_1599616794314", "nodeId": "9999", "flowId": "10719459", "createUid": "<EMAIL>"}}]}}