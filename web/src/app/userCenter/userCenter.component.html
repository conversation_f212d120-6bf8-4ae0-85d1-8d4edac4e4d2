<div class="section">
    <div class="section-header">
        <span class="section-header-title">个人中心</span>
    </div>
    <div class="section-block">
        <shark-tabset [type]="'line'" [(active)]="activeTab" (activeChange)="activeChange($event)">
            <shark-tabpanel [sharkTabTitle]="'个人基本信息'">
                <div class="section-block">
                    <div class="text-lg margin-b-2x">基本信息</div>
                    <form class="form form-horizontal" shark-validform #form="shark-validform" autocomplete="off">
                        <div class="form-item">
                            <label class="form-item-label col-4">姓名：</label>
                            <label class="form-item-text col-12">{{userInfo?.userName}}</label>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">工号：</label>
                            <label class="form-item-text col-12">{{userInfo?.employeeNum}}</label>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">邮箱：</label>
                            <label class="form-item-text col-12">{{userInfo?.uid}}</label>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">手机号：</label>
                            <div class="form-item-control col-4">
                                <shark-input [size]="'lg'" [ngModelOptions]="{standalone: true}"
                                    [(ngModel)]="userInfo.mobile" shark-valid [validRule]="'phone'"></shark-input>
                            </div>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">易信号：</label>
                            <div class="form-item-control col-4">
                                <shark-input [size]="'lg'" [ngModelOptions]="{standalone: true}"
                                    [(ngModel)]="userInfo.yxId" shark-valid [validRule]="'required'"></shark-input>
                            </div>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">微信号：</label>
                            <div class="form-item-control col-4">
                                <shark-input [size]="'lg'" [ngModelOptions]="{standalone: true}"
                                    [(ngModel)]="userInfo.wxId"></shark-input>
                            </div>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">OA部门：</label>
                            <label class="form-item-text col-12">{{userInfo.oaDept ? userInfo.oaDept : ' - '}}</label>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">OA状态：</label>
                            <label class="form-item-text col-12">{{userInfo.oaState ? userInfo.oaState : ' - '}}</label>
                        </div>
                        <!-- <div class="form-item" *ngFor="let orgPosPathDTO of userInfo?.orgPosPathDTOS">
                            <label class="form-item-label col-4">所属组：</label>
                            <label class="form-item-text col-12">
                                <div class="breadcrumb">
                                    <ng-container *ngFor="let orgPosDTOS of orgPosPathDTO.orgPosDTOS; ">
                                        <span class="breadcrumb-item">
                                            <span class="breadcrumb-tag">{{orgPosDTOS.orgPosName}}</span>
                                            <span class="breadcrumb-separator">
                                                <i class="icon-right"></i>
                                            </span>
                                        </span>
                                    </ng-container>
                                </div>
                            </label>
                        </div> -->
                        <div class="form-item">
                            <label class="form-item-label col-4">指导员：</label>
                            <label
                                class="form-item-text col-12">{{userInfo?.derector?.name}}({{userInfo?.derector?.uid}})</label>
                        </div>
                        <div class="form-item">
                            <label class="form-item-label col-4">BU/二级部门负责人：</label>
                            <label
                                class="form-item-text col-12">{{userInfo?.manager?.name}}({{userInfo?.manager?.uid}})</label>
                        </div>
                        <div class="form-item">
                            <label class="form-item-control col-offset-4">
                                <button class="btn btn-primary" type="button"
                                    (click)="modifyUserInfo();">确认修改</button>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="section-block">
                    <div class="text-lg margin-b-2x margin-t-2x">组织身份：</div>
                    <div class="table-wrap section-block">
                        <table class='table table-full text-center'>
                            <thead>
                                <tr>
                                    <th class="text-center">员工类型</th>
                                    <th class="text-center">部门</th>
                                    <th class="text-center">岗位</th>
                                </tr>
                            </thead>
                            <tbody *ngIf="staffInfo?.listData?.table?.length === 0">
                                <tr>
                                    <td class="table-no-data" colspan="8">
                                        <span class="no-data-tip">暂无数据</span>
                                    </td>
                                </tr>
                            </tbody>
                            <tbody *ngIf="staffInfo?.listData?.table?.length > 0">
                                <tr *ngFor="let item of staffInfo.listData.table | combine: ['type'];let index = index;">
                                    <td class="text-nowrap" name="员工类型" [attr.rowspan]="item.type_rowspan" *ngIf="item.type_display">
                                        {{ getTypeName(userType)[item.type] }}
                                    </td>
                                    <td class="text-nowrap" name="部门">
                                        <span>
                                            {{ item.part }}
                                        </span>
                                    </td>
                                    <td class="text-nowrap" name="岗位">
                                        <span>
                                            {{ item.job }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </shark-tabpanel>
            <shark-tabpanel>
                <ng-template sharkTabTitle>
                    我的权限
                </ng-template>
                <div class="section-block position-relative">
                    <shark-tabset [(active)]="tabKey">
                        <shark-tabpanel>
                            <ng-template sharkTabTitle>
                                全部 ({{tab1DataInfo.length}})
                            </ng-template>
                            <div class="table-wrap section-block">
                                <table class='table table-full text-center'>
                                    <thead>
                                        <tr>
                                            <th class="text-center">系统</th>
                                            <th class="text-center">角色</th>
                                            <th class="text-center">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody *ngIf="tab1DataInfo.length === 0">
                                        <tr>
                                            <td class="table-no-data" colspan="8">
                                                <span class="no-data-tip">暂无数据</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody *ngIf="tab1DataInfo.length > 0">
                                        <tr *ngFor="let item of tab1DataInfo.tab1Data | combine: ['system'];let index = index;">
                                            <td class="text-nowrap" name="系统" [attr.rowspan]="item.system_rowspan" *ngIf="item.system_display">
                                                <span>
                                                    {{ item.system }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="角色">
                                                <div *ngIf="item.date" class="disp-inline-block" shark-tooltip [template]="template">
                                                    <ng-template #template>
                                                        <p>
                                                            有效期至：{{ item.date | date:'yyyy-MM-dd HH:mm:ss'}}
                                                        </p>
                                                    </ng-template>
                                                    <i _ngcontent-c0="" class="icon-schedule"></i>
                                                </div>
                                                <span>
                                                    {{ item.role }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="操作" [attr.rowspan]="item.system_rowspan" *ngIf="item.system_display">
                                                <button type="button" #btn class="btn btn-link" (click)="openPermDetailModal(item, btn)">查看权限</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </shark-tabpanel>
                        <shark-tabpanel>
                            <ng-template sharkTabTitle>
                                来自组织身份的权限 ({{tab2DataInfo.length}})
                            </ng-template>
                            <div class="table-wrap section-block">
                                <table class='table table-full text-center'>
                                    <thead>
                                        <tr>
                                            <th class="text-center" [attr.rowspan]="2">系统</th>
                                            <th class="text-center" [attr.rowspan]="2">角色</th>
                                            <th class="text-center" [attr.colspan]="3">组织身份</th>
                                            <th class="text-center" [attr.rowspan]="2">操作</th>
                                        </tr>
                                        <tr>
                                            <th class="text-center">员工类型</th>
                                            <th class="text-center">部门</th>
                                            <th class="text-center">岗位</th>
                                        </tr>
                                    </thead>
                                    <tbody *ngIf="tab2DataInfo.length === 0">
                                        <tr>
                                            <td class="table-no-data" colspan="8">
                                                <span class="no-data-tip">暂无数据</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody *ngIf="tab2DataInfo.length > 0">
                                        <tr *ngFor="let item of tab2DataInfo.tab2Data | combine: ['system'];let index = index;">
                                            <td class="text-nowrap" name="系统" [attr.rowspan]="item.system_rowspan" *ngIf="item.system_display">
                                                <span>
                                                    {{ item.system }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="角色">
                                                <div *ngIf="item.date" class="disp-inline-block" shark-tooltip [template]="template">
                                                    <ng-template #template>
                                                        <p>
                                                            有效期至：{{ item.date | date:'yyyy-MM-dd HH:mm:ss'}}
                                                        </p>
                                                    </ng-template>
                                                    <i _ngcontent-c0="" class="icon-schedule"></i>
                                                </div>
                                                <span>
                                                    {{ item.role }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="员工类型">
                                                <span>
                                                    {{ getTypeName(userType)[item.staffType] }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="部门">
                                                <span>
                                                    {{ item.department }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="岗位名称">
                                                <span>
                                                    {{ item.postName }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="操作" [attr.rowspan]="item.system_rowspan" *ngIf="item.system_display">
                                                <button type="button" class="btn btn-link" (click)="openPermDetailModal(item)">查看权限</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </shark-tabpanel>
                        <shark-tabpanel>
                            <ng-template sharkTabTitle>
                                个人特殊权限 ({{tab3DataInfo.length}})
                            </ng-template>
                            <div class="table-wrap section-block">
                                <table class='table table-full text-center'>
                                    <thead>
                                        <tr>
                                            <th class="text-center">系统</th>
                                            <th class="text-center">角色</th>
                                            <th class="text-center">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody *ngIf="tab3DataInfo.length === 0">
                                        <tr>
                                            <td class="table-no-data" colspan="8">
                                                <span class="no-data-tip">暂无数据</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody *ngIf="tab3DataInfo.length > 0">
                                        <tr *ngFor="let item of tab3DataInfo.tab3Data | combine: ['system'];let index = index;">
                                            <td class="text-nowrap" name="系统" [attr.rowspan]="item.system_rowspan" *ngIf="item.system_display">
                                                <span>
                                                    {{ item.system }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="角色">
                                                <div *ngIf="item.date" class="disp-inline-block" shark-tooltip [template]="template">
                                                    <ng-template #template>
                                                        <p>
                                                            有效期至：{{ item.date | date:'yyyy-MM-dd HH:mm:ss'}}
                                                        </p>
                                                    </ng-template>
                                                    <i _ngcontent-c0="" class="icon-schedule"></i>
                                                </div>
                                                <span>
                                                    {{ item.role }}
                                                </span>
                                            </td>
                                            <td class="text-nowrap" name="操作" [attr.rowspan]="item.system_rowspan" *ngIf="item.system_display">
                                                <button type="button" class="btn btn-link" (click)="openPermDetailModal(item)">查看权限</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </shark-tabpanel>
                    </shark-tabset>
                    <!-- <div class="u-lookInfoBtn">
                        <button class="btn btn-link" type="button" (click)="openUserInfoModal();">查看员工信息</button>
                    </div> -->
                </div>
            </shark-tabpanel>
            <!-- <shark-tabpanel [sharkTabTitle]="'我的系统'">
                <div class="section-block">
                    <shark-stree [(model)]="permTree.model" [data]="permTree.data" [expandAll]="permTree.expandAll"
                        [disabled]="permTree.isDisabled"></shark-stree>
                </div>
            </shark-tabpanel> -->
            <shark-tabpanel [sharkTabTitle]="'常用联系人管理'">
                <div class="section-block">
                    <umc-contact-mamanage></umc-contact-mamanage>
                </div>
            </shark-tabpanel>
        </shark-tabset>
    </div>
</div>