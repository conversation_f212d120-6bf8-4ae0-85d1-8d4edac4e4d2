<div class="section" [ngClass]="{'section-fold': !switch}">
    <div class="section-header section-header-bordered" (click)="switch = !switch" style="cursor: pointer;">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block" *ngIf="switch">
        <form class="form form-inline">
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        工单编号：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowMetaData?.globalId}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        工单类型：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowMetaData?.topologyName | toponamepipe}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8 word-break">
                        申请人：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.applerName}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-8">
                    <div class="form-item-label col-8 word-break">
                        手机号码：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.applerPhoneNum}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8 word-break">
                        角色类型：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.applerRoleType | employeeRoleTypePipe}}
                    </div>
                </div>
                <div class="form-item col-8">
                    <div class="form-item-label col-8">
                        员工类型：
                    </div>
                    <div class="form-item-text col-16">
                        {{flowData?.flowNodeMessage?.applerType | employeeTypePipe}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        申请加入的组：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.orgPosPath}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        职责描述：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.dutyDesc}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained">
                <div class="form-item col-16">
                    <div class="form-item-label col-4">
                        入职说明：
                    </div>
                    <div class="form-item-text col-16 word-break">
                        {{flowData?.flowNodeMessage?.remark}}
                    </div>
                </div>
            </div>
            <div class="form-item-group form-item-group-plained" *ngIf="flowData?.flowNodeMessage?.businessStatus === -1 && userInfo?.uid === flowData?.flowNodeMessage?.createUser">
                <div class="form-item col-12">
                    <button class="btn btn-primary" (click)="goEditOrder()">编辑</button>
                </div>
            </div>
        </form>
    </div>
</div>