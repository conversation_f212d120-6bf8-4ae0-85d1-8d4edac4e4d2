import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkActionToastrService, SharkToastrService, SharkTreeCheckedEvent } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { UserSelectorService } from '@yx-module/umc-selector';
import { AjaxUrl } from '../config';
import { UserService } from '../shared/services/user.service';
import { CmdbService } from './../shared/services/cmdb.serverice'
import { ProductInfo, CheckedList } from './product.interface'
import { _ } from 'core-js';
import { element } from '@angular/core/src/render3/instructions';

@Component({
    templateUrl: './edit.component.html',
    selector: 'flow-permedit'
})
export class PermEditComponent {
    @ViewChild('form') form;
    @Input() workFlowData: WorkFlowData;
    @Input()
    userInfo: any = null;
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {
            originOrgPos: {}
        }
    };
    showData: any = {
        appler: '',
        manager: '',
        orgPosTreeList: []
    };
    permList: CheckedList[]
    productList: any[]
    initProductList: any[] // 保存初始化数据，用于搜索
    searchVal: string
    checkedList: number[] = []
    checkedProductList: Array<ProductInfo> = []
    orgPosId: number
    originOrgPos: any = {}; // 申请人原始所在组织架构信息
    originOrgPosList: any[] = []; // 原始组织架构列表
    userList: any[] = [];
    pedding: boolean = false;
    creatorOrgPosInfo: any[] = [];
    partOfOrgPosInfo: any[] = [];
    uiModel: any = {
        switch: true
    };
    isWorkFlowDataReady: boolean = false;
    isUserInfoReady: boolean = false;
    applerOrgPosList: any[] = [];
    filterApplerData: (v) => any;
    applerList: any[] = [];

    constructor(
        private userSelectorService: UserSelectorService,
        private atoastr: SharkActionToastrService,
        private toastr: SharkToastrService,
        private router: Router,
        private ajax: Ajax,
        private userServ: UserService,
        private cmdbService: CmdbService
    ) {

    }

    async ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.isWorkFlowDataReady = true;
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
        }
        if (schanges.userInfo && schanges.userInfo.currentValue) {
            if (this.userInfo.uid) {
                // 获取用户组织架构信息
                try {
                    this.partOfOrgPosInfo = await this.getApplerOrgPosInfo(this.userInfo.uid);
                } catch (e) {
                    this.atoastr.error('当前用户不在组织架构内');
                    this.pedding = true;
                }
                this.creatorOrgPosInfo = this.userInfo.userCenterOrgPosVOS;
                this.sortoutCompleteOrgData(this.partOfOrgPosInfo, this.creatorOrgPosInfo); // 组织出一份完整的创建人组织架构数据


                this.isUserInfoReady = true;
            }
        }
        if (this.isUserInfoReady && this.isWorkFlowDataReady) {
            if (this.flowData.flowNodeMessage.applerUid) {    // 如果是从列表页进入编辑页面
                this.showData.rewriteAppler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
                this.showData.appler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
                const originOrgPos = this.flowData.flowNodeMessage.originOrgPos;
                for (const orgPosId in originOrgPos) {
                    if (originOrgPos.hasOwnProperty(orgPosId)) {
                        this.showData.orgPosTreeList.push(originOrgPos[orgPosId].name);
                    }
                }
            } else {
                await this.initData();
            }
        }
    }

    async initAppler() {
        await this.getApplerList(this.userInfo.uid);
        this.filterApplerData = (v) => {
            const list = [];
            for (const appler of this.applerList) {
                if ((appler.userName + appler.uid + appler.orgPosName).indexOf(v) > -1) {
                    list.push({
                        name: `${appler.userName}(${appler.uid}) ${appler.orgPosName}`,
                        userUid: appler.uid,
                        userName: appler.userName,
                        level: appler.level,
                        orgPosId: appler.orgPosId,
                        orgPosName: appler.orgPosName
                    });
                }
            }
            return list;
        };
    }
    // 获得创建人（指导员）所在部门的所有人员
    async getApplerList(instructorUid) {
        try {
            for (const orgPosId of this.applerOrgPosList) {
                const memberList = (await this.ajax.get(AjaxUrl.departure.getApplerList, {
                    orgPosId: orgPosId.orgPosId,
                    level: -1,
                    type: -1,
                    queryType: 0
                })).data;
                for (const member of memberList) {
                    if (!this.applerList.find((v) => v.uid == member.uid)) this.applerList.push(member);
                }
            }
        } catch (e) {
            this.atoastr.error('申请人列表获取失败');
        }
    }
    /**
     * 获取所有业务系统列表
     */
    async getProductList() {
        let { data } = await this.ajax.get(AjaxUrl.perm.getProductListInNav)
        if (data.length) {
            let _target = []
            data.forEach(element => {
                if (element.applications && element.applications.length) {
                    element.hasChildren = true
                    element.icon = ''
                    element.applications.forEach((child, index) => {
                        if (child.productId && child.cmdbProductCode && child.applicable) { // 测试环境存在部分无productid的产品，可以忽略这部分产品
                            child.id = child.productId
                            child.pid = element.id
                            child.icon = ''
                            _target.push(child)
                        }
                    });
                }
            })
            data = data.filter(element => element.applications.length)
            data = data.concat(_target)
            this.productList = data
            this.initProductList = JSON.parse(JSON.stringify(this.productList))
        }
    }
    async onChecked(event: SharkTreeCheckedEvent) {
        const applicantProductList = this.flowData.flowNodeMessage.applicantProductList || [];

        const productInfo = event.data.node;
        const cmdbProductCode = event.data.node.cmdbProductCode
        if (cmdbProductCode) {
            // 选中时
            if (event.data.isChecked) {

                const res = await this.cmdbService.getProductManage(cmdbProductCode)
                if (res.data) {

                    let manager = res.data.filter(managerInfo => managerInfo.role === 1)

                    if (manager.length > 0) {
                        // 有产品经理
                        applicantProductList.push({
                            productId: productInfo.productId,
                            productName: productInfo.name,
                            sensitiveLevel: productInfo.sensitiveLevel || 0,
                            cmdbProductCode: productInfo.cmdbProductCode,
                            // 归属业务部门
                            belongOrgPosId: productInfo.businessDepartment,
                            placeholder: productInfo.description || '请填写申请理由',
                            desc: ''
                        })
                        this.flowData.flowNodeMessage.applicantProductList = applicantProductList;
                    } else {
                        return this.toastr.error(`业务系统未配置产品经理，无法申请线上权限`)
                    }
                }
            } else {
                // 取消选中时，删除
                this.flowData.flowNodeMessage.applicantProductList = applicantProductList.filter((ele) => {
                    return ele.productId !== event.data.node.productId;
                })
            }
        }
    }


    // 初始化数据
    async initData() {
        this.flowData.flowNodeMessage.applerUid = this.userInfo.uid;
        this.flowData.flowNodeMessage.applerName = this.userInfo.userName;
        this.showData.rewriteAppler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
        this.showData.appler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
        // 整理页面展示的组织架构树,并整理传到后台的组织架构数据
        await this.getPosTree(this.creatorOrgPosInfo);
        // 查询申请人原始组织架构信息
        await this.getOriginOrgPos();
        // 获取产品信息
        await this.getProductList();
        this.setOriginOrgPosList();
    }

    // 整理一份完整的组织架构数据
    sortoutCompleteOrgData(partOfOrgPosInfo, userInfo) {
        for (const orgPosInfo of partOfOrgPosInfo) {
            for (const orgPosVos of userInfo) {
                for (const orgPosItem of orgPosVos.userCenterOrgPosItemVOList) {
                    if (orgPosInfo.orgPosId === orgPosItem.orgPosId) {
                        orgPosItem.type = orgPosInfo.type;
                    }
                }
            }
        }
    }

    setOriginOrgPosList() {
        const originOrgPosList = [];
        for (const orgPosId in this.originOrgPos) {
            if (this.originOrgPos.hasOwnProperty(orgPosId)) {
                originOrgPosList.push(this.originOrgPos[orgPosId].name);
            }
        }
        this.originOrgPosList = originOrgPosList;
    }

    // 获取申请人原始组织架构信息
    async getOriginOrgPos() {
        try {
            this.originOrgPos = (await this.ajax.postByJson(AjaxUrl.permcenter.getOriginOrgPosByUid, {
                uid: this.userInfo.uid
            })).data;
            return this.originOrgPos;
        } catch (e) {
            this.toastr.error(`查询用户原始组织架构信息失败: ${e.errorCode}`);
        }
    }
    // 整理页面展示的组织架构树,并整理传到后台的组织架构数据
    async getPosTree(creatorOrgPosInfo) {
        this.showData.orgPosTreeList = [];
        this.flowData.flowNodeMessage.originOrgPos = {};
        for (const item of creatorOrgPosInfo) {
            let _posTree = '';
            for (let i = 0; i < item.userCenterOrgPosItemVOList.length; i++) {
                _posTree = _posTree.concat(item.userCenterOrgPosItemVOList[i].orgPosName);

                if (i < item.userCenterOrgPosItemVOList.length - 1) {
                    _posTree = _posTree.concat('>');
                }

                if (i === item.userCenterOrgPosItemVOList.length - 1) {
                    this.flowData.flowNodeMessage.level = item.userCenterOrgPosItemVOList[i].level;
                    this.flowData.flowNodeMessage.orgPosId = item.userCenterOrgPosItemVOList[i].orgPosId;
                    this.flowData.flowNodeMessage.orgPosName = item.userCenterOrgPosItemVOList[i].orgPosName;
                    const orgPosId = this.flowData.flowNodeMessage.orgPosId;
                    this.flowData.flowNodeMessage.originOrgPos[orgPosId] = {};
                    this.flowData.flowNodeMessage.originOrgPos[orgPosId].name = _posTree;
                }
            }
            this.showData.orgPosTreeList.push({
                label: _posTree,
                value: item.userCenterOrgPosItemVOList[item.userCenterOrgPosItemVOList.length - 1].orgPosId
            });
            // 默认第一个组织架构
            if (!this.flowData.flowNodeMessage.orgPosId) {
                this.flowData.flowNodeMessage.orgPosId = this.showData.orgPosTreeList[0].value
            }
        }
        // 填充已选择产品列表
        if (this.flowData.flowNodeMessage.applicantProductList && this.flowData.flowNodeMessage.applicantProductList.length) {
            this.checkedList = []
            this.flowData.flowNodeMessage.applicantProductList.forEach(element => {
                this.checkedList.push(element.productId)
            });
        }
    }

    // 开始选择人员
    selectUsers() {
        this.userSelectorService.selectUsers({ list: this.userList }, (res) => {
            this.userList = res.data;
        });
    }
    // 获取申请人的OrgPos信息
    async getApplerOrgPosInfo(applerUid) {
        const applerOrgPosInfo = await this.ajax.get(AjaxUrl.departure.getUserOrgPosInfo, {
            uid: applerUid
        });
        return applerOrgPosInfo.data;
    }
    /**
     * 搜索商品，三种情况可搜索出来
     * 子节点包含关键字展示出来
     * 已勾选的节点展示出来
     *
     * @memberof PermEditComponent
     */
    search() {
        this.productList = this.initProductList
    }

    save() {
        this.form.submit(async () => {
            const applicantProductList = this.flowData.flowNodeMessage.applicantProductList || [];

            if (!applicantProductList.length) {
                this.toastr.error('请先选择需要申请权限的系统权限')
                return false
            }
            this.orgSubmitData();
            // 检查当前用户的职位级别是否在二级架构中
            let orgTree = await (this.ajax.get(AjaxUrl.permcenter.getUppserOrgPos, {
                orgPosId: this.flowData.flowNodeMessage.orgPosId
            }))
            let level = orgTree.data.filter(element => element.orgPosId === this.flowData.flowNodeMessage.orgPosId)[0].level

            level >= 98 ? this.flowData.flowNodeMessage.underThirdLevel = false : this.flowData.flowNodeMessage.underThirdLevel = true
            this.flowData.flowNodeMessage.approved = true
            // 检查所选的产品是否都有关联cmdb产品信息，并有产品经理角色账号
            const subFlowList = await Promise.all(applicantProductList.map(element => this.cmdbService.getProductManage(element.cmdbProductCode)))

            if (!subFlowList.every((element: any) => { return element.data.filter(child => { return child.role === 1 }).length })) {
                this.toastr.error('部分系统未设置产品经理，无法进行线上权限申请')
                return false
            }
            try {
                this.pedding = true;
                await this.ajax.postByJson(AjaxUrl.submitFlow.perm, {
                    ...this.flowData
                });
                this.pedding = false;
                this.toastr.success('工单提交成功!');
                this.router.navigate(['/workflow/createdList']);
            } catch (error) {
                this.atoastr.error('工单提交失败!');
            }
        });
    }

    orgSubmitData() {
        this.flowData.flowNodeMessage.trackList = this.userList.map((user) => {
            return user.uid;
        });
        this.flowData.flowNodeMessage.trackFullList = this.userList;
    }
}
