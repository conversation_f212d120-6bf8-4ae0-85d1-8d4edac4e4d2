<div class="section" [ngClass]="{'section-fold': !uiModel.switch}">
    <div class="section-header section-header-bordered" style="cursor: pointer;" (click)="uiModel.switch = !uiModel.switch">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="uiModel.switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!uiModel.switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block">
        <form class="form form-horizontal" shark-validform #form="shark-validform" autocomplete="off">
            <table class='table table-full text-center'>
                <thead>
                    <tr>
                        <th>工单ID</th>
                        <th>文件编号</th>
                        <th>文件名称</th>
                        <th>文件密级</th>
                        <th>使用范围</th>
                        <th>文件类型</th>
                        <th>所属部门</th>
                        <th>创建人</th>
                        <th>审批人</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{flowData?.flowMetaData?.flowId}}</td>
                        <td>{{flowData?.flowNodeMessage?.content.number}}</td>
                        <td>{{flowData?.flowNodeMessage?.content.name}}</td>
                        <td class="text-nowrap" name="文件密级">
                            <span [ngSwitch]="flowData?.flowNodeMessage?.content.confidentiality">
                                <span *ngSwitchCase="1">
                                    R.01绝密级
                                </span>
                                <span *ngSwitchCase="2">
                                    R.02机密级
                                </span>
                                <span *ngSwitchCase="3">
                                    R.03秘密级
                                </span>
                                <span *ngSwitchCase="4">
                                    R.04普一级
                                </span>
                                <span *ngSwitchCase="5">
                                    R.05普二级
                                </span>
                                <span *ngSwitchCase="6">
                                    R.06普三级
                                </span>
                                <span *ngSwitchDefault>

                                </span>
                            </span>
                        </td>
                        <td class="text-nowrap" name="使用范围">{{flowData?.flowNodeMessage?.content.range}}</td>
                        <td class="text-nowrap" name="文件类型">
                            <span *ngIf='this.flowData?.flowNodeMessage?.content.categoryList.length >=
                                   0'>{{this.flowData?.flowNodeMessage?.content.categoryList[0]}}</span>
                        </td>
                        <td class="text-nowrap" name="所属部门">
                            {{flowData?.flowNodeMessage?.content.mainDepartment}}
                        </td>
                        <td class="text-nowrap" name="创建人">
                            {{flowData?.flowNodeMessage?.content.createUser}}
                            <p>{{flowData?.flowNodeMessage?.content.creatorUId}}</p>
                        </td>
                        <td class="text-nowrap" name="审核人">
                            <popo *ngFor="let uid of flowData?.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                            <span *ngIf="flowData?.flowNodeMessage?.acceptorList.length === 0">/</span>
                        </td>
                        <td>{{flowData?.flowNodeMessage?.content.createTime | date: 'yyyy-MM-dd HH:mm:ss'}}</td>
                    </tr>
                </tbody>
            </table>
            <div class="form-item">
                <div class="form-item-text">
                    <button class="btn btn-link" (click)='toDoc()'>成文文件详情</button>
                </div>
            </div>
            <div class="form-item">
                <div class="col-offset-4 col-20">
                    <button class="btn btn-primary" type="button" (click)="save()" [disabled]="pedding">提交</button>
                </div>
            </div>
        </form>
    </div>
</div>
