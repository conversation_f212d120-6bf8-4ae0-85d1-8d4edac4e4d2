import { Service } from '@tiger/boot';
import { appLogger } from '@tiger/logger';
import axios from '@tiger/request';
import { Context } from 'koa';
import { isObjectExisted } from '../../common/util';
import { cmdbDomain } from '../../conf';

const errData: any = {
    404: '404，请 <a href="https://yx.mail.netease.com/openid/login?url=http%3A%2F%2Fyx.mail.netease.com%2F">重新登录</a>',
    403: '暂无权限',
    401004: '暂无权限',
    401002: '404，请 <a href="https://yx.mail.netease.com/openid/login?url=http%3A%2F%2Fyx.mail.netease.com%2F">重新登录</a>'
};

@Service
export class ErrorService {

    async renderUnauthorized(ctx: Context) {
        const product = ctx.query.product;

        // 有product参数再去查询
        const productInfo = await this.getProductInfo(product);

        return await ctx.render('error', Object.assign({
            code: ctx.params.code,
            content: `${productInfo.productName}权限申请流程`,
            productName: `${productInfo.productName}`,
            username: ctx.openIDInfo.fullname,
            product
        }, productInfo));
    }

    async renderNotFound(ctx: Context) {
        return await ctx.render('error', {
            code: ctx.params.code,
            content: errData[ctx.params.code],
            username: ctx.openIDInfo.fullname
        });
    }

    async renderNotFoundOrg(ctx: Context) {
        return await ctx.render('notfoundorg', {
            username: ctx.openIDInfo.fullname,
            title: '组织架构申请'
        });
    }

    private async getProductInfo(product: string) {
        const productInfo = {
            productName: '',
            productManager: []
        };
        try {
            const pres = await axios.get(`${cmdbDomain}/product/${product}`);
            if (pres.data && pres.data.code !== 200) {
                throw new Error(`getProductInfo error: ${product}, res code: ${pres.data.code}`);
            }
            productInfo.productName = pres.data.data.name;

            const res = await axios.get(`${cmdbDomain}/product/${product}/user`);

            if (res.data && res.data.code !== 200) {
                throw new Error(`getProductUser error: ${product}, res code: ${res.data.code}`);
            }
            if (!isObjectExisted(res.data.data)) {
                throw new Error(`product not found: ${product}`);
            }
            productInfo.productManager = res.data.data.filter((x: any) => x.role === 1);
        } catch (e) {
            appLogger.error(e);
        }
        return productInfo;
    }

}
