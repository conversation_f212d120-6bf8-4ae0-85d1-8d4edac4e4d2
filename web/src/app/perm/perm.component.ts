import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SharkToastrService, Ajax } from '@shark/shark-angularX';
import { YXWorkFlowBaseComponent, WorkFlowData } from '@eagle/workflow';
import { UserService } from '../shared/services/user.service';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './perm.component.html'
})
export class PermComponent extends YXWorkFlowBaseComponent {
    url: string = AjaxUrl.perm.getDetal;
    workFlowData: any = {
        flowData: null
    }
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    sublist: any[] = []
    userInfo: any = {};
    showChildren: boolean = true
    flowId: string

    constructor(
        private sharkToastrService: SharkToastrService,
        protected activatedRoute: ActivatedRoute,
        private userServ: UserService,
        // private activatedRoute: ActivatedRoute,
        protected ajax: Ajax,
    ) {
        super(activatedRoute, ajax, sharkToastrService);
        this.userServ.getUserInfo().then((userInfo) => {
            this.userInfo = userInfo.data;
        });
    }
    onFlowDataChange(data: WorkFlowData): void {
        document.title = '严选业务门户-业务系统权限申请工单';
        this.flowId = this.activatedRoute.snapshot.params['flowId']
        super.onFlowDataChange(data);
        this.workFlowData = data || {
            flowData: {
                flowMetaData: {},
                flowNodeMessage: {}
            }
        }
        this.flowData = this.workFlowData.flowData
    }
    ngOnDestroy() {
        document.title = '严选业务门户';
    }
    showEdit() {
        const uid = this.userInfo.uid;
        if (!this.flowId || (this.flowData.flowMetaData.currentNodeId === '30010505' && this.flowData.flowNodeMessage.createUser === this.userInfo.uid)) {
            return true;
        }
        if (this.flowData.flowNodeMessage.businessStatus === -1 && uid === this.flowData.flowNodeMessage.createUser) {
            return true;
        }
        return false;
    }

    showApprove() {
        const uid = this.userInfo.uid;
        if ((this.flowData.flowNodeMessage.acceptorList || []).includes(uid)) {
            return true;
        } else {
            return false;
        }
    }

    spread() {
        this.showChildren = !this.showChildren
        if (!this.showChildren && this.flowData.flowNodeMessage.subList.length) {
            this.ajax.post(AjaxUrl.permChild.getDetail, { sublist: this.flowData.flowNodeMessage.subList, topologyId: 'nav_personnelchange_perm_child' })
                .then((data) => {
                    this.sublist = data.data
                })
        }
    }
}
