import { FlowData, FlowSubmitData } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { contextPath } from '../../../../conf';
import { topologyName } from '../transfer-config';
import Workflow from '../transfer-workflow';
import { checkMoveIcacUser } from '../transfer.service';

type SubmitResult = AjaxResult<string[]>;
type UpsertResult = AjaxResult<string>;

/**
 * 工单创建与提交
 */
@RestController
@RequestMapping(`${contextPath}/xhr/workflow/${topologyName}`)
export class TransferWorkflowController {

    constructor(private workflow: Workflow) {
    }

    /**
     * 工单创建并提交，触发流程流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/submit.json')
    async submit(ctx: RequestContext<FlowSubmitData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 如果是创建工单，则需要预检验
        if (!flowMetaData.flowId) {
            try {
                const res = await checkMoveIcacUser({
                    uid: flowNodeMessage.applerUid,
                    // 组织职位Id
                    orgPosIds: flowNodeMessage.transferOrgPos.join(','),
                    // 目标职位Id
                    targetOrgPosId: flowNodeMessage.orgPosId,
                    // 移动后用户类型
                    targetType: flowNodeMessage.applerRoleType

                });
                if (res.data.code !== 200) {
                    throw new Error(res.data.errorCode);
                }
            } catch (e) {
                console.error('转岗工单校验失败:', e);
                ctx.body = AjaxResult.internal(`${e}`);
                return;
            }
        }
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            console.error('工单提交失败:', e);
            ctx.body = AjaxResult.internal('工单提交失败');
        }
    }

    /**
     * 工单创建与保存草稿，不提交，不触发流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/upsert.json')
    async upsert(ctx: RequestContext<FlowData, UpsertResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const flowId = await this.workflow.upsert(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(flowId);
        } catch (e) {
            console.error('工单保存失败:', e);
            ctx.body = AjaxResult.internal('工单保存失败');
        }
    }
}
