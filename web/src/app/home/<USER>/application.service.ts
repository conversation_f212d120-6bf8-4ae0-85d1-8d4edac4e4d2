import { Injectable } from '@angular/core';
import { Ajax } from '@shark/shark-angularX';
import { AppConfig } from '../../config';
import { IAppDataFromIcac } from './app.interface';

@Injectable()
export class ApplicationService {
    // 获取应用列表

    constructor(private ajax: Ajax) { }

    // 获取应用列表
    list() {
        return this.ajax.get(`${AppConfig.contextPath}/xhr/user/getTagsWithApplications.do`);
    }

    // 获取当前用户收藏的应用
    getFavApps() {
        return this.ajax.get(`${AppConfig.contextPath}/xhr/user/getFavApps.do`);
    }

    addFavApp(appId) {
        return this.ajax.postByJson(`${AppConfig.contextPath}/xhr/user/addFavApp.do`, {
            appId
        });
    }

    deleteFavApp(appId) {
        return this.ajax.postByJson(`${AppConfig.contextPath}/xhr/user/cancelFavApp.do`, {
            appId
        });
    }

    /**
     *
     * 从 icac 获取当前用户有权限的应用 --暂时没有地方用到 yangbo 
     * @returns {Promise<appDataFromIcac>}
     * @memberof ApplicationService
     */
    getUsersApp(): Promise<IAppDataFromIcac[]> {
        return this.ajax.get('/icac/webapi/xhr/nav/getMyProductPermission.do');
    }
}
