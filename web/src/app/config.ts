const AppConfig = {
    contextPath: '/bflow-base',
    productCode: 'navAdmin'
};
const BpmFlowNames = {
    WRITTEN_DOC_PROJECT:'written_doc_project',
    NAV_PRODUCT_PERM: 'nav_product_perm',
    NAV_PERSONNELCHANGE_DEPARTURE: 'nav_personnelchange_departure',
    NAV_PERSONNELCHANGE_ONBOARD: 'nav_personnelchange_onboard',
    NAV_PERSONNELCHANGE_TRANSFER: 'nav_personnelchange_transfer',
    NAV_PERSONNELCHANGE_PERM: 'nav_personnelchange_perm',
    NAV_PERSONNELCHANGE_PERM_CHILD: 'nav_personnelchange_perm_child',
    WRITTEN_DOC_DELETE_PROJECT: 'written_doc_delete_project'
};
const AjaxUrl = {
    user: {
        getUserInfo: `${AppConfig.contextPath}/xhr/user/getUserInfo.json`,
        getUserFullInfo: `${AppConfig.contextPath}/xhr/icac/icac/webapi/xhr/user/getUserInfo.do`,
        getDetailUserInfo: `${AppConfig.contextPath}/xhr/user/getDetailUserInfo.do`
    },
    permcenter: {
        getOrgPosMetaTree: `${AppConfig.contextPath}/xhr/userCenterManage/navAdmin/external/uniteOrg/getPartOrgPosMetaTree.json`,
        getUppserOrgPos: `${AppConfig.contextPath}/xhr/userCenterManage/navAdmin/user/uniteOrg/allTeam/listUpperByOrgPosId.json`,
        getOriginOrgPosByUid: `${AppConfig.contextPath}/xhr/unitorg/getOriginOrgPosByUid.json`,
        getOrgPosByUid: `${AppConfig.contextPath}/xhr/userCenterManage/${AppConfig.productCode}/user/uniteOrg/allTeam/listIcacOrgPosIdByUidAndType.json`,
        getUppserOrgPosMap: `${AppConfig.contextPath}/xhr/unitorg/getUppserOrgPosMap.json`
    },
    bpmflow: {
        queryFlowMap: `${AppConfig.contextPath}/xhr/bpmFlow/queryFlowMap.json`,
        queryFlowHistory: `${AppConfig.contextPath}/xhr/bpmFlow/queryHistory.json`,
        queryFlowDetail: `${AppConfig.contextPath}/xhr/bpmFlow/queryFlowDetail.json`,
        queryCreatedList: `${AppConfig.contextPath}/xhr/bpmFlow/queryCreatedList.json`,
        queryDealedList: `${AppConfig.contextPath}/xhr/bpmFlow/queryDealedList.json`,
        queryDealingList: `${AppConfig.contextPath}/xhr/bpmFlow/queryDealingList.json`,
        queryTrailedList: `${AppConfig.contextPath}/xhr/bpmFlow/queryTrailedList.json`,
        queryMonitorList: `${AppConfig.contextPath}/xhr/bpmFlow/queryMonitorList.json`,
        queryFlowCount: `${AppConfig.contextPath}/xhr/bpmOrder/${AppConfig.productCode}/msgQuery/count`
    },
    createFlow: {
        onboard: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_onboard/upsert.json`,
        transfer: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_transfer/upsert.json`,
        departure: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_departure/upsert.json`,
        perm: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_perm/upsert.json`,
        permChild: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_perm_child/upsert.json`,
        flowStatus: `${AppConfig.contextPath}/xhr/flowServer/workflow/apply/v1/flowStatus`,
        batchApproval: `${AppConfig.contextPath}/xhr/flowServer/workflow/apply/v1/batchApproval`,
        externalBatchApprove: `${AppConfig.contextPath}/xhr/flowServer/v1/flowx/external/system/doBatchSubmit`,
        externalBatchClear: `${AppConfig.contextPath}/xhr/flowServer/v1/flowx/external/system/authClear/doBatchSubmit`,
        urge: `${AppConfig.contextPath}/xhr/flowServer/workflow/apply/v1/urge`,
        withdraw: `${AppConfig.contextPath}/xhr/flowServer/workflow/apply/v1/withdraw`,
    },
    updateFlow: {
        onboard: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_onboard/update.json`,
        transfer: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_transfer/update.json`,
        departure: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_departure/update.json`,
        perm: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_perm/update.json`,
        permChild: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_perm_child/update.json`
    },
    submitFlow: {
        onboard: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_onboard/submit.json`,
        transfer: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_transfer/submit.json`,
        departure: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_departure/submit.json`,
        perm: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_perm/submit.json`,
        permChild: `${AppConfig.contextPath}/xhr/workflow/nav_personnelchange_perm_child/submit.json`
    },
    departure: {
        getPartLeader: AppConfig.contextPath + '/xhr/userCenterManage/navAdmin/user/uniteOrg/getPartLeader.json',
        getOrgPostId: AppConfig.contextPath + '/xhr/userCenterManage/navAdmin/user/uniteOrg/allTeam/listIcacOrgPosIdByUidAndType.json',
        getApplerList: AppConfig.contextPath + '/xhr/userCenterManage/navAdmin/user/uniteOrg/fuzzyPartSubUserByOrgPosId.json',
        getUserOrgPosInfo: AppConfig.contextPath + '/xhr/userCenterManage/navAdmin/user/uniteOrg/listOrgPosUserByUid.json'
    },
    mockNotify: {
        nav_personnelchange_onboard: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_onboard/notify.json`,
        nav_personnelchange_departure: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_departure/notify.json`,
        nav_personnelchange_transfer: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_transfer/notify.json`,
        nav_personnelchange_perm: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_perm/notify.json`,
        nav_personnelchange_perm_child: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_perm_child/notify.json`
    },
    mockNotifyEnd: {
        nav_personnelchange_onboard: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_onboard/notifyEnd.json`,
        nav_personnelchange_departure: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_departure/notifyEnd.json`,
        nav_personnelchange_transfer: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_transfer/notifyEnd.json`,
        nav_personnelchange_perm: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_perm/notifyEnd.json`,
        nav_personnelchange_perm_child: `${AppConfig.contextPath}/xhr/manual/workflow/nav_personnelchange_perm_child/notifyEnd.json`
    },
    perm: {
        getProductList: `${AppConfig.contextPath}/xhr/userCenterManage/navAdmin/product/getAllProduct.json`,
        getApplerList: AppConfig.contextPath + '/xhr/userCenterManage/navAdmin/user/uniteOrg/fuzzyPartSubUserByOrgPosId.json',
        getProductListInNav: `${AppConfig.contextPath}/xhr/user/getTagsWithApplications.do`,
        getDetal: `${AppConfig.contextPath}/xhr/bpmFlow/get.json`,
    },
    permChild: {
        getDetail: `${AppConfig.contextPath}/xhr/bpmFlow/getSub.json`,
    },
    cmdb: {
        getRoles: `${AppConfig.contextPath}/xhr/cmdb/getCmdbRoles.json`,
    },
    org: {
        checkInOrg: `${AppConfig.contextPath}/xhr/icac/icac/webapi/xhr/user/checkInOrg.do`
    },
    docManagement: {
        queryDocumentList: `${AppConfig.contextPath}/xhr/bpmFlow/queryDocumentList.json`,
        upload: `${AppConfig.contextPath}/xhr/fms/upload.do`,
        submitTrain: `${AppConfig.contextPath}/xhr/workflow/written_doc_project/submit.json`,
        submitTrainDel: `${AppConfig.contextPath}/xhr/workflow/written_doc_delete_project/submit.json`,
        export: `${AppConfig.contextPath}/xhr/bpmFlow/exportDocumentList.json`,
        docAuthority: `${AppConfig.contextPath}/xhr/workflow/written_doc_authority_project/submit.json`,
        getOrgName: `${AppConfig.contextPath}/xhr/workflow/written_doc_project/getOrgName.do`,
        revoke: `${AppConfig.contextPath}/xhr/workflow/written_doc_project/revoke.do`, // 撤回工单
        revokeDel: `${AppConfig.contextPath}/xhr/workflow/written_doc_delete_project/revoke.do`, // 撤回作废工单，
        reminders: `/written-doc/xhr/proxy/xhr/file/orderUrging`, // 催办
    }
};

let flowServerHost = '/xhr/flowServer'

if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    flowServerHost = '/xhr';
}
export {
    AppConfig,
    AjaxUrl,
    BpmFlowNames,
    flowServerHost
};
