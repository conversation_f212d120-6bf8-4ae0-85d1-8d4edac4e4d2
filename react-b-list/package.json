{"name": "react-b-list", "version": "0.0.0", "description": "A blank tool", "scripts": {"build": "node ./scripts/build.js", "build_schema": "node ./scripts/build_schema.js", "test": "npm run build && jasmine src/**/*_spec.js"}, "keywords": ["tools", "muse-tools"], "author": "yanxuan group", "license": "MIT", "muse-tools": "./src/collection.json", "dependencies": {"@muses/cli": "^0.0.71", "typescript": "~3.5.3"}, "devDependencies": {"@types/node": "^12.11.1", "@types/jasmine": "~3.5.0", "quicktype-core": "6.0.69", "jasmine": "^3.5.0"}}