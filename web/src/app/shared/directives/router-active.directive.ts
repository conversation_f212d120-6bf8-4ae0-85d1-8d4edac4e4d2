import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import { Event, NavigationEnd, Router } from '@angular/router';
import { Common } from '@shark/shark-angularX';
import { Subscription } from 'rxjs';

@Directive({
    selector: '[router-active]',
    exportAs: 'router-active'
})
export class RouterActiveDirective {
    @Input('router-active') routerActive: string[] | string;
    subscription: Subscription;
    private element;
    constructor(
        private elementRef: ElementRef,
        private renderer2: Renderer2,
        private router: Router,
        private common: Common
    ) {
        this.element = this.elementRef.nativeElement;
        this.subscription = this.router.events.subscribe((event: Event) => {
            if (event instanceof NavigationEnd) {
                this.handUrl();
            }
        });
    }

    ngOnChanges() {
        this.handUrl();
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    private formatPath(path) {
        if (path.charAt(0) !== '/') {
            return '/' + path;
        } else {
            return path;
        }
    }

    private meetWithPath(currentPath, path) {
        const currentTmp = this.formatPath(currentPath);
        const pathTmp = this.formatPath(path);

        /**********特殊处理**************/
        if (currentTmp === '/workflow-detail') {
            return new Promise((resolve) => {
                const parentPath = this.formatPath(decodeURIComponent(this.common.getUrlParamByName(this.router.url, 'parentPath')));
                if (parentPath === pathTmp) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        }
        /************************/
        if (currentTmp === pathTmp || (currentTmp.length > pathTmp.length && currentTmp.indexOf(pathTmp) === 0 && (currentTmp.charAt(pathTmp.length) === '/' || currentTmp.charAt(pathTmp.length) === '?'))) {
            return new Promise((resolve) => {
                resolve(true);
            });
        } else {
            return new Promise((resolve) => {
                resolve(false);
            });
        }
    }

    private toggleActive(flag) {
        if (flag) {
            this.renderer2.addClass(this.element, 'active');
        } else {
            this.renderer2.removeClass(this.element, 'active');
        }
    }

    private handUrl() {
        if (this.routerActive) {
            const currentPath = this.router.url.split(';')[0];
            if (typeof this.routerActive === 'string') {
                const promise = this.meetWithPath(currentPath, this.routerActive);
                promise.then((flag) => {
                    this.toggleActive(flag);
                }, () => { });
            } else if (Array.isArray(this.routerActive)) {
                const promiseList = [];
                for (const item of this.routerActive) {
                    promiseList.push(this.meetWithPath(currentPath, item));
                }
                Promise.all(promiseList).then((flagList) => {
                    this.toggleActive(flagList.indexOf(true) > -1);
                }, () => { });
            }
        }
    }
}
