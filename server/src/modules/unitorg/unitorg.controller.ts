import { GetMapping, PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxR<PERSON>ult, QueryContext } from '@tiger/core';
import { Context } from 'koa';
import { contextPath, xhrPrefix } from '../../conf';
import { getAllUpperByOrgUserId, getUpperByOrgPosId } from '../service/permcenter/unitOrg';
import { EmployeeRoleType } from './employeeRoleType';

/**
 * 组织架构查询
 */
@RestController
@RequestMapping(`${contextPath}${xhrPrefix}/unitorg`)
export class UnitOrgController {

    constructor(
    ) { }

    @PostMapping('/getOriginOrgPosByUid.json')
    async getOriginOrgPosByUid(ctx: Context) {
        const { uid } = ctx.request.body;
        try {
            const upperOrgPosMap = await getAllUpperByOrgUserId(uid);
            const translatedMap: any = {};
            for (const orgPosId in upperOrgPosMap) {
                if (upperOrgPosMap.hasOwnProperty(orgPosId)) {
                    const upperOrgPos = upperOrgPosMap[orgPosId];
                    const orgPosNameList = [];
                    let level = null;
                    for (let len = upperOrgPos.length, i = len - 1; i >= 0; i--) {
                        const orgPos = upperOrgPos[i];
                        if (i !== 0) {
                            orgPosNameList.push(orgPos.orgPosName);
                            continue;
                        }
                        for (const orgUser of orgPos.orgPosUniteUsers) {
                            if (orgUser.uid === uid) {
                                const roleType = EmployeeRoleType[orgUser.type];
                                orgPosNameList.push(`${orgPos.orgPosName}(${roleType})`);
                                level = orgPos.level;
                                break;
                            }
                        }
                    }
                    translatedMap[orgPosId] = {
                        name: orgPosNameList.join('>'),
                        level
                    };
                }
            }
            ctx.body = AjaxResult.success(translatedMap);
        } catch (e) {
            console.error(`查询用户所在组织架构失败`, e);
            ctx.body = AjaxResult.internal('查询用户所在组织架构失败');
        }
    }

    @PostMapping('/getUppserOrgPosMap.json')
    async getUppserOrgPosMap(ctx: Context) {
        const { orgPosId } = ctx.request.body;
        try {
            const upperOrgPosMap = await getUpperByOrgPosId(orgPosId);
            ctx.body = AjaxResult.success(upperOrgPosMap);
        } catch (e) {
            console.error(`查询上级组织架构失败`, e);
            ctx.body = AjaxResult.internal('查询上级组织架构失败');
        }
    }

}
