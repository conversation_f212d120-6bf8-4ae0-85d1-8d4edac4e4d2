import { NgModule } from '@angular/core';
import { YXWrokFlowConfig } from '@shark/shark-yx-workflow';
import { BpmFlowNames } from '../config';
import { SharedModule } from '../shared/shared.module';
import { OnboardApproveComponent } from './approve.component';
import { OnboardDetailComponent } from './detail.component';
import { OnboardEditComponent } from './edit.component';
import { OnboardComponent } from './onboard.component';

@NgModule({
    imports: [SharedModule],
    declarations: [
        OnboardComponent,
        OnboardEditComponent,
        OnboardDetailComponent,
        OnboardApproveComponent
    ],
    entryComponents: [OnboardComponent]
})
export class OnBoardModule {
    constructor() {
        YXWrokFlowConfig.addDetailComponent(BpmFlowNames.NAV_PERSONNELCHANGE_ONBOARD, {
            component: OnboardComponent,
            excludeNodeIdList: ['30010310']
        });
    }
}
