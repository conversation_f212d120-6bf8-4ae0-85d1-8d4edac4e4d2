import {ModuleWithProviders, NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {RouterModule} from '@angular/router';
// module
import {SharedModule} from '../shared/shared.module';
import {ApplicationListComponent} from './application/list.component';
import {TagBlock} from './application/tagBlock/tagBlock.component';
// component
import {HomeComponent} from './home.component';

// service
import {ApplicationService} from './application/application.service';

const routes = [
    {
        path: 'home',
        component: HomeComponent
    },
    {
        path: '',
        component: HomeComponent
    }
];
const routing: ModuleWithProviders = RouterModule.forChild(routes);
const components = [HomeComponent, ApplicationListComponent, TagBlock];

@NgModule({
    imports: [
        routing,
        SharedModule,
        BrowserModule
    ],
    declarations: [...components],
    providers: [ApplicationService]
})
export class HomeModule {}
