import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { UserSelectorService } from '@yx-module/umc-selector';
import { AjaxUrl } from '../config';
import { EmployeeRoleTypePipe, EmployeeTypePipe } from '../shared/pipes/employee.pipe';

@Component({
    templateUrl: './edit.component.html',
    selector: 'onboard-edit'
})
export class OnboardEditComponent {
    @ViewChild('form') form;
    @Input()
    workFlowData: WorkFlowData = null;
    @Input()
    userInfo: any = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };

    userlist: any[] = []; // 开始的跟踪用户列表
    selectedOrgId: any[] = []; // 用户选中的组织架构id

    originOrgPos: any = {}; // 申请人原始所在组织架构信息
    originOrgPosList: any[] = []; // 原始组织架构列表

    applerRoleTypeOption: any = [];
    applerTypesOption: any = [];
    orgPosDataOption: any = [];

    switch: boolean = true;
    pedding: boolean = false;
    isWorkFlowDataReady: boolean = false;
    isUserInfoReady: boolean = false;
    isInOrgUnit: boolean = true; // 是否在组织架构内
    constructor(
        private router: Router,
        private toastr: SharkToastrService,
        private ajax: Ajax,
        private userSelectorService: UserSelectorService,
        private roleTypePipeServ: EmployeeRoleTypePipe,
        private typePipeServ: EmployeeTypePipe
    ) {
        this.initDataOption();
    }

    ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.isWorkFlowDataReady = true;
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
        }
        if (schanges.userInfo && schanges.userInfo.currentValue) {
            if (this.userInfo.email) {
                this.isUserInfoReady = true;
            }
        }
        if (this.isUserInfoReady && this.isWorkFlowDataReady) {
            this.initData();
        }
    }

    async initData() {
        this.userlist = this.flowData.flowNodeMessage.trackFullList || [];
        this.flowData.flowNodeMessage.applerUid = this.userInfo.email;
        this.flowData.flowNodeMessage.applerName = this.userInfo.fullname;
        if (this.flowData.flowNodeMessage.orgPosId) {
            this.selectedOrgId = [this.flowData.flowNodeMessage.orgPosId];
        }
        this.setInOrgUnitFlag();
        // 查询申请人原始组织架构信息
        await this.getOriginOrgPos();
        this.setOriginOrgPosList();
    }

    setOriginOrgPosList() {
        const originOrgPosList = [];
        for (const orgPosId in this.originOrgPos) {
            if (this.originOrgPos.hasOwnProperty(orgPosId)) {
                originOrgPosList.push(this.originOrgPos[orgPosId].name);
            }
        }
        this.originOrgPosList = originOrgPosList;
    }

    // 获取申请人原始组织架构信息
    async getOriginOrgPos() {
        try {
            this.originOrgPos = (await this.ajax.postByJson(AjaxUrl.permcenter.getOriginOrgPosByUid, {
                uid: this.userInfo.email
            })).data;
            return this.originOrgPos;
        } catch (e) {
            this.toastr.error(`查询用户原始组织架构信息失败: ${e.errorCode}`);
        }
    }
    async setInOrgUnitFlag() {
        try {
            const rst = await this.ajax.get(AjaxUrl.permcenter.getOrgPosByUid, {
                uid: this.userInfo.email
            });
            const orgPosIds = rst.data || [];
            if (orgPosIds.length > 0) {
                this.toastr.warning('您已在组织架构中');
                // this.isInOrgUnit = true;
                this.isInOrgUnit = false;
            } else {
                this.isInOrgUnit = false;
            }
        } catch (e) {
            this.toastr.error(`查询用户是否在组织架构内失败: ${e.errorCode}`);
        }
    }

    async initDataOption() {
        this.applerRoleTypeOption = this.roleTypePipeServ.transform('', true);
        this.applerTypesOption = this.typePipeServ.transform('', true);
        this.orgPosDataOption = await this.getOrgPosDataOption();
    }

    async getOrgPosDataOption() {
        const rst = await this.ajax.postByJson(AjaxUrl.permcenter.getOrgPosMetaTree, {deptTag: 'yanxuan'});
        const orgPosts = this.filterOrgPosMetaTree(rst.data);
        let orgPosTreeOption = [];
        for (const orgPos of orgPosts) {
            orgPosTreeOption = orgPosTreeOption.concat(orgPos.orgPosDTOS);
        }
        return orgPosTreeOption;
    }

    filterOrgPosMetaTree(orgPosTree) {
        // 默认访客组需要过滤掉
        const ExcludeOrgPosIds = [587];
        for (let len = orgPosTree.length, i = len - 1; i >= 0; i--) {
            const orgPos = orgPosTree[i];
            const orgPosDTOS = [];
            for (let j = 0, length = orgPos.orgPosDTOS.length; j < length; j++) {
                const orgPosId = orgPos.orgPosDTOS[j].orgPosId;
                const parentOrgPosId = orgPos.orgPosDTOS[j].parentOrgPosId;
                if (!ExcludeOrgPosIds.includes(orgPosId) && !ExcludeOrgPosIds.includes(parentOrgPosId)) {
                    orgPosDTOS.push(orgPos.orgPosDTOS[j]);
                    continue;
                }
                if (ExcludeOrgPosIds.includes(parentOrgPosId)) {
                    ExcludeOrgPosIds.push(orgPosId);
                }
            }
            orgPos.orgPosDTOS = orgPosDTOS;
        }
        return orgPosTree;
    }

    onOrgPosSelected(event) {
        const selected = event.data.node;
        this.flowData.flowNodeMessage.orgPosId = selected.orgPosId;
        this.flowData.flowNodeMessage.orgPosName = selected.orgPosName;
        this.flowData.flowNodeMessage.parentOrgPosId = selected.parentOrgPosId;
        this.flowData.flowNodeMessage.level = selected.level;
        this.validOrgPos();
    }

    // 开始选择人员
    selectUsers() {
        this.userSelectorService.selectUsers({ list: this.userlist }, (res) => {
            this.userlist = res.data;
        });
    }

    submit() {
        this.form.submit(async () => {
            this.pedding = true;
            const validRst = await this.validFormData();
            if (!validRst) {
                this.pedding = false;
                return;
            }
            this.orgSubmitData();
            try {
                const orgPosPath = await this.getUpperOrgPos();
                this.flowData.flowNodeMessage.orgPosPath = orgPosPath;
            } catch (e) {
                this.pedding = true;
                this.toastr.error(`查询员工待加入的组织架构信息失败:${e.errorCode}`);
                return;
            }
            try {
                const result = await this.ajax.postByJson(AjaxUrl.submitFlow.onboard, {
                    ...this.flowData
                });
                this.pedding = false;
                this.toastr.success('工单提交成功，请耐心等待审核。');
                this.router.navigate(['/workflow/createdList']);

            } catch (e) {
                this.pedding = false;
                this.toastr.error(`工单提交失败:  ${e.message ? e.message : e.errorCode}`);
            }
        });
    }

    async getUpperOrgPos() {
        const orgPosId = this.flowData.flowNodeMessage.orgPosId;
        const upperOrgs = (await this.ajax.get(AjaxUrl.permcenter.getUppserOrgPos, {
            orgPosId
        })).data;
        const orgPosList = [];
        for (const upperOrg of upperOrgs) {
            orgPosList.unshift(upperOrg.orgPosName);
        }
        return orgPosList.join('>');
    }

    orgSubmitData() {
        this.flowData.flowNodeMessage.trackList = this.userlist.map((user) => {
            return user.uid;
        });
        this.flowData.flowNodeMessage.trackFullList = this.userlist;
    }

    async validFormData() {
        const orgValidRst = await this.validOrgPos();
        return orgValidRst;
    }

    async validOrgPos() {
        const level = this.flowData.flowNodeMessage.level;
        const orgPosId = this.flowData.flowNodeMessage.orgPosId;
        if (!orgPosId) {
            this.toastr.error('请选择要加入的组');
            return false;
        }
        // 校验是否选择了顶层组织
        if (level > 98) {
            this.toastr.error('不能选择顶层组织');
            return false;
        }
        // 校验所选则的组织的二级部门是否有负责人
        const rst = await this.ajax.postByJson(AjaxUrl.permcenter.getUppserOrgPosMap, {
            orgPosId
        });
        const upperOrg = rst.data;
        const secondLevelOrg = upperOrg[98] || {};
        const hasManager = (secondLevelOrg.orgPosUniteUsers || []).some((unitUser) => {
            return unitUser.type === 2;
        });
        if (!hasManager) {
            this.toastr.error(`您选择的组织的二级部门[${secondLevelOrg.orgPosName}]缺少负责人，请联系张希越`, 10000);
            return false;
        }
        return true;
    }
}
