import { Component, Input } from '@angular/core';
import { Ajax, SharkBaseModal,SharkModalParams, SharkToastrService } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';
import { Router } from '@angular/router';

@Component({
    selector: 'dealingTipModal',
    templateUrl: './dealingTipModal.component.html'
})
export class DealingTipModal extends SharkBaseModal {
    num: number;

    constructor(private ajax: Ajax,
        private params: SharkModalParams,
        private sharkToastrService: SharkToastrService,
        private router: Router,
        private toast: SharkToastrService) {
        super();
        this.num = params.data.num;
    }

    dismiss() {
        this.close();
    }

    toDealingList() {
        if (this.router.url === '/workflow/dealingList') {
            this.close();
            return;
        }
        this.router.navigate(['/workflow/dealingList']);
    }
}
