import { Service } from '@tiger/boot';
import { AppConfig } from '@tiger/core';

export const topologyName = 'nav_personnelchange_transfer';

/**
 * 当前流程的配置文件
 */
@Service
export default class Config {
    /**
     * 应用名称
     */
    appName: string = AppConfig.appName;
    /**
     * 流程拓扑ID
     */
    topologyName: string = topologyName;
    /**
     * 邮件链接审批的节点
     * 转出指导员审批以外
     */
    linkApproveNodeIds: string[] = ['30010402', '30010403', '30010404', '30010405', '30010408'];
    /**
     * 链接审批加密密码
     */
    linkApprovePassword: string = 'approveemailpassword';
}
