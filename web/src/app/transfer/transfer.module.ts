import { NgModule } from '@angular/core';
import { YXWrokFlowConfig } from '@shark/shark-yx-workflow';
import { BpmFlowNames } from '../config';
import { SharedModule } from '../shared/shared.module';
import { TransferApproveComponent } from './approve.component';
import { TransferDetailComponent } from './detail.component';
import { TransferEditComponent } from './edit.component';
import { TransferComponent } from './transfer.component';

@NgModule({
    imports: [SharedModule],
    declarations: [
        TransferComponent,
        TransferEditComponent,
        TransferDetailComponent,
        TransferApproveComponent
    ],
    entryComponents: [TransferComponent]
})
export class TransferModule {
    constructor() {
        YXWrokFlowConfig.addDetailComponent(BpmFlowNames.NAV_PERSONNELCHANGE_TRANSFER, {
            component: TransferComponent,
            excludeNodeIdList: ['30010410']
        });
    }
}
