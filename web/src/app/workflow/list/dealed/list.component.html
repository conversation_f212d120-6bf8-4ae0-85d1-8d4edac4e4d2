<div class="m-workflow-dealed layout-main">
    <!-- Search 搜索 -->
    <div class="section ">
        <div class="section-header section-header-bordered">
            <span class="section-header-title">已办工单</span>
        </div>
        <div class="section-block margin-b-0x">
            <form class="form form-inline">
                <div class="form-item-group">
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label col-8">工单类型：</label>
                        <div class="form-item-control col-16">
                            <shark-select [(ngModel)]="searchData.topologyIds" (ngModelChange)="changeWolkflowType($event)" [clearable]="true"
                             [placeholder]="'请选择'" [data]="workFlowTypeOptions.data" [size]="'full'" name="topologyId"></shark-select>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label col-8">工单名称：</label>
                        <div class="form-item-control col-16">
                            <shark-select [(ngModel)]="searchData.topologyId" [placeholder]="'请选择'" [clearable]="true"
                             [data]="flowTypeOptions" [size]="'full'" name="name"></shark-select>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label col-8">工单编号：</label>
                        <div class="form-item-control col-16">
                            <shark-input [(ngModel)]="searchData.flowId" [size]="'full'" [placeholder]="'请填写'"  name="flowId"></shark-input>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label col-8">创建人：</label>
                        <div class="form-item-control col-16">
                            <single-user-selector [(model)]="searchData.createUser" [size]="'full'" name="createUserEmail" ></single-user-selector>
                            <!-- <shark-input [(ngModel)]="searchData.createUser" [size]="'full'" name="createUserEmail"></shark-input> -->
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label col-8">选择时间：</label>
                        <div class="form-item-control col-16">
                            <shark-select [(ngModel)]="searchData.dateType" [data]="dateType.data" name="dateType"  [clearable]="true" [placeholder]="'请选择时间类型'"></shark-select>
                            <shark-select style="margin-left: 10px;" [(ngModel)]="searchData.createTime" [data]="timeFrameOptions.data" name="timeFrame" 
                            [ngStyle]="{'display': searchData.dateType === dateType.POINT ? 'inline-block' : 'none'}"></shark-select>
                            <shark-daterangepicker class="margin-l-1x" [(ngModel)]="searchData.createTime" [ngStyle]="{'display': searchData.dateType === dateType.RANGE ? 'inline-block' : 'none', 'width': '68%' }"
                                [format]="'yyyy-MM-dd HH:mm:ss'" [ngModelOptions]="{standalone: true}"></shark-daterangepicker>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label col-8">工单状态：</label>
                        <div class="form-item-control col-16">
                            <!-- TODO 这边的值明天确认 -->
                            <shark-select [(ngModel)]="searchData.status"  [clearable]="true" [data]="topologyStatusOptions" [size]="'full'" [placeholder]="'请选择'" name="status"></shark-select>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <div class="col-8"></div>
                        <div class="form-item-control col-16">
                            <button class="btn btn-primary" (click)="doSearch()">搜索</button>
                            <button class="btn btn-secondary" (click)="doReset()">重置</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 列表List -->
    <div class="section" *ngIf="itemList.length === 0">
     
        <div class="no-data-tip">暂无数据</div>
       
    </div>
    <div *ngIf="itemList.length > 0">
        <div *ngFor="let item of itemList;let index = index;">
            <!-- 权限类型 ius_special_rights -->
            <div class="section m-list-item position-relative" *ngIf="item.flowMetaData.topologyId === 'ius_special_rights' || item.flowMetaData.topologyId === 'ius_special_rights_child' || item.flowMetaData.topologyId === 'ius_org_identity_permit_topology' || item.flowMetaData.topologyId === 'ius_org_identity_permit_child_topology' || item.flowMetaData.topologyId === 'ius_ext_system_auth_apply' || item.flowMetaData.topologyId === 'ius_ext_system_auth_clear'">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)" *ngIf="!isMobile">
                    <div class="border-success text-success bg-success float-right padding-h-1x" style="display: inline-block;" >
                        权限
                    </div>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div class="position-relative m-left-info" [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">
                            <div class="border-success text-success bg-success padding-h-1x" style="display: inline-block;" *ngIf="isMobile">
                                权限
                            </div>
                            {{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}
                        </div>
                        <div class="m-line-height" [ngClass]="{'position-absolute': !isMobile}" [style.bottom]="isMobile ? '0' : '15px'">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span></div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                        <img *ngIf="item.flowNodeMessage.urgeState === 1 && !extraBussinessData[index]?.allowUrge && item.flowNodeMessage.nodeId !== '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/urged.png">
                        <img *ngIf="item.flowNodeMessage.nodeId === '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/overed.png">
                        <img *ngIf="item.flowNodeMessage.withdrawState === 1" class="u-urged-img" src="../../../../assets/img/cancel.png">
                    </div>
                    <div [ngClass]="{'col-15': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="m-line-height">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <div class="m-line-height" *ngIf="item.flowMetaData.topologyId != 'ius_ext_system_auth_clear'">
                            <span><span class="m-label-color">员工类型：</span>{{extraBussinessData[index]?.userType || '无'}}</span>
                            <span class="margin-l-4x"><span class="m-label-color">岗位：</span>{{extraBussinessData[index]?.workPost}}</span>
                        </div>
                        <!-- item.flowNodeMessage.businessObject.multiApplySystem -->
                        <div class="m-line-height">
                            <span class="m-label-color">部门：</span>
                            <span *ngIf="item.flowMetaData.topologyId != 'ius_ext_system_auth_clear'">{{extraBussinessData[index]?.department}}</span>
                            <span *ngIf="item.flowMetaData.topologyId == 'ius_ext_system_auth_clear'">{{item.flowNodeMessage.changeUserInfo.fullDeptName}}</span>
                        </div>
                        <div class="m-line-height text-truncate text-width" shark-tooltip [template]="item | muliapplypipe" [direction]="'bottom'">
                            <span class="m-label-color">权限需求：</span>{{ item | muliapplypipe }}
                        </div>
                        <div class="m-line-height text-error" *ngIf="item.flowNodeMessage?.forOtherMobileCheckRemark">{{item.flowNodeMessage?.forOtherMobileCheckRemark}}</div>
                    </div>
                    <div class="m-operating" [ngClass]="{'col-3': !isMobile, 'col-24': isMobile}">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <urge-btn [parent]='this' [data]='item' [sucCb]='getItemList' [disabled]='!extraBussinessData[index]?.allowUrge'></urge-btn>
                                <withdraw-btn [parent]='this' [data]='item' [sucCb]='getItemList' [isShow]='extraBussinessData[index]?.allowWithDraw && item.flowNodeMessage.withdrawState !== 1'></withdraw-btn>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

            <!-- 组织架构类型 -->
            <div class="section m-list-item position-relative"  *ngIf="item.flowMetaData.topologyId === 'ius_entry_apply_topology' || item.flowMetaData.topologyId === 'ius_org_identity_confirm_topology' || item.flowMetaData.topologyId === 'ius_hold_post_apply_topology' || item.flowMetaData.topologyId === 'ius_quit_apply_topology' || item.flowMetaData.topologyId === 'ius_transfer_apply_topology'" >
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)" *ngIf="!isMobile">
                    <div class="border-primary text-primary bg-primary float-right padding-h-1x" style="display: inline-block;" >
                        组织架构
                    </div>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div class="position-relative m-left-info"  [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}"  (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">
                            <div class="border-primary text-primary bg-primary padding-h-1x" style="display: inline-block;" *ngIf="isMobile">
                                组织架构
                            </div>
                            {{item | topoauthnamepipe}}-{{getUserName(item)}}
                        </div>
                        <div class="m-line-height" [ngClass]="{'position-absolute': !isMobile}" [style.bottom]="isMobile ? '0' : '15px'">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span> </div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                        <img *ngIf="item.flowNodeMessage.urgeState === 1 && !extraBussinessData[index]?.allowUrge && item.flowNodeMessage.nodeId !== '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/urged.png">
                        <img *ngIf="item.flowNodeMessage.nodeId === '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/overed.png">
                        <img *ngIf="item.flowNodeMessage.withdrawState === 1" class="u-urged-img" src="../../../../assets/img/cancel.png">
                    </div>
                    <div [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <!-- <div class="m-line-height">当前节点/审批人：部门负责人审批</div> -->
                        <div class="m-line-height">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>

                        
                        <div class="m-line-height">
                            <span><span class="m-label-color">员工类型：</span>{{extraBussinessData[index]?.userType || '无'}}</span>
                            <span class="margin-l-4x">
                                <span class="m-label-color">{{getWorkPostLabel(item.flowMetaData.topologyId)}}：</span>
                                {{getWorkPostValue(item.flowMetaData.topologyId, index)}}
                            </span>
                        </div>
                        <ng-container>
                            <div class="m-line-height">
                                <span class="m-label-color">{{getWorkDepartLabel(item.flowMetaData.topologyId)}}：</span>
                                {{getWorkDepartValue(item.flowMetaData.topologyId, index)}}
                            </div>
                            <div *ngIf="item.flowNodeMessage.businessObject?.remark; else notHaveRemark" class="m-line-height text-truncate text-width" shark-tooltip [template]="item.flowNodeMessage.businessObject.remark" [direction]="'bottom'">
                                <span class="m-label-color">{{formatRemarkLabel(item.flowMetaData.topologyId)}}：</span>
                                {{item.flowNodeMessage.businessObject?.remark}}
                            </div>
                            <ng-template #notHaveRemark>
                                <div class="m-line-height text-truncate text-width">
                                    <span class="m-label-color">{{formatRemarkLabel(item.flowMetaData.topologyId)}}：</span>
                                    {{item.flowNodeMessage.businessObject?.remark}}
                                </div>
                            </ng-template>
                        </ng-container>
                    </div>
                    <div class="m-operating" [ngClass]="{'col-3': !isMobile, 'col-24': isMobile}">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <urge-btn [parent]='this' [data]='item' [sucCb]='getItemList' [disabled]='!extraBussinessData[index]?.allowUrge'></urge-btn>
                                <withdraw-btn [parent]='this' [data]='item' [sucCb]='getItemList' [isShow]='extraBussinessData[index]?.allowWithDraw && item.flowNodeMessage.withdrawState !== 1'></withdraw-btn>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

             <!-- 处理过的 老工单 属于组织架构类型-->
             <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'nav_personnelchange_onboard' || item.flowMetaData.topologyId === 'nav_personnelchange_departure' || item.flowMetaData.topologyId === 'nav_personnelchange_transfer' || item.flowMetaData.topologyId === 'ius_personal_special_rights'">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)" *ngIf="!isMobile">
                    <div *ngIf="item.flowMetaData.topologyId === 'ius_personal_special_rights' else org1" class="border-success text-success bg-success float-right padding-h-1x" style="display: inline-block;" >
                        权限
                    </div>
                    <ng-template #org1>
                        <div #org1 class="border-primary text-primary bg-primary float-right padding-h-1x" style="display: inline-block;" >
                            组织架构
                        </div>
                    </ng-template>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div class="position-relative m-left-info"  [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="m-line-height">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：</span> {{item.flowMetaData.flowId}}</div>
                            <div><span class="m-label-color">更新时间：</span>{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</div>
                        </div>
                        <img *ngIf="item.flowMetaData.status === 200" class="u-urged-img" src="../../../../assets/img/overed.png">
                    </div>
                    <div [ngClass]="{'col-15': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="m-line-height" *ngIf="item.flowMetaData.topologyId === 'ius_personal_special_rights' else personal1">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.status === 200 ? '工单完结' :item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span *ngIf="item.flowMetaData.status !== 200">
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <ng-template #personal1>
                            <div class="m-line-height" #personal1>
                                <span *ngIf="item.flowNodeMessage?.businessStatus === 0"><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                                <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                                <span>
                                    <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                    <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                                </span>
                            </div>
                        </ng-template>
                        
                    </div>
                    <div class="m-operating" [ngClass]="{'col-3': !isMobile, 'col-24': isMobile}">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                                <div style="display:inline-block;">
                                    <button
                                        *ngIf="item.flowNodeMessage?.business_status === -1 || item.flowNodeMessage?.businessStatus === -1 else other1"  
                                        class="btn btn-link" type="button" (click)="goEditOrder(item)">
                                        编辑
                                    </button>
                                    <ng-template #other1>
                                        <span>/</span>
                                    </ng-template>
                                </div>
                               
                            </div>
                        </div>
                </div>
            </div>
            <!-- 文档类型 照抄之前-->
            <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'written_doc_delete_project' || item.flowMetaData.topologyId === 'written_doc_project' ">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)" *ngIf="!isMobile">
                    <div class="border-error text-error bg-error float-right padding-h-1x" style="display: inline-block;" >
                        文档
                    </div>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div class="position-relative m-left-info"  [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="m-line-height">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：</span> {{item.flowMetaData.flowId}}</div>
                            <div><span class="m-label-color">更新时间：</span>{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</div>
                        </div>
                        <img *ngIf="item.flowMetaData.status === 200" class="u-urged-img" src="../../../../assets/img/overed.png">
                        
                    </div>
                    <div [ngClass]="{'col-15': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;"> 
                        <div class="m-line-height" *ngIf="item.flowMetaData.status === 200 else end">
                            <span class="m-label-color">当前节点/审批人：</span>工单完结
                        </div>
                        <ng-template #end>
                            <div class="m-line-height">
                                <span *ngIf="item.flowNodeMessage?.businessStatus === 0"><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                                <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                                <span>
                                    <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                    <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                                </span>
                            </div>
                        </ng-template>
                    </div>
                    <div class="m-operating" [ngClass]="{'col-3': !isMobile, 'col-24': isMobile}">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <!-- 如果工单类型为成文文档下发 显示撤回或编辑 或处理-->
                                <div *ngIf="item.flowMetaData.topologyId === 'written_doc_project' else other" style="display:inline-block;">
                                    <button *ngIf="item.flowMetaData.currentNodeId === '70210102'" class="btn btn-link"
                                        type="button" (click)="revoke(item)">撤回</button>
                                    <button
                                        *ngIf="item.flowMetaData.currentNodeId === '70210101' &&
                                        item.flowNodeMessage?.content?.approved === undefined"
                                        class="btn btn-link" type="button" (click)="toEdit(item)">编辑</button>
                                        <!-- 如果当前节点为拟写成文文件、审批不通过、登录人是创建人 则显示处理按钮 -->
                                    <button class="btn btn-link" (click)="toEdit(item)"
                                        *ngIf="item.flowNodeMessage?.content?.approved === false
                                    && item.flowMetaData.currentNodeId === '70210101' &&
                                        email === item.flowNodeMessage?.createUser">处理</button>
                                </div>
                                <!-- 如果工单类型为文档作废工单 显示撤回或处理-->
                                <div *ngIf="item.flowMetaData.topologyId === 'written_doc_delete_project' else other"
                                    style="display:inline-block;">
                                    <button
                                        *ngIf="item.flowMetaData.currentNodeId === '70210302' && 
                                        item.flowNodeMessage?.content.dealStatus === undefined "
                                        class="btn btn-link"
                                        type="button" (click)="revokeDel(item)">撤回</button>
                                    <button *ngIf="item.flowMetaData.currentNodeId === '70210301' &&
                                        item.flowNodeMessage?.content?.approved === false" class="btn btn-link"
                                        type="button" (click)="toDeal(item)">处理</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="section-block text-right" *ngIf="pagination.total > 0">
        <shark-pagination [page]="pagination.page" [totalPage]="pagination.totalPage" [pageSize]="pagination.size"
            (onSizeChanged)="onSizeChanged($event);" (onPageChanged)="onPageChanged($event);"></shark-pagination>
    </div>
</div>