import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkActionToastrService, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { UserSelectorService } from '@yx-module/umc-selector';
import { AjaxUrl } from '../config';
import { UserService } from '../shared/services/user.service';
import { CmdbService } from '../shared/services/cmdb.serverice';

@Component({
    templateUrl: './edit.component.html',
    selector: 'flow-departureedit'
})
export class DepartureEditComponent {
    @ViewChild('form') form;
    @Input() workFlowData: WorkFlowData;
    @Input()
    userInfo: any = null;
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {
            originOrgPos: {}
        }
    };
    showData: any = {
        appler: '',
        manager: '',
        orgPosTreeList: []
    };
    uiModel: any = {
        switch: true
    };
    originOrgPos: any = {}; // 申请人原始所在组织架构信息
    originOrgPosList: any[] = []; // 原始组织架构列表
    userList: any[] = [];
    pedding: boolean = false;
    isWorkFlowDataReady: boolean = false;
    isUserInfoReady: boolean = false;
    filterApplerData: (v) => any;
    applerList: any[] = [];
    applerOrgPosList: any[] = [];
    isInstructor: boolean = false;
    creatorOrgPosInfo: any[] = [];
    partOfOrgPosInfo: any[] = [];
    instructorOrgPosId: any = null;
    secondManager: boolean = false;
    orgArr: any[] = [];
    // cmdb 角色校验文案
    cmdbRoleText: string = ''
    constructor(
        private userSelectorService: UserSelectorService,
        private atoastr: SharkActionToastrService,
        private toastr: SharkToastrService,
        private router: Router,
        private ajax: Ajax,
        private userServ: UserService,
        private cmdbService: CmdbService
    ) {

    }

    async ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.isWorkFlowDataReady = true;
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
            this.userList = this.flowData.flowNodeMessage.trackList || [];
        }
        if (schanges.userInfo && schanges.userInfo.currentValue) {
            if (this.userInfo.uid) {
                // 获取用户组织架构信息
                try {
                    this.partOfOrgPosInfo = await this.getApplerOrgPosInfo(this.userInfo.uid);
                } catch (e) {
                    this.atoastr.error('当前用户不在组织架构内');
                    this.pedding = true;
                }

                this.cmdbRoleCheck()
                this.creatorOrgPosInfo = this.userInfo.userCenterOrgPosVOS;
                this.sortoutCompleteOrgData(this.partOfOrgPosInfo, this.creatorOrgPosInfo); // 组织出一份完整的创建人组织架构数据
                for (const item of this.partOfOrgPosInfo) {
                    if (item.type === 1) {
                        this.isInstructor = true;
                        this.instructorOrgPosId = item.orgPosId;
                    }
                }

                this.isUserInfoReady = true;
            }
        }
        if (this.isUserInfoReady && this.isWorkFlowDataReady) {
            if (this.flowData.flowNodeMessage.applerUid) {    // 如果是从列表页进入编辑页面
                this.showData.rewriteAppler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
                this.showData.appler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
                const originOrgPos = this.flowData.flowNodeMessage.originOrgPos;
                for (const orgPosId in originOrgPos) {
                    if (originOrgPos.hasOwnProperty(orgPosId)) {
                        this.showData.orgPosTreeList.push(originOrgPos[orgPosId].name);
                    }
                }
            } else {
                await this.initData();
            }
            if (this.isInstructor === true) {
                this.initAppler();
            }
        }
    }

    async initAppler() {
        await this.getApplerList(this.userInfo.uid);
        this.filterApplerData = (v) => {
            const list = [];
            for (const appler of this.applerList) {
                if ((appler.userName + appler.uid + appler.orgPosName).indexOf(v) > -1) {
                    list.push({
                        name: `${appler.userName}(${appler.uid}) ${appler.orgPosName}`,
                        userUid: appler.uid,
                        userName: appler.userName,
                        level: appler.level,
                        orgPosId: appler.orgPosId,
                        orgPosName: appler.orgPosName
                    });
                }
            }
            return list;
        };
    }

    // 获得创建人（指导员）所在部门的所有人员
    async getApplerList(instructorUid) {
        try {
            for (const orgPosId of this.applerOrgPosList) {
                const memberList = (await this.ajax.get(AjaxUrl.departure.getApplerList, {
                    orgPosId: orgPosId.orgPosId,
                    level: -1,
                    type: -1,
                    queryType: 0
                })).data;
                for (const member of memberList) {
                    if (!this.applerList.find((v) => v.uid == member.uid)) this.applerList.push(member);
                }
            }
        } catch (e) {
            this.atoastr.error('申请人列表获取失败');
        }
    }

    // 初始化数据
    async initData() {
        this.flowData.flowNodeMessage.applerUid = this.userInfo.uid;
        this.flowData.flowNodeMessage.applerName = this.userInfo.userName;
        this.showData.rewriteAppler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
        this.showData.appler = this.flowData.flowNodeMessage.applerName + '(' + this.flowData.flowNodeMessage.applerUid + ')';
        // 整理页面展示的组织架构树,并整理传到后台的组织架构数据
        this.getPosTree(this.creatorOrgPosInfo);
        // 查询申请人原始组织架构信息
        await this.getOriginOrgPos();
        this.setOriginOrgPosList();
    }

    // 整理一份完整的组织架构数据
    sortoutCompleteOrgData(partOfOrgPosInfo, userInfo) {
        for (const orgPosInfo of partOfOrgPosInfo) {
            for (const orgPosVos of userInfo) {
                for (const orgPosItem of orgPosVos.userCenterOrgPosItemVOList) {
                    if (orgPosInfo.orgPosId === orgPosItem.orgPosId) {
                        orgPosItem.type = orgPosInfo.type;
                    }
                }
            }
        }
    }

    setOriginOrgPosList() {
        const originOrgPosList = [];
        for (const orgPosId in this.originOrgPos) {
            if (this.originOrgPos.hasOwnProperty(orgPosId)) {
                originOrgPosList.push(this.originOrgPos[orgPosId].name);
            }
        }
        this.originOrgPosList = originOrgPosList;
    }

    // 获取申请人原始组织架构信息
    async getOriginOrgPos() {
        try {
            this.originOrgPos = (await this.ajax.postByJson(AjaxUrl.permcenter.getOriginOrgPosByUid, {
                uid: this.userInfo.uid
            })).data;
            return this.originOrgPos;
        } catch (e) {
            this.toastr.error(`查询用户原始组织架构信息失败: ${e.errorCode}`);
        }
    }
    // 整理页面展示的组织架构树,并整理传到后台的组织架构数据
    async getPosTree(creatorOrgPosInfo) {
        let orgStr;
        this.showData.orgPosTreeList = [];
        this.flowData.flowNodeMessage.originOrgPos = {};
        for (const item of creatorOrgPosInfo) {
            let posTree = '';
            for (let i = 0; i < item.userCenterOrgPosItemVOList.length; i++) {
                posTree = posTree.concat(item.userCenterOrgPosItemVOList[i].orgPosName);

                if (item.userCenterOrgPosItemVOList[i].type !== undefined && item.userCenterOrgPosItemVOList[i].type != null) {
                    if (item.userCenterOrgPosItemVOList[i].type === 0) {
                        posTree = posTree.concat('(普通员工)');
                    } else if (item.userCenterOrgPosItemVOList[i].type === 1) {
                        posTree = posTree.concat('(指导员)');
                    } else if (item.userCenterOrgPosItemVOList[i].type === 2) {
                        posTree = posTree.concat('(负责人)');
                    }
                }

                if (i < item.userCenterOrgPosItemVOList.length - 1) {
                    posTree = posTree.concat('>');
                }

                if (i === item.userCenterOrgPosItemVOList.length - 1) {
                    this.flowData.flowNodeMessage.level = item.userCenterOrgPosItemVOList[i].level;
                    this.flowData.flowNodeMessage.orgPosId = item.userCenterOrgPosItemVOList[i].orgPosId;
                    this.flowData.flowNodeMessage.orgPosName = item.userCenterOrgPosItemVOList[i].orgPosName;
                    const orgPosId = this.flowData.flowNodeMessage.orgPosId;
                    this.flowData.flowNodeMessage.originOrgPos[orgPosId] = {};
                    this.flowData.flowNodeMessage.originOrgPos[orgPosId].name = posTree;
                    orgStr = await this.judgeSecondOrgManager(orgPosId);
                }
            }
            this.showData.orgPosTreeList.push(posTree);
        }
        if (!this.secondManager) {
            this.atoastr.error('二级部门：' + orgStr + '缺少负责人，请联系张希越');
            this.pedding = true;
        }
    }

    async judgeSecondOrgManager(orgPosId) {
        let highestOrg;
        let orgStr = '';
        const uppser = (await this.ajax.get(AjaxUrl.permcenter.getUppserOrgPos, {
            orgPosId
        })).data;
        for (const item of uppser) {
            if (item.level === 99) {
                highestOrg = item.orgPosName;
            }
            if (item.level === 98) {
                for (const user of item.orgPosUniteUsers) {
                    if (user.type === 2) {
                        this.secondManager = true;
                    }
                }
                if (!this.secondManager) {
                    if (this.orgArr.indexOf(item.orgPosName) === -1) {
                        this.orgArr.push(item.orgPosName);
                    }
                }
            }
        }
        if (this.orgArr.length > 0) {
            for (const org of this.orgArr) {
                orgStr = orgStr + highestOrg + '>' + org + '，';
            }
        }
        return orgStr;
    }

    // 获取申请人的OrgPos信息
    async getApplerOrgPosInfo(applerUid) {
        const applerOrgPosInfo = await this.ajax.get(AjaxUrl.departure.getUserOrgPosInfo, {
            uid: applerUid
        });
        this.applerOrgPosList = applerOrgPosInfo.data;
        return applerOrgPosInfo.data;
    }

    // 开始选择人员
    selectUsers() {
        this.userSelectorService.selectUsers({ list: this.userList }, (res) => {
            this.userList = res.data;
        });
    }

    save() {
        this.form.submit(async () => {
            this.orgSubmitData();
            try {
                this.pedding = true;
                await this.ajax.postByJson(AjaxUrl.submitFlow.departure, {
                    ...this.flowData
                });
                this.pedding = false;
                this.toastr.success('提交离职申请工单成功!');
                this.router.navigate(['/workflow/createdList']);
            } catch (error) {
                this.atoastr.error('提交离职申请工单失败!');
            }
        });
    }

    orgSubmitData() {
        this.flowData.flowNodeMessage.trackList = this.userList.map((user) => {
            return user.uid;
        });
        this.flowData.flowNodeMessage.trackFullList = this.userList;
    }

    // 更改申请人之后要做的变更
    async dataNotify(option) {
        this.secondManager = false;
        this.orgArr = [];
        // 初始化flowData中的appler
        this.flowData.flowNodeMessage.applerUid = option.data.item.userUid;
        this.flowData.flowNodeMessage.applerName = option.data.item.userName;
        let applerOrgPosInfo;
        await this.userServ.getUserInfoByUid(this.flowData.flowNodeMessage.applerUid).then((userInfo) => {
            applerOrgPosInfo = userInfo.data.userCenterOrgPosVOS;
        });
        const partOfOrgPosInfo = await this.getApplerOrgPosInfo(this.flowData.flowNodeMessage.applerUid);
        // 整理一份完整的组织架构数据
        this.sortoutCompleteOrgData(partOfOrgPosInfo, applerOrgPosInfo);
        // 获得组织架构树，并整理传到后台的组织架构数据
        this.getPosTree(applerOrgPosInfo);
    }

    // 检查 CMDB 角色 --yangbo
    async  cmdbRoleCheck() {
        try {
            this.cmdbRoleText = await this.cmdbService.checkCmdbRoles(this.userInfo.uid);
        } catch (error) {
            console.log('cmdb角色获取出错')
        }
    }

}


