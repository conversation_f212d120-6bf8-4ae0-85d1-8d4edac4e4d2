import {Service} from '@tiger/boot';
import {AjaxResult, AppConfig, getServiceUrl} from '@tiger/core';
import {isObjectExisted} from '../../common/util';
import {post} from 'request-promise';
import fs from 'fs'
import request from "@tiger/request";

const getUrl = (url: any) => {
    return `${getServiceUrl(FmsDomains)}${url}`;
};

const FmsDomains = {
    dev: 'http://fms.you.163.com',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-fms.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-fms.service.mailsaas'
};

@Service
export default class FmsService {

    /**
     * @description 上传单个文件
     * @param {IFileUploadOpt} param
     * @returns {Promise<AjaxResult<IFileInfo>>}
     * @memberof FmsService
     */
    async uploadFile(param: any) {
        if (!isObjectExisted(param.file) && !isObjectExisted(param.topic)) {
            throw Error('参数不全');
        }
        const {file, topic} = param;
        const name = file.name;
        const productCode = AppConfig.get<string>('productCode');
        try {
            const rst = await post({
                uri: getUrl(`/xhr/nos/upload/${productCode}/${topic}`),
                formData: {
                    file: {
                        value: fs.createReadStream(file.path),
                        options: {
                            filename: name,
                            contentType: file.type,
                            fileSize: file.size
                        }
                    },
                    fileName: name
                },
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            const rstObj = JSON.parse(rst);
            if (rstObj.code === 200) {
                const data = rstObj.data.map((v: any) => {
                    if (new RegExp('http://fms.you.163.com').test(v)) {
                        return v.replace(`http://fms.you.163.com/xhr/nos/download/${productCode}/${topic}/`, '');
                    } else {
                        return v.replace(`http://yxfms.hz.infra.mail/xhr/nos/download/${productCode}/${topic}/`, '');
                    }
                });
                return AjaxResult.success(data);
            } else {
                console.error(`uploadFile failed. 项目: ${productCode} 主题: ${topic}, 文件名：${name}  response: ${JSON.stringify(rstObj)}`);
                return rstObj;
            }
        } catch (error) {
            console.error(`uploadFile failed. 项目: ${productCode} 主题: ${topic}, 文件名：${name}  response: ${JSON.stringify(error)}`);
            return error;
        }
    }

    /**
     * @description 下载文件
     * @param {IFilesDownloadOpt} param
     * @returns {Promise<Stream>}
     * @memberof FmsService
     */
    async downLoadFile(param: any) {
        if (!isObjectExisted(param.fileKey) && !isObjectExisted(param.topic)) {
            throw Error('参数不全');
        }
        const {fileKey, topic} = param;
        const productCode = AppConfig.get<string>('productCode');
        const rst = await request.get(getUrl(`/xhr/nos/download/${productCode}/${topic}/${fileKey}`), {responseType: 'stream'});
        return rst.data;
    }


}
