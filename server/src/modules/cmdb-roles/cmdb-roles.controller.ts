import { GetMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, QueryContext } from '@tiger/core';
import { contextPath, xhrPrefix } from '../../conf';
import { Cmdbservice } from './cmdb-roles.service';
import { RoleResultVo } from './vo/roles-result.vo';
import { CmdbRolesPo } from './vo/cmdb-roles.po';

/**
 * 用户信息，给前台的接口
 */
@RestController
@RequestMapping(`${contextPath}${xhrPrefix}/cmdb`)
export class CmdbRolesController {
    constructor(
        private cmdbservice: Cmdbservice
    ) { }

    // 获取 用户在 cmdb 中承担的角色
    @GetMapping('/getCmdbRoles.json')
    async getCmdbRoles(ctx: QueryContext<CmdbRolesPo, AjaxResult<RoleResultVo>>) {
        if (!ctx.query.uid) {
            return ctx.body = AjaxResult.badRequest('缺少参数 uid')
        }
        try {
            const data = await this.cmdbservice.getRoles(ctx.query.uid)
            ctx.body = AjaxResult.success(data)
        } catch (error) {
            ctx.body = AjaxResult.badRequest('获取用户CMDB角色出错')
        }
    }


}
