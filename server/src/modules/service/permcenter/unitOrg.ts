// const querystring = require('querystring');
import axios from '@tiger/request';
import querystring from 'querystring';
import { iusDomain, productCode } from '../../../conf';
interface UserExtInfo {
    orgPosId: number; // 组织职位Id，若传-1测查询所有用户
    level: number; // 组织职位级别，-1查全部
    type: number; // 用户type，-1查全部，0-普通职员，1-指导员，2-负责人
    queryType: number; // 查询类型 0-按照邮箱查询，1-按照真实姓名
    keyword?: string; // 查询关键字
}

export async function getAllUpperByOrgUserId(userId: string): Promise<any> {
    const rst = await getOrgPosIdByUid(userId);
    if (rst.code !== 200) {
        throw new Error(`查询用户组织架构失败: ${rst.errorCode}`);
    }
    const orgIdList = rst.data;
    if (!orgIdList || orgIdList.length === 0) {
        return {};
    }
    const promises = [];
    for (const orgPosId of orgIdList) {
        promises.push(getUpperOrgListByOrgPosId(orgPosId));
    }
    const allPromise = Promise.all(promises);
    const upperOrgPosList = await allPromise;
    const upperOrgPosMap: any = {};
    for (let i = 0, len = upperOrgPosList.length; i < len; i++) {
        upperOrgPosMap[orgIdList[i]] = upperOrgPosList[i];
    }
    return upperOrgPosMap;
}

export async function getUpperByOrgUserId(userId: string): Promise<any> {
    const rst = await getOrgPosIdByUid(userId);
    if (rst.code !== 200) {
        throw new Error(`查询用户组织架构失败: ${rst.errorCode}`);
    }
    const orgIdList = rst.data;
    if (!orgIdList || orgIdList.length === 0) {
        return [];
    }
    // 默认取第一个
    const orgPosId = orgIdList[0];
    return await getUpperByOrgPosId(orgPosId);
}

export async function getUpperByOrgPosId(orgPosId: number): Promise<any> {
    const upperList = await getUpperOrgListByOrgPosId(orgPosId);
    // 转换成按level组织的map形式
    const upperMap: any = {};
    upperList.forEach((upper: any) => {
        upperMap[upper.level] = upper;
    });
    return upperMap;
}

export async function getUpperOrgListByOrgPosId(orgPosId: number): Promise<any> {
    const queryStr = querystring.stringify({
        orgPosId
    });
    const rst = await axios.get(`${iusDomain}/${productCode}/user/uniteOrg/allTeam/listUpperByOrgPosId.json?${queryStr}`);
    if (rst.data.code !== 200) {
        throw new Error(`查询上级组织架构失败:${rst.data.errorCode}`);
    }
    const upperList = rst.data.data;
    return upperList;
}

export async function getOrgPosIdByUid(userId: string, type?: any) {
    const param: any = {
        uid: userId
    };
    if (type !== undefined && type !== null) {
        param.type = type;
    }
    const queryStr = querystring.stringify(param);
    const rst = await axios.get(`${iusDomain}/${productCode}/user/uniteOrg/allTeam/listIcacOrgPosIdByUidAndType.json?${queryStr}`);
    return rst.data;
}

export async function deleteIcacOrgPosUser(params: any) {
    const queryStr = querystring.stringify(params);
    return await axios.get(`${iusDomain}/${productCode}/user/uniteOrg/allTeam/deleteIcacOrgPosUserByUidAndType.json?${queryStr}`);
}

export async function getUserEmployeeNum(uid: string) {
    const rst = await axios.get(`${iusDomain}/${productCode}/employee/getEmployeeByUid.json?uid=${uid}`);
    return rst.data;
}

export async function lockUser(params: any) {
    // 后端遗留问题，历史接口会同时删除组织架构数据。现在新增leaveOrg参数，判断是否删除组织架构
    params.leaveOrg = false;
    const queryStr = querystring.stringify(params);
    return await axios.get(`${iusDomain}/${productCode}/user/leaveByUid.json?${queryStr}`);
}

export async function disassociateUser(params: any) {
    const queryStr = querystring.stringify(params);
    return await axios.get(`${iusDomain}/${productCode}/user/disassociateByUid.json?${queryStr}`);
}

export async function getUserExtInfo(params: UserExtInfo) {
    const queryStr = querystring.stringify(params as any);

    const rst = await axios.get(`${iusDomain}/${productCode}/user/uniteOrg/allTeam/fuzzyPartSubUserByOrgPosId.json?${queryStr}`);

    return rst.data;
}
