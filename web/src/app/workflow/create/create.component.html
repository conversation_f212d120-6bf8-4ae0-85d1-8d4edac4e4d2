<div class="section">
    <div class="section-header section-header-bordered">
        <span class="section-header-title">新建工单</span>
    </div>
    <div class="section-block section-entry">

        <div class="m-authority">
            <div class="m-auth-title">权限类</div>
            <div *ngIf="canCreateSpecial">
                <button [ngClass]="{'btn': true, 'btn-secondary': true }" (click)="applySpecial();" [disabled]="!canCreateSpecial">
                    <span [ngClass]="{'text-gray-light': !canCreateSpecial, 'u-activeBtn': canCreateSpecial}">个人特殊权限申请</span>
                </button>
                <img class="m-hot-img" src="../../../assets/img/hot.png" alt="">
            </div>
            <div *ngIf="!canCreateSpecial" shark-tooltip [template]="'系统检测到您还未加入严选组织架构，暂时无法申请个人特殊权限，请联系权限中心管理员！'" [direction]="'top'">
                <button class="btn btn-secondary" (click)="applySpecial();" *ngIf="!canCreateSpecial" disabled>
                    <span class="text-gray-light">个人特殊权限申请</span>
                </button>
                <img class="m-hot-img" src="../../../assets/img/hot.png" alt="">
            </div>
           <div>
                <button class="btn btn-secondary" (click)="applyOrgId();" [disabled]="!canCreateOrgID">
                    <span [ngClass]="{'text-gray-light': !canCreateOrgID, 'u-activeBtn': canCreateOrgID }">组织身份权限申请</span>
                </button>
                <img class="m-hot-img" src="../../../assets/img/hot.png" alt="">
           </div>
           <div *ngIf="canCreateSpecial">
                <button [ngClass]="{'btn': true, 'btn-secondary': true }" (click)="applyExternal();">
                    <span [ngClass]="{'text-gray-light': !canCreateSpecial, 'u-activeBtn': canCreateSpecial}">外部系统权限申请</span>
                </button>
            </div>
            <div *ngIf="!canCreateSpecial" shark-tooltip [template]="'系统检测到您还未加入严选组织架构，暂时无法申请个人特殊权限，请联系权限中心管理员！'" [direction]="'top'">
                <button class="btn btn-secondary" (click)="applyExternal();" *ngIf="!canCreateSpecial" disabled>
                    <span class="text-gray-light">外部系统权限申请</span>
                </button>
            </div>
        </div>

        <div class="m-organization">
            <div class="m-org-title">业务组织架构类</div>
            <!-- <div *ngIf="!isInOrgUnit" >
                <button class="btn btn-secondary" (click)="applyEntry();">
                    <span class="u-activeBtn">加入严选组织架构 (供外包同学申请)</span>
                </button>
                <img class="m-hot-img" src="../../../assets/img/hot.png" alt="">
            </div>
            <div *ngIf="isInOrgUnit"  shark-tooltip [template]="'您当前已在组织架构中'" [direction]="'top'">
                <button class="btn btn-secondary" disabled>
                    <span class="text-gray-light">加入严选组织架构 (供外包同学申请)</span>
                </button>
                <img class="m-hot-img" src="../../../assets/img/hot.png" alt="">
            </div> -->
            <!-- <div>
                <button class="btn btn-secondary"  *ngIf="isInOrgUnit && isInYanXuan" (click)="applyHoldPost();">
                    <span class="u-activeBtn">兼岗申请</span>
                </button>
            </div>
            <div *ngIf="!isInOrgUnit" shark-tooltip [template]="'您当前不在组织架构内，请先申请加入'" [direction]="'top'">
                <button class="btn btn-secondary"  disabled>
                    <span class="text-gray-light">兼岗申请</span>
                </button>
            </div>
            <div *ngIf="isInOrgUnit && !isInYanXuan" shark-tooltip [template]="'您当前不属于严选事业部，无法申请兼岗工单'" [direction]="'top'">
                <button  class="btn btn-secondary" (click)="applyHoldPost();"  disabled>
                    <span class="text-gray-light">兼岗申请</span>
                </button>
            </div> -->
            <div *ngIf="isInOrgUnit">
                <button class="btn btn-secondary" (click)="applyQuit();">
                    <span class="u-activeBtn">离职申请 (供外包同学申请)</span>
                </button>
            </div>
            <div *ngIf="!isInOrgUnit" shark-tooltip [template]="'您当前不在组织架构内，请先申请加入'" [direction]="'top'">
                <button  class="btn btn-secondary" (click)="applyQuit();"  disabled>
                    <span class="text-gray-light">离职申请 (供外包同学申请)</span>
                </button>
            </div>
        </div>
    </div>
</div>
<div class="section" style="display: none">
    <div class="section-header section-header-bordered">
        <span class="section-header-title">严选系统权限申请工单</span>
    </div>
    <div class="section-block section-entry">
        <button class="btn btn-primary" *ngIf="isInOrgUnit" (click)="applyPerm();">申请业务系统权限</button>
        <div *ngIf="!isInOrgUnit" style="display: inline-block;" shark-tooltip [template]="'您当前不在组织架构内'">
            <button class="btn btn-primary" shark-tooltip [template]="'您当前不在组织架构内，请先申请加入'"
                disabled>申请业务系统权限</button>
        </div>
    </div>
</div>
