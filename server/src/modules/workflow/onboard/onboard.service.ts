import axios from '@tiger/request';
import { iusDomain, productCode } from '../../../conf';
import { IUSUserEntryDTO } from './DTO/ IUS_UserEntryDTO.dto';

export async function addOrgPosUserByType(param: any) {
    return await axios.post(`${iusDomain}/${productCode}/user/entry.json`, param);
}

// 入职预校验接口，用于门户入职工单，提交之前调用
export async function checkEntry(param: IUSUserEntryDTO) {
    return await axios.post(`${iusDomain}/${productCode}/user/inner/checkEntry.json`, param);
}
