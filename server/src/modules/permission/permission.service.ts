import {AjaxResult, AppConfig, getServiceUrl} from '@tiger/core';
import request from "@tiger/request";
import {Service} from '@tiger/boot';
import {IPerm, IRole, IUser, IUserPerms} from '@tiger/permission';

const permsDomains = {
    ttl: 60000,
    dev: 'http://yxius.you.163.com',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-ius.service.mailsaas'
};
const productCode = AppConfig.get<string>('productCode');
const getUrl = (url: any) => {
    return `${getServiceUrl(permsDomains)}/${productCode}${url}`;
};

@Service
export default class PermissionService {
    /**
     * @description 根据uid查询 用户信息
     * @param {string} uid
     * @returns {(Promise<IUser | null>)}
     * @memberof UserPerms
     */
    async getUser(uid: string): Promise<IUser | null> {
        const getByUidUrl = getUrl('/user/getByUid.json');
        const result = await request.get(getByUidUrl, {params: {uid}});
        const resultSet = result.data;
        if (resultSet.isSuccess()) {
            return resultSet.data;
        }
        // 状态异常，不缓存
        console.error(`getUser(uid) failed. params: ${uid} , response: ${JSON.stringify(resultSet)}`);
        return null;
    }


    /**
     * @description 根据用户id查询 用户角色列表
     * @param {number} userId 用户id
     * @returns {Promise<IRole[]>} 用户角色列表
     * @memberof PermissionService 权限服务
     */
    async getRoles(userId: number): Promise<IRole[]> {
        const listRoleByUserIdUrl = getUrl('/user/listRoleByUserId.json');
        const result = await request.get(listRoleByUserIdUrl, {params: {userId}});
        const resultSet = result.data;
        if (resultSet.isSuccess()) {
            return resultSet.data;
        }
        console.error(`getRoles(userId) failed. params: ${userId} , response: ${JSON.stringify(resultSet)}`);
        return [];
    }

    /**
     * @description 根据userId查询 用户权限
     * @param {number} userId 用户id
     * @returns {Promise<IPerm[]>} 用户角色列表
     * @memberof PermissionService
     */
    async getPerms(userId: any): Promise<IPerm[]> {
        const listPermByUserIdUrl = getUrl('/user/listPermByUserId.json');
        const result = await request.get(listPermByUserIdUrl, {params: {userId}});
        const resultSet = result.data;
        if (resultSet.isSuccess()) {
            return resultSet.data;
        }
        console.error(`getPerms(userId) failed. params: ${userId} , response: ${JSON.stringify(resultSet)}`);
        // 状态异常，不缓存
        return [];
    }

    /**
     * @description 根据uid查询用户权限列表
     * @param {string} uid
     * @returns {(Promise<IUserPerms | null>)}
     * @memberof PermissionService 权限服务
     */
    async getUserPerms(uid: string): Promise<IUserPerms | null> {
        const user = await this.getUser(uid);
        if (user === null) {
            return null;
        }
        const userId = user.userId;
        const [perms, roles] = await Promise.all([this.getPerms(userId), this.getRoles(userId)]);
        const interfaceUrls: any[] = [];
        const permExps: any[] = [];
        // 换行分隔符
        const lineReg = /\r\n|\r|\n/;
        perms.forEach((perm: any) => {
            if (perm.interfaceUrls && perm.interfaceUrls.length > 0) {
                const urls = perm.interfaceUrls;
                urls.forEach((url: any) => {
                    let permUrl = url.trim();
                    permUrl = permUrl === '*' ? '(.*)' : permUrl;
                    // 去重
                    if (permUrl.length > 0 && !interfaceUrls.includes(permUrl)) {
                        interfaceUrls.push(permUrl);
                    }
                });
            }
            if (perm.permExp && perm.permExp.length > 0) {
                const exps = perm.permExp.split(lineReg);
                exps.forEach((exp: any) => {
                    if (exp.trim().length > 0) {
                        permExps.push(exp.trim());
                    }
                });
            }
        });
        return {user, perms, roles, permExps, interfaceUrls};
    }

};