.root {
    display: flex;
    min-width:980px;
    padding: 0 10px;
}

.m-index {
    flex: 1;
}

.m-fav-description {
    >img {
        width: 250px;
    }
}
.u-collapse {
    .collapse-block {
        transition: all 0.5s linear;
        // overflow: hidden;
    }
    &.collapsed {
        .collapse-block {
            height: 0 !important;
            display: none;
        }
    }
    .padding-top {
        position: relative;
        height: 52px;
        cursor: pointer;
    }
    .icon {
        line-height: 36px;
        margin-top: 20px;
        text-align: left;
        border-bottom: 1px solid #d9d9d9;
        overflow: hidden;
        margin-bottom: 0;
        >a {
            text-decoration: none;
            font-size: 28px;
            // line-height: 42px;
            color: #333333;
        }
        >span {
            float: right;
            color: #666666;
            font-size:14px;
        }
    }
}

@media (max-width: 980px) {
    .root {
        padding: 0;
        min-width: auto;
    }
    .m-index {
        width: 100%;
    }
}