import { TgModule } from '@tiger/boot';

import { PermChildWorkflowLinkAuditController } from './controller/perm-workflow-link-audit.controller';
import { PermChildWorkflowNotifyController } from './controller/perm-workflow-notify.controller';
import { PermChildWorkflowController } from './controller/perm-workflow.controller';

@TgModule({
    imports: [],
    controllers: [PermChildWorkflowLinkAuditController, PermChildWorkflowNotifyController, PermChildWorkflowController]
})
export class PermChildWorkflowModule { }
