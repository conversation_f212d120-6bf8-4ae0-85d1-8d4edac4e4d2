import { TgModule } from '@tiger/boot';

import { DepartureWorkflowModule } from './departure/departure-workflow.module';
import { OnboardWorkflowModule } from './onboard/onboard-workflow.module';
import { PermWorkflowModule } from './perm/perm-workflow.module';
import { PermChildWorkflowModule } from './permChild/perm-workflow.module';
import { TransferWorkflowModule } from './transfer/transfer-workflow.module';

import WorkflowQueryController from './workflow-query.controller';
import { WrittendocWorkflowModule } from './writtendoc/writtendoc-workflow.module';
import { WrittendocDeleteWorkflowModule } from './writtendocdelete/writtendoc-delete-workflow.module';

@TgModule({
    imports: [DepartureWorkflowModule, OnboardWorkflowModule, PermWorkflowModule, PermChildWorkflowModule, TransferWorkflowModule
        , WrittendocWorkflowModule, WrittendocDeleteWorkflowModule],
    controllers: [WorkflowQueryController]
})
export class WorkflowModule {
}
