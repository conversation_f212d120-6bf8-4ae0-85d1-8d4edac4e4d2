import { Component, Input } from '@angular/core';
import { SharkModalService, SharkToastrService, Ajax } from '@shark/shark-angularX';
import { AjaxUrl } from '../../../config';

@Component({
  selector: 'withdraw-btn',
  templateUrl: './withdrawBtn.component.html'
})

export class WithdrawBtn {
  @Input() data: any = {}; // 工单数据
  @Input() isShow: boolean = false; // 是否展示
  @Input() sucCb: any; // 成功回调
  @Input() parent: any; // 父组件实例

  constructor(
    private sharkModalService: SharkModalService,
    private sharkToastrService: SharkToastrService,
    private ajax: Ajax
  ) {
  }

  // 撤回操作
  onWithdraw() {
    this.sharkModalService.confirm({
      title: '工单还未审批，确定要撤回吗？',
      okText: '确定',
      cancelText: '取消'
    }).then(() => {
      const { flowMetaData, flowNodeMessage } = this.data;
      const {
        nodeId,
        flowId
      } = flowNodeMessage || { flowId: '', nodeId: '' };
      this.ajax.get(AjaxUrl.createFlow.withdraw, { flowId, nodeId }).then((res) => {
        this.parent.getItemList();
        this.sharkToastrService.success('操作成功')
      }, () => {
        this.sharkToastrService.error('操作失败')
      })
    });
  }
}