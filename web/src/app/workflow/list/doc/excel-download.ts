import { Injectable } from '@angular/core';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';

const EXCEL_TYPE = 'application/octet-stream';
const EXCEL_EXTENSION = '.xlsx';
@Injectable()
export class ExcelDownload {
    // 表头数组
    headList: any= [
        {
            序号: '序号',
            文档编号: '文档编号',
            文档名称: '文档名称',
            文档密级: '文档密级',
            文档类型: '文档类型',
            所属部门: '所属部门',
            拟制人: '拟制人',
            审核人: '审核人',
            工单状态: '工单状态',
            当前节点: '当前节点',
            拟制日期: '拟制日期'         
        },
    ];
    // 表头merge数组
    // Headmerges: any = [{
    //     's': { 'c': 0, 'r': 0 },
    //     'e': { 'c': 0, 'r': 1 }
    // },
    // {
    //     's': { 'c': 1, 'r': 0 },
    //     'e': { 'c': 1, 'r': 1 }
    // },
    // {
    //     's': { 'c': 2, 'r': 0 },
    //     'e': { 'c': 2, 'r': 1 }
    // }, {
    //     's': { 'c': 3, 'r': 0 },
    //     'e': { 'c': 3, 'r': 1 }
    // }, {
    //     's': { 'c': 4, 'r': 0 },
    //     'e': { 'c': 4, 'r': 1 }
    // }, {
    //     's': { 'c': 5, 'r': 0 },
    //     'e': { 'c': 5, 'r': 1 }
    // }, {
    //     's': { 'c': 6, 'r': 0 },
    //     'e': { 'c': 6, 'r': 1 }
    // }, {
    //     's': { 'c': 7, 'r': 0 },
    //     'e': { 'c': 7, 'r': 1 }
    // }, {
    //     's': { 'c': 8, 'r': 0 },
    //     'e': { 'c': 8, 'r': 1 }
    // }, {
    //     's': { 'c': 9, 'r': 0 },
    //     'e': { 'c': 9, 'r': 1 }
    // }, {
    //     's': { 'c': 10, 'r': 0 },
    //     'e': { 'c': 10, 'r': 1 }
    // }, {
    //     's': { 'c': 11, 'r': 0 },
    //     'e': { 'c': 11, 'r': 1 }
    // }, {
    //     's': { 'c': 12, 'r': 0 },
    //     'e': { 'c': 12, 'r': 1 }
    // }, {
    //     's': { 'c': 16, 'r': 0 },
    //     'e': { 'c': 16, 'r': 1 }
    // }, {
    //     's': { 'c': 13, 'r': 0 },
    //     'e': { 'c': 15, 'r': 0 }
    // }
    // ];
    constructor() {
        //console.log('导出');
    }
    // 导出主函数
    public saveAsExcelFile(fileName: string, resDate: any): void {
        // var outputPos = Object.keys(this.exportOrderList);
        // console.log(resDate);
        const type = undefined;
        // 用来腾出来标头空间
        // resDate[0] = this.headList[1];
        // resDate.unshift(this.headList[0]);
        // const showSheet: XLSX.WorkSheet = XLSX.utils.format_to_Sheet(Data, 0, 3, keyMap);
        
        const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(resDate,{skipHeader:false});
        // const worksheet: XLSX.WorkSheet = this.transformExportDataToArray(resDate);
        // for (const item of this.head) {
        //     worksheet.push(item);
        // }
        // 样式
        // worksheet.N1.s = {
        //     alignment: {
        //       horizontal: 'center' ,
        //       vertical: 'center'
        //     }
        // };
        // const merges = this.getMergeLocal(mergeNumber,2,this.Headmerges);
        // worksheet['!merge'] = merges;
        const tmpWB = {
            Sheets: { 'data':
                      {...worksheet, // 内容
                   }},
            SheetNames: ['data']
        };
        const data: Blob = new Blob([this.s2ab(XLSX.write(tmpWB,
            { bookType: (type == undefined ? 'xlsx' : type), bookSST: false, type: 'binary' }// 这里的数据是用来定义导出的格式类型
        ))], {
                type: ''
            }); // 创建二进制对象写入转换好的字节流
        FileSaver.saveAs(data, fileName + EXCEL_EXTENSION);
    }
    s2ab(s: string) { // 字符串转字符流
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i !== s.length; ++i) {
            // tslint:disable-next-line:no-bitwise
            view[i] = s.charCodeAt(i) & 0xFF;
        }
        return buf;
    }

    transformExportDataToArray(json: any): XLSX.WorkSheet { // json转换函数
        const tmpdata = json[0];
        // json.unshift({})
        // this.jsono.unshift({"保质期临期预警(天)": "adventLifecycle"});//向json数组前面加入{}
        const keyMap = []; // 表头数组
        // keyMap =Object.keys(this.jsono[0]);

        for (const k in tmpdata) {// 循环获取表头
            keyMap.push(k);
            json[0][k] = k;
        }

        const tmpdata2 = [];// 用来保存转换好的json

        json.map((v, i) => keyMap.map((k, j) => ({
            v: v[k],
            position: (j > 25 ? this.getCharCol(j) : String.fromCharCode(j + 65)) + (i + 1)
        }))).reduce((prev, next) => prev.concat(next)).forEach((v, i) => tmpdata2[v.position] = {
            v: v.v
        });
        return tmpdata2;
    }

    // 将指定的自然数转换为26进制表示。映射关系：[0-25] -> [A-Z]。
    getCharCol(n: any): any {
        const temCol: any = '';
        let s: any = '';
        let m: any = 0;
        while (n > 0) {
            // 字母
            m = n % 26 + 1;
            // 最后的结果
            s = String.fromCharCode(m + 64) + s;
            n = (n - m) / 26;
        }
        return s;
    }
    // 增加merge数组
    getMergeLocal(json: number[], startFlag: number, Headmerge: any[]) {
        const merges = Headmerge;
        let flag = startFlag;
        for (const i of json) {
            for (let j = 0; j < 18; j++) {
                if (j === 14 || j === 15 || j === 13) {
                    continue;
                } else {
                    merges.push({ s: { c: j, r: flag }, e: { c: j, r: flag + i - 1 } });
                }
            }
            flag += i ;
        }
        return merges;
    }

    public getMerge(json: any) {
        const keyMap = []; // 表头数组
        const merges = [];
        const tmpdata = json[0];
        // json.unshift({});
        for (const k in tmpdata) {// 循环获取表头
            keyMap.push(k);
            json[0][k] = k;
        }
        let flag = true;
        let mergeId = 0;
        let date = [];
        json.map((v, i) => keyMap.map((k, j) => {
            if (i === 1 && j === 0) {
                date = v[k];
            }
            if (j === 0 && i > 1 && i < json.length - 1) {

                if (v[k] === date && flag === true) {
                    if (i === 2) {
                        merges.push({ s: { c: 0, r: 1 }, e: { c: 0, r: 0 } });
                    }
                }
                if (v[k] === date && flag === false) {
                    merges.push({ s: { c: 0, r: 0 }, e: { c: 0, r: 0 } });
                    merges[mergeId].s.r = i - 1;
                    flag = true;
                }
                if (v[k] !== date && flag === false) {
                    date = v[k];
                }
                if (v[k] !== date && flag === true) {
                    flag = false;
                    date = v[k];
                    if (i !== 2) {
                        merges[mergeId].e.r = i - 1;
                        mergeId++;
                    }

                }
            }
            if (j === 0 && i === json.length - 1) {
                if (v[k] === date && flag === true) {
                    if (i === 2) {
                        merges.push({ s: { c: 0, r: 1 }, e: { c: 0, r: 2 } });
                    } else {
                        merges[mergeId].e.r = i;
                    }

                }
                if (v[k] === date && flag === false) {
                    merges.push({ s: { c: 0, r: i - 1 }, e: { c: 0, r: 0 } });
                    merges[mergeId].e.r = i;
                    flag = true;
                }
                if (v[k] !== date && flag === true) {
                    flag = false;
                    merges[mergeId].e.r = i - 1;
                    mergeId++;
                    date = v[k];
                }
            }
        }));
        return merges;
    }
}
