import { FlowData, FlowSubmitData } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';

import openid from '@tiger/openid';
import { contextPath } from '../../../../conf';
import WorkflowQuery from '../../workflow-query';
import { topologyName } from '../writtendoc-delete-config';
import Workflow from '../writtendoc-delete-workflow';

type SubmitResult = AjaxResult<string[]>;
type UpsertResult = AjaxResult<string>;

/**
 * 工单创建与提交
 */
@RestController
@RequestMapping(`${contextPath}/xhr/workflow/${topologyName}`)
export default class WrittendocDeleteWorkflowController {

    constructor(private workflow: Workflow, private workflowQuery: WorkflowQuery) {
    }

    /**
     * 工单创建并提交，触发流程流转(审批通过或者不通过)
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/submit.json')
    async submit(ctx: RequestContext<FlowSubmitData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const flowData: FlowData = await this.workflowQuery.detailQuery(flowMetaData.flowId, flowMetaData.currentNodeId);
            flowNodeMessage.content = flowData.flowNodeMessage.content;
            if (!flowNodeMessage.approved) {
                flowNodeMessage.content.approved = false;
                flowNodeMessage.content.rejectReason = flowNodeMessage.operateRemark;
            }
            const nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            console.error('工单提交失败:', e);
            ctx.body = AjaxResult.internal('工单提交失败');
        }
    }

    /**
     * 工单创建与保存草稿，不提交，不触发流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/upsert.json', [openid()()])
    async upsert(ctx: RequestContext<FlowData, UpsertResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const flowId = await this.workflow.upsert(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(flowId);
        } catch (e) {
            console.error('工单保存失败:', e);
            ctx.body = AjaxResult.internal('工单保存失败');
        }
    }

    /**
     *
     * @description 立项人撤回工单
     *
     * @param ctx
     */
    @PostMapping('/revoke.do', [openid()()])
    async revoke(ctx: RequestContext<FlowData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const rst = ctx.request.body;
        const userInfo = ctx.openIDInfo;
        rst.flowNodeMessage.content.branch = 1;

        const flowNodeMessage: any = { ...rst.flowNodeMessage, operateRemark: '撤回工单', approved: false };
        flowNodeMessage.content.approved = false;
        // 目前工单所处的节点版本信息
        const flowMetaData = {
            flowId: rst.flowMetaData.flowId,
            version: rst.flowMetaData.version,
            currentNodeId: rst.flowMetaData.currentNodeId
        };
        let nextNodeIds;
        try {
            nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            console.error('工单撤回失败:', e);
            ctx.body = AjaxResult.internal('工单撤回失败');
        }
    }
}
