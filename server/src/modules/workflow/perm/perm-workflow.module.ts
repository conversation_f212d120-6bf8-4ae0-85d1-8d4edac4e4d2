import { TgModule } from '@tiger/boot';

import { PermWorkflowLinkAuditController } from './controller/perm-workflow-link-audit.controller';
import { PermWorkflowNotifyController } from './controller/perm-workflow-notify.controller';
import { PermWorkflowController } from './controller/perm-workflow.controller';

@TgModule({
    imports: [],
    controllers: [PermWorkflowLinkAuditController, PermWorkflowNotifyController, PermWorkflowController]
})
export class PermWorkflowModule { }
