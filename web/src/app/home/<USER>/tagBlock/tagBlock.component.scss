a,
a:visited,
a:active {
    text-decoration: none;
}

.m-category {
    box-sizing: border-box;
    padding: 50px 0 0 0;
    position: relative;
    margin-left: -20px;
    margin-bottom:10px;
}

.m-category h5 {
    font-size: 20px;
    line-height:30px;
    padding-left: 20px;
    color: #666666;
    position: absolute;
    width: 100%;
    top: 10px;
    font-weight: normal;
    letter-spacing: 2px;
    margin-top: 0;
    .icon-intro {
        float: right;
        >span {
            display: inline-block;
            line-height: 20px;
            font-size: 14px;
            margin-left: 20px;
            color: #666666;
            >i {
                display: inline-block;
                width:3px;
                height:12px;
                margin-right: 5px;
                margin-bottom: -1px;
            }
            .normal-icon {
                background: #538AFF;
            }
            .active-icon {
                background: #00A854;
            }
        }
    }
}

.m-category {
    &.fav-tag {
        padding-top: 10px;
        h5 {
            color: #333333;
            line-height:42px;
            font-size:28px;
            letter-spacing: normal;
        }
    }
}

.m-category a {
    color: #ffffff;
}

.m-category .app-content {
    width: 20%;
    min-width: 220px;
    // margin-left: 14px;
    margin-bottom: 20px;
    position: relative;
    white-space: nowrap;
    overflow: visible;
    height: 104px;
    padding-left:20px;
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10);
    // border-radius: 8px;
    &.app-content-4{
        width: 25%;
    }
}
.m-category .app-content:hover {
    .u-app-block {
        min-width: calc( 100% - 20px );
        box-shadow: 0 4px 20px 0 rgba(0,0,0,0.5);
        position: absolute;
        z-index: 99;
        // padding-right: 50px;
    }
    .collect-box {
        visibility: visible;
        z-index: 100;
    }
}

.m-category .u-app-block {
    cursor: pointer;
    // line-height: 104px;
    height: 104px;
    background-color: #ffffff;
    box-sizing: border-box;
    position: relative;
    font-size: 20px;
    color: #ffffff;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.15);
    padding-right: 20px;
    border-radius: 4px;
    >a {
        padding-left: 30px;
        display: inline-block;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        .app-icon {
            margin-top: 15px;
            display: block;
            width: 34px;
            height: 34px;
            margin-bottom: 10px;
        }
        span{
            line-height: 30px;
            width: 100%;
            display: inline-block;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .app-icon-0 {
        background: url('../../../../assets/img/supply.png') no-repeat center;
    }
    .app-icon-1 {
        background: url('../../../../assets/img/data.png') no-repeat center;
    }
    .app-icon-2 {
        background: url('../../../../assets/img/distribution.png') no-repeat center;
    }
    .app-icon-3 {
        background: url('../../../../assets/img/operation.png') no-repeat center;
    }
    .app-icon-4 {
        background: url('../../../../assets/img/support.png') no-repeat center;
    }
    .app-icon-5 {
        background: url('../../../../assets/img/dev.png') no-repeat center;
    }
}

.m-category {
    .app-block-0 {
        background-color: #006AB5;
        background-image: linear-gradient(90deg, #1C81BD 0%, #006AB5 100%);
    }
    .app-block-1 {
        background-color: #00927F;
        background-image: linear-gradient(90deg, #18AA97 0%, #00927F 100%);

    }

    .app-block-2 {
        background-color: #5B3CAC;
        background-image: linear-gradient(90deg, #6642CD 0%, #5B3CAC 100%);
    }

    .app-block-3 {
        background-color: #8E6669;
        background-image: linear-gradient(90deg, #9F7478 0%, #8E6669 100%);
    }

    .app-block-4 {
        background-color: #324588;
        background-image: linear-gradient(90deg,  #3A5AA0 0%, #324588 100%);
    }

    .app-block-5 {
        background-color: #9F7306;
        background-image: linear-gradient(90deg, #AE8036  0%, #9F7306 100%);
    }
}

.m-category .collect-box {
    position:absolute;
    right: 15px;
    top:15px;
    width:34px;
    height:34px;
    visibility: hidden;
    .collect-shadow {
        background: #000000;
        opacity: .2;
        width:34px;
        height:34px;
        border-radius: 50%;
    }
    .u-top {
        cursor: pointer;
        color: #3187F6;
        font-size: 18px;
        width: 34px;
        height:34px;
        top:0;
        left:0;
        text-align: center;
        text-align: center;
        background-color: transparent;
        position: absolute;
        font-weight: 100;
        &.icon-collect {
            background: url('../../../../assets//img/collect.svg') no-repeat center;
        }
        &.icon-collected {
            background: url('../../../../assets/img/collected.svg') no-repeat center;
        }
    }
}

.m-category .lock {
    position: absolute;
    bottom: 0;
    right: 0px;
    width: 48px;
    height: 36px;
    .lock-shadow {
        position: absolute;
        opacity: 0.2;
        width: 0;
        height: 0;
        border-bottom: 36px solid #ffffff;
        border-left: 48px solid transparent;
    }
    .lock-icon {
        position: absolute;
        bottom: 4px;
        right: 8px;
        width: 12px;
        height: 15px;
        background: url('../../../../assets/img/unlock.png') no-repeat center;
    }
}

.m-category .u-app-block.add-app {
    background-color: transparent;
    border: 4px dashed #d7e2eb;
    box-sizing: border-box;
    box-shadow: none;
    line-height: 104px;
    color: #d7e2eb;
    font-size: 50px;
    text-align: center;
    padding-left: 0;
}

@media (max-width: 980px) {
    .m-category {
        min-width: auto;
        margin-left: 0;
    }
    .m-category .app-content{
        min-width: 100%;
        padding-left: 0;
        margin-bottom: 5px
    }
    .m-category .app-content:hover {
        .u-app-block {
            min-width: 100%;
        }
    }
}