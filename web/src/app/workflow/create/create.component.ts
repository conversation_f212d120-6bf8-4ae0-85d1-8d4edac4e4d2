import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { AjaxUrl } from '../../config';
import { IUser, UserService } from '../../shared/services/user.service';
import { flowServerHost } from '../../config'

@Component({
    templateUrl: './create.component.html',
    styleUrls: ['./create.component.scss']
})
export class WorkFlowCreateComponent {
    userInfo: IUser = null;
    isInOrgUnit: boolean = true; // 是否在组织架构内
    canCreateSpecial: boolean = false; // 是否有权限创建个人特殊权限工单
    canCreateOrgID: boolean = false; // 是否有权限创建组织身份权限工单
    isInYanXuan: boolean = false; // 是否属于严选事业部
    constructor(
        private router: Router,
        private ajax: Ajax,
        private toastr: SharkToastrService,
        private userServ: UserService
    ) {

    }

    async ngOnInit() {
        await this.getUserInfo();
        this.setInOrgUnitFlag();
        this.getCanCreateSpecial()
        this.getCanCreateOrgID()
        this.getCanCreateConcurrentFlow()
    }

    async getUserInfo() {
        this.userInfo = await this.userServ.getUser();
    }

    async setInOrgUnitFlag() {
        try {
            const rst = await this.ajax.get(AjaxUrl.permcenter.getOrgPosByUid, {
                uid: this.userInfo.email
            });
            const orgPosIds = rst.data || [];
            if (orgPosIds.length > 0) {
                this.isInOrgUnit = true;
            } else {
                this.isInOrgUnit = false;
            }
        } catch (e) {
            this.toastr.error(`查询用户是否在组织架构内失败: ${e.errorCode}`);
        }
    }

    async getCanCreateSpecial() {
        try {
            const rst = await this.ajax.get(`/bflow-base${flowServerHost}/workflow/apply/v1/isInTargetOrgPos?uid=${this.userInfo.email}`);
            this.canCreateSpecial = rst.data
        } catch (e) {
            this.toastr.error(`查询用户创建个人特殊权限工单权限失败: ${e.errorCode}`);
        }
    }

    async getCanCreateOrgID() {
        try {
            if (this.userInfo.email === '<EMAIL>') {
                this.canCreateOrgID = true
            } else {
                const rst = await this.ajax.get(`/bflow-base${flowServerHost}/workflow/orgIdentityPermit/applyPermission/query?uid=${this.userInfo.email}`);
                this.canCreateOrgID = rst.data
            }
        } catch (e) {
            this.toastr.error(`查询用户创建个人特殊权限工单权限失败: ${e.errorCode}`);
        }
    }

    

    /**
     * 兼岗工单是否允许创建
     */
    
    async getCanCreateConcurrentFlow() {
        let rootId = 963
        if (window.location.host.indexOf('local.yx.mail.netease.com') > -1 || window.location.host.indexOf('remote.yx.mail.netease.com') > -1 || window.location.host.indexOf('micro.test.yx.mail.netease.com') > -1 || window.location.host.indexOf('test.yx.mail.netease.com') > -1) {
            rootId = 220
        }
        try {
            const rst = await this.ajax.get(`/bflow-base${flowServerHost}/icac/user/bizOrg/allTeam/listByUid.json?uid=${this.userInfo.email}`);
            if (rst && rst.data && rst.data.length) {
                rst.data.forEach(element => {
                    if (element && element.rootOrgPosId && element.rootOrgPosId === rootId) {
                        this.isInYanXuan = true
                    }
                });
            }
        } catch (e) {
            this.toastr.error(`查询用户创建个人特殊权限工单权限失败: ${e.errorCode}`);
        }
    }

    /**
     * 加入组织架构
     */
    applyOnboard() {
        // if (this.isInOrgUnit) {
        //     this.toastr.warning('您已经在严选组织架构中，请勿重复申请', 10000);
        //     return;
        // }
        this.router.navigate(['/workflow/detail', {
            topologyId: 'nav_personnelchange_onboard'
        }]);
    }

    /**
     * 离职
     */
    applyDeparture() {
        this.router.navigate(['/workflow/detail', {
            topologyId: 'nav_personnelchange_departure'
        }]);
    }

    /**
     * 转岗
     */
    applyTransfer() {
        this.router.navigate(['/workflow/detail', {
            topologyId: 'nav_personnelchange_transfer'
        }]);
    }

        
    /**
     * 个人特殊权限
     */
    applySpecial() {
        window.location.href = encodeURI(`/bflow/icacflow/#/personalSpecialFlow`); 
    }

    /**
     * 入职工单
     */
    async applyEntry() {
        try {
            const rst = await this.ajax.get(`/bflow-base${flowServerHost}/workFlowInfo/applyEntry/query?uid=${this.userInfo.email}`);
            let flowId = null
            if (rst.data.length) {
                rst.data.forEach(item => {
                    if (item.state === 0) {
                        flowId = item.flowId
                    }
                });
            }
            if (flowId) {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow/${flowId}`);
            } else {
                window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow`);
            }
        } catch (e) {
            window.location.href = encodeURI(`/bflow/icacflow/#/entryFlow`); 
        }
    }
    
    applyQuit() {
        window.location.href = encodeURI(`/bflow/icacflow/#/quitFlow`); 
    }

    applyHoldPost() {
        window.location.href = encodeURI(`/bflow/icacflow/#/concurrentFlow`); 
    }

    /**
     * 组织身份权限创建
     */
    applyOrgId() {
        window.location.href = encodeURI(`/bflow/icacflow/#/identityFlow`); 
    }


    /**
     * 申请业务系统权限
     */
    applyPerm() {
        this.router.navigate(['/workflow/permDetail', {
            topologyId: 'nav_personnelchange_perm'
        }]);
    }

    /**
     * 外部系统权限
     */
    applyExternal() {
        window.location.href = encodeURI(`/bflow/icacflow/#/externalSystemFlow`); 
    }
}
