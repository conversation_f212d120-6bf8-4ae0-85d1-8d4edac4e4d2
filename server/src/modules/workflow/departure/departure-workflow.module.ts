import { TgModule } from '@tiger/boot';

import { DepartureWorkflowLinkAuditController } from './controller/departure-workflow-link-audit.controller';
import { DepartureWorkflowNotifyController } from './controller/departure-workflow-notify.controller';
import { DepartureWorkflowController } from './controller/departure-workflow.controller';

@TgModule({
    imports: [],
    controllers: [DepartureWorkflowLinkAuditController, DepartureWorkflowNotifyController, DepartureWorkflowController]
})
export class DepartureWorkflowModule { }
