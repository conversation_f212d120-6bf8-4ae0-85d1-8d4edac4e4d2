import { Component, Input } from '@angular/core';
import { Ajax, SharkBaseModal,SharkModalParams, SharkToastrService } from '@shark/shark-angularX';
// import { AjaxUrlService } from '../../../shared/services/';
// import { CustomerInformationService } from '../../../../../shared/services/customer-information.service';
import { AjaxUrl } from '../../../config';

@Component({
    selector: 'uploadTainInfo',
    templateUrl: './uploadTainInfo.component.html'
})
export class uploadTainInfoComponent extends SharkBaseModal {
    
    url: string = AjaxUrl.docManagement.upload;
    fileList = [];
    trainTime: number;
    item:any;

    constructor(private ajax: Ajax,
        private params: SharkModalParams,
        private sharkToastrService: SharkToastrService,
        private toast: SharkToastrService) {
        super();
        this.item = this.params.data.item;
        console.log(this.item);
    }
    onSelected(e: any) {

    }
    // 选择文件时的预处理函数，判断文件是否合法
    filterPreSelected = (res: any) => {
        const isLt1M = res.nativeFile.size / 1024 / 1024 <= 2;
        if (!isLt1M) {
            this.sharkToastrService.error('上传文件需小于等于2MB!');
        }
        return isLt1M;
    }
    onFailed(e: any) {
        // console.log(e);
        this.sharkToastrService.error('上传文件失败！');
        this.fileList = [];
    }
    //提交培训信息
    submit(item:any) {
        // 校验是否有上传附件
        if (this.fileList.length === 0) {
            this.sharkToastrService.error('请上传培训信息文件');
            return false;
        }
        // console.log(item);
        this.trainTime = new Date().getTime();
        this.item.flowNodeMessage['fileKey'] = this.fileList[0]['key'];
        this.item.flowNodeMessage['fileName'] = this.fileList[0]['name'];
        this.item.flowNodeMessage['trainTime'] = this.trainTime;
        const params = {
            flowMetaData: this.item.flowMetaData,
            flowNodeMessage: this.item.flowNodeMessage
        }
        this.ajax.postByJson(AjaxUrl.docManagement.submitTrain, params).then((res) => {
            if(res.code === 200){
                this.toast.success('提交成功!');
                this.close('ok');
            }
        }, (err) => {
            this.sharkToastrService.error(err.message);
        });
    }
}
