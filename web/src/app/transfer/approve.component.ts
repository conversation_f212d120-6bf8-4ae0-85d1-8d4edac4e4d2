import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { Event } from '@sharkr/utils';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './approve.component.html',
    selector: 'transfer-approve'
})
export class TransferApproveComponent {
    @ViewChild('form') form;
    @Input()
    workFlowData: WorkFlowData = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    approveData: any = {
        flowId: '',
        approved: true,
        transferType: 0,
        operateRemark: ''
    };

    approveChoices: any = [];
    transferTypeChoices: any = [];
    // 是否转出指导员的审批节点
    isOutPoliticalAudit = false;
    switch: boolean = true;
    pedding: boolean = false;
    constructor(
        private router: Router,
        private ajax: Ajax,
        private toastr: SharkToastrService
    ) {
        this.initDataOption();
    }

    ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
            this.isOutPoliticalAudit = this.workFlowData.nodeId === '30010407';
        }
    }

    initDataOption() {
        this.approveChoices = [{
            label: '通过',
            value: true
        }, {
            label: '不通过',
            value: false
        }];
        this.transferTypeChoices = [{
            label: '职责未变转岗(系统不对权限做删减或变更)',
            value: 0
        }, {
            label: '职责变更转岗(系统自动收回原岗位涉及全部权限，新岗位需员工自行申请权限)',
            value: 1
        }];

    }

    submit() {
        this.form.submit(async () => {
            this.pedding = true;
            this.approveData.flowId = this.workFlowData.flowId;
            try {
                await this.ajax.postByJson(AjaxUrl.submitFlow.transfer, {
                    flowMetaData: this.flowData.flowMetaData,
                    flowNodeMessage: {
                        ...this.approveData
                    }
                });
                Event.dispatch('nav:refreshDealing', null);
                this.toastr.success('工单审批成功');
                this.router.navigate(['/workflow/dealingList']);
            } catch (e) {
                this.pedding = false;
                this.toastr.error(`工单审批失败: ${e.errorCode}`);
            }
        });
    }
}
