import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Ajax, SharkModalService, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';

@Component({
    selector: 'flow-doc-authority-trainInfo',
    templateUrl: './trainInfo.component.html',
})
export class TrainInfoComponent implements OnInit {
    @Input() workFlowData: WorkFlowData;
    // 培训文档列表
    // fileList: any = [];5260338        5301525
    fileList = [];
    // 附件上传路径
    url: string = AjaxUrl.docManagement.upload;
    switch: boolean = true;
    // 培训时间
    trainTime: number;
    // 培训
    trainStatus: number; //不培训 0，培训 1
    // 有上传培训的权限
    hasAuthority: boolean = false;
    // 展示培训信息
    showTrainDoc: boolean = false;
    isDetail:boolean = false; //false:不是从文档管理列表跳转进来，true：是 

    //获取当前登录人邮箱
    cookies: {
        YX_CSRF_TOKEN?: string;
        yx_name?: string;
        yx_username?: string;
        has_user?: string;
    } = {};
    constructor(
        private ajax: Ajax,
        private router: Router,
        private sharkModalService: SharkModalService,
        private sharkToastrService: SharkToastrService,
        private route: ActivatedRoute
    ) {

        //获取当前登录人邮箱
        document.cookie.split('; ').forEach(i => {
            const kv = i.split('=');
            this.cookies[kv[0]] = decodeURIComponent(kv[1]);
        });

        this.route.params.subscribe((params) => {
            this.isDetail = params.isDetail !== undefined ? params.isDetail : false;
        });
    }

    public ngOnInit(): void {
        const createUser = this.workFlowData.flowData.flowNodeMessage['content'].creatorUId;//文档创建人邮箱
        if (this.cookies.yx_username === createUser) {
            //若当前登录人是 文档创建人且从文档管理列表跳转过来，则有权限进行培训文档上传
            if(this.isDetail){
                this.hasAuthority = true;
            }
        }
        this.getTrainInfo();
    }

    setSwitch() {
        this.switch = !this.switch;
    }
    onSelected(e: any) {
        console.log(e);
    }
    // 选择文件时的预处理函数，判断文件是否合法
    filterPreSelected = (res: any) => {
        const isLt1M = res.nativeFile.size / 1024 / 1024 <= 2;
        if (!isLt1M) {
            this.sharkToastrService.error('上传文件需小于等于2MB!');
        }
        return isLt1M;
    }
    onFailed(e:any){
        // console.log(e);
        this.sharkToastrService.error('上传文件失败！');
        this.fileList=[];
    }
    // 获取附件信息
    getTrainInfo() {
        const param = {
            'flowId': this.workFlowData.flowData.flowMetaData.flowId
        }
        this.ajax.post(AjaxUrl.bpmflow.queryFlowDetail, param).then((res) => {
            // console.log(res.data);
            console.log(res.data.flowNodeMessage.content.fileName);
            if (res.data.flowNodeMessage.content.fileName) {
                //有上传附件
                this.showTrainDoc = true;//展示详情页
                //把数据传给页面
                this.fileList=[{
                    key:res.data.flowNodeMessage.content.fileKey,
                    name:res.data.flowNodeMessage.content.fileName
                }]
                this.trainTime = res.data.flowNodeMessage.content.trainTime;
            }
        })
    }

    // 返回
    dismiss() {
        // this.router.navigate(['/workflow/docList']);
        history.back();
    }
    //提交培训信息
    submit() {
        // 校验是否有上传附件
        if(this.fileList.length === 0){
            this.sharkToastrService.error('请上传培训信息文件');
            return false;
        }
        this.trainTime = new Date().getTime();
        this.workFlowData.flowData.flowNodeMessage['fileKey'] = this.fileList[0]['key'];
        this.workFlowData.flowData.flowNodeMessage['fileName'] = this.fileList[0]['name'];
        this.workFlowData.flowData.flowNodeMessage['trainTime'] = this.trainTime;
        const params = {
            flowMetaData: this.workFlowData.flowData.flowMetaData,
            flowNodeMessage: this.workFlowData.flowData.flowNodeMessage
        }
        this.ajax.postByJson(AjaxUrl.docManagement.submitTrain, params).then((res) => {
            this.showTrainDoc = true;
        }, (err) => {
            this.showTrainDoc = false;
            this.sharkToastrService.error(err.message);
        });
    }

}