import { Injectable } from '@angular/core';
import { Ajax } from '@shark/shark-angularX';
import { AppConfig, AjaxUrl } from '../../config';

@Injectable()
export class CmdbService {

    cmdbRoleMap: any = {
        products: '产品',
        services: '服务',
        domains: '业务域'
    }
    // 用户是否存在CMDB 角色 
    cmdbRole: any = { products: false, services: false, domains: false };
    // cmdb 角色文案
    cmdbRoleText: string = ''

    constructor(
        private ajax: Ajax
    ) {
    }
    // 获取cmdb产品的产品经理列表
    getProductManage(productCode) {
        return this.ajax.get(`${AppConfig.contextPath}/xhr/cmdbProduct/api/v2/product/${productCode}/user`)
    }

    // 获取用户在cmdb的角色 --yangbo 加
    getCmdbRoles(uid: string) {
        return this.ajax.get(AjaxUrl.cmdb.getRoles, { uid })
    }

    /**
     *
     *  cmdb 角色校验
     * @param {string} uid
     * @returns 校验文案
     * @memberof CmdbService
     */
    async checkCmdbRoles(uid: string) {
        this.cmdbRoleText = ''
        const data = (await this.getCmdbRoles(uid)).data.data
        for (const key in this.cmdbRole) {
            this.cmdbRole[key] = data[key] && data[key].length > 0
            if (this.cmdbRole[key] === true) {
                if (this.cmdbRoleText.length > 0 && this.cmdbRoleText.length < 4) {
                    this.cmdbRoleText = this.cmdbRoleText + '与' + this.cmdbRoleMap[key]
                    continue
                } if (this.cmdbRoleText.length > 4) {
                    this.cmdbRoleText = this.cmdbRoleText + '以及' + this.cmdbRoleMap[key]
                    continue
                } else {
                    this.cmdbRoleText = this.cmdbRoleText + this.cmdbRoleMap[key]
                    continue
                }
            }
        }
        return this.cmdbRoleText === '' ? '' : `您在CMDB里还有${this.cmdbRoleText}的角色未交接`
    }

}