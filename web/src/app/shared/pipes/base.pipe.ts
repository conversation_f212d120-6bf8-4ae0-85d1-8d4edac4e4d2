export class BasePipe {
    constructor() {

    }
    transfromData(data: any, keyType: string = 'number', key?, isList?, displayVal: string = 'value', displayName: string = 'name'): any {
        if (isList) {
            const list = [];
            for (const k in data) {
                if (data.hasOwnProperty(k)) {
                    list.push({
                        [displayVal]: keyType === 'number' ? Number(k) : k,
                        [displayName]: data[k]
                    });
                }
            }
            return list;
        } else {
            const val = data[key] || '';
            return val;
        }
    }
}
