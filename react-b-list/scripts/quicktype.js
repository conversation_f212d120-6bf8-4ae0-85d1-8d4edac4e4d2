const {
  InputData,
  JSONSchemaInput,
  JSONSchemaStore,
  TypeScriptTargetLanguage,
  parseJSON,
  quicktype,
} = require('quicktype-core');
const fs = require('fs');
const path = require('path');

// Header to add to all files.
const header = `
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN 'npm  run  build_schema or npm run build'
// BY YANXUAN TEAM

// tslint:disable:no-global-tslint-disable
// tslint:disable

`;

// Footer to add to all files.
const footer = ` `;

class FetchingJSONSchemaStore extends JSONSchemaStore {
  constructor(inPath) {
    super();
    this._inPath = inPath;
  }

  async fetch(address) {
    const URL = require("url");
    const url = URL.parse(address);
    let content = null;
    if (url.protocol === 'muse-cli:') {
      console.log('hostname, path', url.hostname, url.path)
      let filePath = path.join(__dirname, '../', url.hostname, url.path);
      console.log('filePath', filePath);
      content = fs.readFileSync(filePath, 'utf-8').trim();
    }
    if (content === null && !path.isAbsolute(address)) {
      const resolvedPath = path.join(path.dirname(this._inPath), address);

      // Check relative to inPath
      if (fs.existsSync(resolvedPath)) {
        content = fs.readFileSync(resolvedPath, 'utf-8');
      }
    }

    if (content === null && fs.existsSync(address)) {
      content = fs.readFileSync(address, 'utf-8').trim();
    }

    if (content == null) {
      return undefined;
    }

    content = appendDeprecatedDescription(content);

    return parseJSON(content, "JSON Schema", address);
  }
}


/**
 * Create the TS file from the schema, and overwrite the outPath (or log).
 * @param {string} inPath 
 * @param {string} outPath 
 */
async function main(inPath, outPath) {
  const content = await generate(inPath);

  if (outPath === '-') {
    console.log(content);
    process.exit(0);
  }
  outPath = path.resolve('.', outPath);
  fs.writeFileSync(outPath, content, 'utf-8');
}


async function generate(inPath) {

  const inputData = new InputData();
  const content = fs.readFileSync(inPath, 'utf-8');
  const source = {
    name: 'Schema',
    schema: appendDeprecatedDescription(content)
  };

  await inputData.addSource('schema', source, () => {
    return new JSONSchemaInput(new FetchingJSONSchemaStore(inPath));
  });

  const lang = new TypeScriptTargetLanguage();

  const {
    lines
  } = await quicktype({
    lang,
    inputData,
    alphabetizeProperties: true,
    rendererOptions: {
      'just-types': 'true',
      'explicit-unions': 'true',
      'acronym-style': 'camel',
    },
  });

  return header + lines.join('\n') + footer;
}

/**
 * Converts `x-deprecated` to `@deprecated` comments.
 * @param {string} schema
 */
function appendDeprecatedDescription(schema) {
  const content = JSON.parse(schema);
  const props = content.properties;

  for (const key in props) {
    let {
      description = '', 'x-deprecated': deprecated
    } = props[key];
    if (!deprecated) {
      continue;
    }

    description += '\n@deprecated' + (typeof deprecated === 'string' ? ` ${deprecated}` : '');
    props[key].description = description;
  }

  return JSON.stringify(content);
}

if (require.main === module) {
  const argv = process.argv.slice(2);
  if (argv.length < 2 || argv.length > 3) {
    console.error('Must include 2 or 3 arguments.');
    process.exit(1);
  }

  main(argv[0], argv[1])
    .then(() => process.exit(0))
    .catch(err => {
      console.error('An error happened:');
      console.error(err);
      process.exit(127);
    });
}

exports.generate = generate;