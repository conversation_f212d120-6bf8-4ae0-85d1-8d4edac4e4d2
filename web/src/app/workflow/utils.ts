export const formatPersonalSpecialFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_personal_special_rights'
    data.flowMetaData.ius_personal_special_rights = 'ius_personal_special_rights'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    // data.flowNodeMessage.nodeId = data.flowMetaData.currentNodeId
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatSpecialFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_special_rights'
    data.flowMetaData.ius_special_rights = 'ius_special_rights'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatSpecialChildFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_special_rights_child'
    data.flowMetaData.ius_special_rights_child = 'ius_special_rights_child'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatEntryFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_entry_apply_topology'
    data.flowMetaData.ius_entry_apply_topology = 'ius_entry_apply_topology'
    // data.flowNodeMessage.nodeId = data.flowMetaData.currentNodeId
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    if (data.flowNodeMessage.createUid) {
        data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
    } else {
        data.flowNodeMessage.createUser = (data.flowNodeMessage.businessObject || {}).uid
    }
}

export const formatQuitFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_quit_apply_topology'
    data.flowMetaData.ius_quit_apply_topology = 'ius_quit_apply_topology'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatHoldPostFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_hold_post_apply_topology'
    data.flowMetaData.ius_hold_post_apply_topology = 'ius_hold_post_apply_topology'
    // data.flowNodeMessage.nodeId = data.flowMetaData.currentNodeId
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatOrgIdentityFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_org_identity_permit_topology'
    data.flowMetaData.ius_org_identity_permit_topology = 'ius_org_identity_permit_topology'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatOrgIdentityChildFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_org_identity_permit_child_topology'
    data.flowMetaData.ius_org_identity_permit_child_topology = 'ius_org_identity_permit_child_topology'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatNewOrgIdentityFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_org_identity_confirm_topology'
    data.flowMetaData.ius_org_identity_confirm_topology = 'ius_org_identity_confirm_topology'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUser = data.flowNodeMessage.createUid
}

export const formatExternalSystemFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_ext_system_auth_apply'
    data.flowMetaData.ius_ext_system_auth_apply = 'ius_ext_system_auth_apply'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUserName = data.flowMetaData.createUser.userName
    data.flowNodeMessage.createUser = data.flowMetaData.createUser.uid
    data.flowNodeMessage.nodeId = data.flowMetaData.currentNodeId
    data.flowNodeMessage.flowId = data.flowMetaData.flowId
    const currNodeData = data.flowMetaData.currNodeDataList.filter(
      (v) => v.nodeId === data.flowMetaData.currentNodeId
    );
    if (currNodeData[0] && currNodeData[0].acceptorUserList) {
        data.flowNodeMessage.acceptorList = currNodeData[0].acceptorUserList.filter(v => v.uid).map(v => v.uid)
    }
}

export const formatExternalSystemClearFlowData = (data) => {
    data.flowMetaData.topologyName = 'ius_ext_system_auth_clear'
    data.flowMetaData.ius_ext_system_auth_clear = 'ius_ext_system_auth_clear'
    data.flowNodeMessage.createTime = data.flowMetaData.createTime
    data.flowNodeMessage.updateTime = data.flowMetaData.updateTime
    data.flowMetaData.globalId = data.flowMetaData.flowId
    data.flowNodeMessage.createUserName = data.flowMetaData.createUser.userName
    data.flowNodeMessage.createUser = data.flowMetaData.createUser.uid
    data.flowNodeMessage.nodeId = data.flowMetaData.currentNodeId
    data.flowNodeMessage.flowId = data.flowMetaData.flowId
    const currNodeData = data.flowMetaData.currNodeDataList.filter(
      (v) => v.nodeId === data.flowMetaData.currentNodeId
    );
    if (currNodeData[0] && currNodeData[0].acceptorUserList) {
        data.flowNodeMessage.acceptorList = currNodeData[0].acceptorUserList.filter(v => v.uid).map(v => v.uid)
    }
}

 // 组织类型
 export const workFlowTypeOptions: any = {
    value: '',
    data: [
        { value: '0', name: '权限类' },
        { value: '1', name: '业务组织架构类' },
        { value: '2', name: '文档类' }
    ]
};


export const flowNamesOptions = {
    [workFlowTypeOptions.data[0].value]: [
        {name: '个人特殊权限申请', value: 'ius_special_rights'},
        {name: '个人特殊权限申请（子工单）', value: 'ius_special_rights_child'},
        {name: '个人特殊权限申请（老）', value: 'ius_personal_special_rights'},
        {name: '组织身份权限申请', value: 'ius_org_identity_permit_topology'},
        {name: '组织身份权限申请（子工单）', value: 'ius_org_identity_permit_child_topology'},
        {name: '外部系统权限申请', value: 'ius_ext_system_auth_apply'},
        {name: '外部系统权限申请(RPA)', value: 'ius_ext_system_auth_apply_rpa'},
        {name: '外部系统权限清退', value: 'ius_ext_system_auth_clear'},
    ],
    [workFlowTypeOptions.data[1].value]: [
        {name: '加入严选组织架构（供外包同学使用）', value: 'ius_entry_apply_topology'},
        {name: '组织身份确认（入职）', value: 'ius_transfer_apply_topology'},
        {name: '组织身份确认（转岗）', value: 'ius_org_identity_confirm_topology'},
        {name: '兼岗申请', value: 'ius_hold_post_apply_topology'},
        {name: '离职申请（供外包同学使用）', value: 'ius_quit_apply_topology'},
        {name: '入职申请（工单已下线）', value: 'nav_personnelchange_onboard'},
        {name: '离职申请（工单已下线）', value: 'nav_personnelchange_departure'},
        {name: '转岗申请（工单已下线）', value: 'nav_personnelchange_transfer'}
    ],
    [workFlowTypeOptions.data[2].value]: [
        {name: '成文文档下发', value: 'written_doc_project'},
        {name: '文档作废工单', value: 'written_doc_delete_project'},
    ]
}


export const flowNamesAllOptions = [
    {name: '个人特殊权限申请', value: 'ius_special_rights'},
    {name: '个人特殊权限申请（子工单）', value: 'ius_special_rights_child'},
    {name: '组织身份权限申请', value: 'ius_org_identity_permit_topology'},
    {name: '组织身份权限申请（子工单）', value: 'ius_org_identity_permit_child_topology'},
    {name: '个人特殊权限申请（老）', value: 'ius_personal_special_rights'},
    {name: '外部系统权限申请', value: 'ius_ext_system_auth_apply'},
    {name: '外部系统权限申请(RPA)', value: 'ius_ext_system_auth_apply_rpa'},
    {name: '外部系统权限清退', value: 'ius_ext_system_auth_clear'},

    {name: '加入严选组织架构（供外包同学使用）', value: 'ius_entry_apply_topology'},
    {name: '组织身份确认（入职）', value: 'ius_transfer_apply_topology'},
    {name: '组织身份确认（转岗）', value: 'ius_org_identity_confirm_topology'},
    {name: '兼岗申请', value: 'ius_hold_post_apply_topology'},
    {name: '离职申请（供外包同学使用）', value: 'ius_quit_apply_topology'},
    {name: '入职申请（工单已下线）', value: 'nav_personnelchange_onboard'},
    {name: '离职申请（工单已下线）', value: 'nav_personnelchange_departure'},
    {name: '转岗申请（工单已下线）', value: 'nav_personnelchange_transfer'},

    // 文档类
    {name: '成文文档下发', value: 'written_doc_project'},
    {name: '文档作废工单', value: 'written_doc_delete_project'},
]


export const flowNamesOptionsForWait = [
    {name: '个人特殊权限申请', value: 'ius_special_rights'},
    {name: '个人特殊权限申请（子工单）', value: 'ius_special_rights_child'},
    {name: '组织身份权限申请', value: 'ius_org_identity_permit_topology'},
    {name: '组织身份权限申请（子工单）', value: 'ius_org_identity_permit_child_topology'},
    {name: '个人特殊权限申请（老）', value: 'ius_personal_special_rights'},
    {name: '外部系统权限申请', value: 'ius_ext_system_auth_apply'},
    {name: '外部系统权限申请(RPA)', value: 'ius_ext_system_auth_apply_rpa'},
    {name: '外部系统权限清退', value: 'ius_ext_system_auth_clear'},

    {name: '加入严选组织架构（供外包同学使用）', value: 'ius_entry_apply_topology'},
    {name: '组织身份确认（入职）', value: 'ius_transfer_apply_topology'},
    {name: '组织身份确认（转岗）', value: 'ius_org_identity_confirm_topology'},
    {name: '兼岗申请', value: 'ius_hold_post_apply_topology'},
    {name: '离职申请（供外包同学使用）', value: 'ius_quit_apply_topology'},
    {name: '入职申请（工单已下线）', value: 'nav_personnelchange_onboard'},
    {name: '离职申请（工单已下线）', value: 'nav_personnelchange_departure'},
    {name: '转岗申请（工单已下线）', value: 'nav_personnelchange_transfer'},

    // 文档类
    {name: '成文文档下发', value: 'written_doc_project'},
    {name: '文档作废工单', value: 'written_doc_delete_project'},
    {name: '', value: 'ius_personal_special_rights_child'}
]


    

/**
 * 工单状态枚举
 */

export const TOPOTYPE_STATUS = {
    STARTED: [0],
    APPROVALED: [100],
    ENDED: [200],
    REJECT: -1,
    REJECTINGCONFIRM: -2,
    RECALL: 8
}

export const topologyStatus = [
    {name: '已发起', value: TOPOTYPE_STATUS.STARTED},
    {name: '审批中', value: TOPOTYPE_STATUS.APPROVALED},
    {name: '已完结', value: TOPOTYPE_STATUS.ENDED},
    {name: '驳回', value: TOPOTYPE_STATUS.REJECT},
    {name: '驳回中待确认', value: TOPOTYPE_STATUS.REJECTINGCONFIRM},
    {name: '已撤回', value: TOPOTYPE_STATUS.RECALL}
]

export const formatRemarkLabel = (topologyId) => {
    if (topologyId === 'ius_hold_post_apply_topology') return '兼岗说明'
    if (topologyId === 'ius_quit_apply_topology') return '离职说明'
    return '备注'
}
