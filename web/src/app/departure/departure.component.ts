import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData, YXWorkFlowDetailBase } from '@shark/shark-yx-workflow';
import { UserService } from '../shared/services/user.service';

@Component({
    templateUrl: './departure.component.html'
})
export class DepartureComponent implements YXWorkFlowDetailBase {
    workFlowData: WorkFlowData = null;
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    // isEdit: boolean = false; //0代表不是编辑页，1代表编辑页
    isEdit: number = 0; // 是否从列表页的编辑按钮跳进来的， 1：是， 0：不是
    userInfo: any = {};
    constructor(
        private sharkToastrService: SharkToastrService,
        private route: Router,
        private userServ: UserService,
        private aRoute: ActivatedRoute
    ) {
        this.userServ.getUserInfo().then((userInfo) => {
            this.userInfo = userInfo.data;
        });
        this.aRoute.params.subscribe((params) => {
            this.isEdit = params.isEdit !== undefined ? Number(params.isEdit) : 0;
        });
    }
    ngOnInit() {
        document.title = '严选业务门户-离职工单';
    }
    ngOnDestroy() {
        document.title = '严选业务门户';
    }
    onFlowDataChange(workFlowData: WorkFlowData) {
        this.workFlowData = workFlowData;
        this.flowData = this.workFlowData.flowData || {
            flowMetaData: {},
            flowNodeMessage: {}
        };
    }

    showEdit() {
        const uid = this.userInfo.uid;
        if (this.workFlowData.nodeId === '30010201') {
            return true;
        }
        if (this.isEdit === 1) {
            if (this.flowData.flowNodeMessage.businessStatus === -1 && uid === this.flowData.flowNodeMessage.createUser) {
                return true;
            }
        }
        return false;
    }

    showApprove() {
        const uid = this.userInfo.uid;
        if ((this.flowData.flowNodeMessage.acceptorList || []).includes(uid)) {
            return true;
        } else {
            return false;
        }
    }
}
