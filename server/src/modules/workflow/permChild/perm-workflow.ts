import { BaseWorkflow, FlowMetaData, FlowNodeMessage, SkipParam } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import { AppConfig } from '@tiger/core';
import { getSecondLevelManagerInfoByOrgId, getThirdLevelManagerInfoByOrgId } from '../../service/permcenter/approvePerm';
import { getCmdbProductManageList } from '../../service/productcenter/productInfo';
import Config from './perm-config';

@Service
export default class Workflow extends BaseWorkflow {
    constructor(config: Config) {
        super(config);
    }
    /**
     * 原来方法固定路由，需要重写
     *
     * @param {string} domain
     * @param {FlowMetaData} flowMetaData
     * @returns {string}
     * @memberof Workflow
     */
    getOrderDetailUrl(domain: string, flowMetaData: FlowMetaData): string {
        if (flowMetaData.topologyName === 'nav_personnelchange_perm' || flowMetaData.topologyName === 'nav_personnelchange_perm_child') {
            return `${domain}${AppConfig.contextPath}/#/workflow/permDetail;flowId=${flowMetaData.flowId};topologyId=${flowMetaData.topologyName}`;
        } else {
            return `${domain}${AppConfig.contextPath}/#/workflow/detail;flowId=${flowMetaData.flowId};topologyId=${flowMetaData.topologyName}`;
        }
    }
    /**
     * @description 获取节点的审批人信息。处理各个节点
     * @param flowMetaData 工单元数据
     * @param flowNodeMessage 工单业务数据
     * @returns string[] 审批人uid列表
     */
    async getApproverList(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<string[]> {
        const currentNodeId = flowMetaData.currentNodeId;
        let approverUsers: string[] = [];
        switch (currentNodeId) {
            // 三级部门负责人 or 代理人审批
            case '30010513':
                approverUsers = await getThirdLevelManagerInfoByOrgId(flowNodeMessage.orgPosId); // 开发环境有点问题，获取不到，暂时写死 TODO
                break;
            // 二级部门负责人审批
            case '30010514':
                approverUsers = await getSecondLevelManagerInfoByOrgId(flowNodeMessage.orgPosId);
                break;
            // 产品归属二级部门负责人审批
            case '30010516':
                approverUsers = await getSecondLevelManagerInfoByOrgId(flowNodeMessage.belongOrgPosId);
                break;
            // 产品经理执行，工单中的cmdb产品productCode获取产品经理信息
            case '30010517':
                approverUsers = await getCmdbProductManageList(flowNodeMessage.applicantProductList[0].cmdbProductCode);
                break;
        }

        return approverUsers.map((approver: any) => {
            return approver.uid;
        });
    }

    /**
     * @description 流程跳转条件, 如果有多条件的跳转，需要用户进行重写实现，处理各个节点的跳转条件设置
     * @param flowMetaData
     * @param flowNodeMessage
     */
    async getCustomSkipParam(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<SkipParam> {
        const currentNodeId = flowMetaData.currentNodeId;
        const orgPosId = flowNodeMessage.orgPosId;
        const level = flowNodeMessage.level;
        const skipParam: any = {
            approved: flowNodeMessage.approved!,
            paramMap: {
                // 是否涉密
                sensitiveLevel: flowNodeMessage.sensitiveLevel,
                // 归属二级部门
                productBelong: !!flowNodeMessage.belongOrgPosId && flowNodeMessage.productBelong,
                underThirdLevel: flowNodeMessage.underThirdLevel
            }
        };

        return skipParam;
    }
}
