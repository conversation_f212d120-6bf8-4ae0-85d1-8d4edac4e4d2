.m-workflow-created {
 .btn {
        margin-left: 10px;
    }

    .section {
        margin-bottom: 10px;
    }

    .m-list-item {
        display: flex;
       font-size: 12px;


        .text-lg {
            font-weight: bold;
            color: #353333;
            font-size: 14px;
        }

        .m-label-color {
            color: #808080;
            font-size: 12px;
        }

        .m-line-height {
           min-height: 27px;
            line-height: 27px;
        }

        .m-operating {
            height: 100%;
            border-left: 1px solid rgb(223, 219, 219);
            text-align: center;
            display: flex;
            align-items: center;
            .btn {
                display: inline-block;
                margin-left: 0;
                padding: 3px;
            }
        }

        .m-left-info {
            height: 100%;
            z-index: 10;
        }

        .u-urged-img {
            width: 60px;
            height: 60px;
            position: absolute;
            left: 60%;
            top: 50%;
            margin-top: -30px;
            z-index: -1;
        }
    }

    .select.select-w-md {
        width: 100px;
    }
    
    /deep/ {
        .datetimepicker-range.datetimepicker-range-w-lg .datetimepicker-start, 
        .datetimepicker-range.datetimepicker-range-w-lg .datetimepicker-end{
            width: 43%;
        }
    }

    .text-width {
        max-width: 906px;
    }
    .tootip{
        display: inline-block;
        position: relative;
        top: 3px;
        margin-left: -14px;
        .iconRecord{
            color: #3187e6;
        }
    }
}