import axios from '@tiger/request';
import querystring from 'querystring';
import { cmdbDomain, iusDomain, productCode } from '../../../conf';

// 获取产品详情信息 模糊搜索
export async function fuzzyQueryFullInfo(param: any) {
    const queryStr = querystring.stringify(param);
    const rst = await axios.get(`${iusDomain}/${productCode}/pdct/fuzzyQueryFullInfo.json?${queryStr}`);
    if (rst.data.code !== 200) {
        throw new Error(`查询产品列表失败: ${rst.data.errorCode}`);
    }
    return rst.data.data.result || [];
}

// 获取产品详情信息 准确搜索
export async function getByProductId(param: any) {
    const rst = await axios.get(`${iusDomain}/${productCode}/pdct/getByProductId.json?productId=${param.productId}`);
    if (rst.data.code !== 200) {
        throw new Error(`查询产品列表失败: ${rst.data.errorCode}`);
    }
    return rst.data.data;
}
// 返回所有的管理员账号
export async function getAllProductManager(param: any = {}): Promise<any[]> {
    param.systemType = 1;
    param.page = 1;
    param.size = -1;
    const productList = await fuzzyQueryFullInfo(param);
    let managerList: string[] = [];
    for (const product of productList) {
        const magagerUidList = (product.productManager || []).map((manager: any) => {
            return manager.uid;
        });
        managerList = managerList.concat(magagerUidList);
    }
    const managerSet = new Set(managerList);
    return [...managerSet];
}
// 精确查询返回所有的管理员账号
export async function getAllProductManagerExact(param: any = {}): Promise<any[]> {
    param.systemType = 1;
    param.page = 1;
    param.size = -1;
    const productList = await fuzzyQueryFullInfo(param);
    let managerList: string[] = [];
    // 获取模糊查询第一个符合条件的产品信息
    if (productList[0].productManager) {
        managerList = productList[0].productManager;
    }

    const managerSet = new Set(managerList);
    return [...managerSet];
}
// 批量获取产品信息，并发给后端服务查询，返回[]
export async function getProductInfo(productIdList: any[]): Promise<any[]> {
    const promiseFn: object[] = [];
    let productInfo: any[] = [];
    productIdList.forEach((element, index) => {
        promiseFn.push(getPromiseFn(getByProductId({
            productId: element.productId
        })));
    });
    await Promise.all(promiseFn).then((values) => {
        productInfo = values;
    });
    return productInfo;
}

// 返回promise 执行函数
export function getPromiseFn(fn: Promise<any>) {
    return fn;
}

// 获取cmdb产品的产品经理
export async function getCmdbProductManageList(prodCode: string) {
    // 这里返回的data是未登录 要确认下
    const rst = await axios.get(`${cmdbDomain}/api/v2/product/${prodCode}/user`);
    if (rst.data.code !== 200) {
        throw new Error(`查询产品列表失败: ${rst.data.errorCode}`);
    }
    const managerList: any[] = [];
    // 过滤调非产品经理角色账户
    rst.data.data = rst.data.data.filter((element: any) => element.role === 1);
    rst.data.data.forEach((element: any) => {
        managerList.push({
            uid: element.uid,
            realName: element.cname
        });
    });

    return managerList;
}
