<div class="modal-header">
    <a class="modal-close" (click)="dismiss()"></a>
    <span class="modal-title">{{modalTitle}}</span>
</div>
<div class="modal-body m-permDetailModal">
    <div>
        <ng-container  *ngIf="tabpanes.length === 0">
            <shark-tabset [type]="'line'" [(active)]="activekey">
                <div *ngFor="let item of tabpanes1;let index = index;">
                    <shark-tabpanel>
                        <ng-template sharkTabTitle>
                            {{item.name}}
                        </ng-template>
                    </shark-tabpanel>
                </div>
            </shark-tabset>
        </ng-container>
        <ng-container  *ngIf="tabpanes.length > 0">
            <div>
                <shark-tabset [type]="'line'" [(active)]="activekey" class="m-tabset">
                    <div *ngFor="let item of tabpanes;let index = index;">
                        <shark-tabpanel>
                            <ng-template sharkTabTitle>
                                {{item.roleName}}
                            </ng-template>
                            <div class="section-block m-tabsetAll-tlt" *ngIf="index === 0">{{title}}</div>
                            <div class="section-block">
                                <shark-stree [(ngModel)]="model" [data]="item.treeData"></shark-stree>
                            </div>
                        </shark-tabpanel>
                    </div>
                </shark-tabset>
            </div>
        </ng-container>
    </div>
</div>