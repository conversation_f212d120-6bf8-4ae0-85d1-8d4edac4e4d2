import { GetMapping, PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, FileContext, QueryContext } from '@tiger/core';
import { PassThrough, Stream } from 'stream';
import { isNullOrUndefined } from 'util';
import { IFileDownloadOptionVO } from './vo/fms-download.vo';
import { IFileUploadVO } from './vo/fms-upload.vo';
import {contextPath} from '../../conf';
// import FmsService from './fms.service';
import {IFileInfo, IFileUploadOpt} from './vo/fms';
import {FmsService} from '@eagle/common-service-node';

/**
 * 文件FMS 服务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(`${contextPath}/xhr/fms`)
export class FmsController {

    constructor(private fmsService: FmsService) {
    }

    /**
     * @description 上传单个个文件
     *
     * @param {FileContext<null, AjaxResult<string>, string[]>} ctx
     */
    @PostMapping('/upload.do')
    public async uploadFile(ctx: FileContext<IFileUploadVO, AjaxResult<any>, ['file']>) {
        const files = ctx.request.files;
        if (!files) {
            return ctx.body = AjaxResult.badRequest('没有传文件');
        }

        if (!ctx.request.body) {
            return ctx.body = AjaxResult.badRequest('缺少参数');
        }

        const topic = ctx.request.body.topic;

        const file = files.file;

        const name = file.name;

        const param: IFileUploadOpt = { file, topic };

        // 调用文件上传服务
        const uploadResult: AjaxResult<IFileInfo> = await this.fmsService.uploadFile(param);

        const key = uploadResult.data[0];
        // const key = 'testtesttest';

        ctx.body = AjaxResult.success({ key, name });
    }

    /**
     * @description 下载文件
     *
     * @param {QueryContext<IFileDownloadOptionVO, Stream | AjaxResult<string>>} ctx
     */
    @GetMapping('/downLoadFile.do')
    public async downLoadFile(ctx: QueryContext<IFileDownloadOptionVO, Stream | AjaxResult<string>>) {
        if (!ctx.query) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const fileKey = ctx.query.key;
        const topic = ctx.query.topic;
        const name = ctx.query.name;
        const rst = await this.fmsService.downLoadFile({ topic, fileKey });
        console.info('文件下载', { topic, fileKey });

        ctx.response.set(
            'Content-Type', 'application/octet-stream;charset=utf-8'
        );

        ctx.response.set(
            'Content-Disposition', `attachment; fileName=${isNullOrUndefined(name) || name === '' ? fileKey : encodeURI(name)}` // 指定下载的文件名，开发时可提供fileName参数
        );

        ctx.body = rst.on('error', ctx.onerror).pipe(new PassThrough());
    }

}
