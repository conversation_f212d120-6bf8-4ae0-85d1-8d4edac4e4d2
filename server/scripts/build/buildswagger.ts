import { AppConfig } from '@tiger/core';
import { swaggerDoc } from '@tiger/swagger';
import { mkdirSync, writeFileSync } from 'fs';
import { join } from 'path';
import BaseConfig from '../../src/conf/config.base';

AppConfig.init<BaseConfig>({
    configPath: join(__dirname, '../../src/conf')
});

const swaggerDocs = swaggerDoc({
    appModule: join(__dirname, '../../src/modules/index.ts')
});

const outputPath = join(__dirname, '../../build/src/swagger');
mkdirSync(outputPath);
writeFileSync(
    join(__dirname, '../../build/src/swagger/swagger.json'),
    JSON.stringify(swaggerDocs),
    { encoding: 'utf-8' }
);
