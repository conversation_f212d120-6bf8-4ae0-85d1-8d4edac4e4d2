.m-workflow-dealing {
    .section {
        margin-bottom: 10px;
    }
    .m-btn-primary{
        border-radius: 4px;
        background: #1890ff;
        color: white;
        transition: all 0.5s;
        border: 1px solid #1890ff;
        &:hover{
            background: #40a9ff;
            border-color: #40a9ff;

        }
    }
    .m-btn-ghost{
        border-radius: 4px;
        background: white;
        transition: all 0.5s;
        border: 1px solid #d9d9d9;
        color: rgba(0,0,0,.85);
        &:hover{
            color: #40a9ff;
            border-color: #40a9ff;
        }
    }
    .m-batch-operation{
        height: 60px;
        padding: 0 30px 0 5px;
        background: white;
        position: relative;
        border-bottom: 1px solid #dfdbdb;
        display: flex;
        align-items: center;
        .m-all-check {
            position: absolute;
            left: -22px;
            cursor: pointer;
        }
        .m-allcheck {
            cursor: pointer;
        }
        .m-batch-btns{
            position: absolute;
            right: 30px;

            button {
                border-radius: 4px;
                padding: 6px 16px;
                margin: 10px;
                cursor: pointer;
            }
        }
        // button{
        //     position: absolute;
        //     right: 0;
        // }
    }
    .m-tag-box{
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        padding: 8px 0;

        .m-tag-row {
            margin: 5px 0;

            .btn {
                margin: 4px;
                padding: 3px;
            }
        }
        // &>div{
        //     height: 35px;
        //     display: flex;
        //     align-items: center;
        // }
    }
    .m-tag{
        padding: 6px 16px;
        margin-left: 10px;
        cursor: pointer;
    }
    .m-list-content{
        position: relative;
        font-size: 12px;

    
        .m-item-check{
            position: absolute;
            top:10px;
            left: -22px;
            z-index: 10;
        }

        .m-uegr-img {
            width: 33px;
            height: 30px;
            margin-left: 7px;
            margin-top: -11px;
        }
    }
  
 
     .m-list-item {
         display: flex;
 
         .text-lg {
             font-weight: bold;
             cursor: pointer;
             color: #353333;
             font-size: 14px;
         }

         .m-label-color {
            color: #808080;
            font-size: 12px;
        }
        
         .m-line-height {
             min-height: 31px;
             line-height: 31px;
         }
 
         .m-operating {
             height: 100%;
             border-left: 1px solid rgb(223, 219, 219);
             text-align: center;
             display: flex;
             align-items: center;
             .btn {
                 display: inline-block;
                 margin-left: 0;
             }
         }
     }
 
     .select.select-w-md {
         width: 100px;
     }
     .text-width {
        max-width: 906px;
    }
 }

 .m-reset-width {
     margin-left: 20px;
 }

/* 移动设备适配样式 */
@media screen and (max-width: 980px) {
    .m-workflow-dealing {
        .m-reset-width {
            margin-left: 0;
        }

        .form-inline {
            .form-item {
                margin-bottom: 10px;
            }
        }

        .m-batch-operation {
            padding: 0 10px;
            flex-direction: column;
            height: auto;
            min-height: 60px;
            
            .m-batch-btns {
                position: relative;
                right: auto;
                margin-top: 10px;
                margin-bottom: 10px;
            }
        }

        .m-list-content {
            .m-list-item {
                flex-wrap: wrap;
                overflow: hidden;
                .u-mobile-tag{
                    width: 100% !important;
                    padding:0 !important;
                }
                
                .m-operating {
                    border-left: none;
                    border-top: 1px solid rgb(223, 219, 219);
                    margin-top: 10px;
                    padding-top: 10px;
                    width: 100%;
                    display: block;
                    height: 44px;
                }
            }
        }

        .m-tag-row {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            
            span {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
            
            .btn {
                margin: 2px 4px;
            }
        }
        .text-width{
            text-overflow:inherit;
            white-space:inherit;
        }
    }
}
