import { SharkToastrService } from '@shark/shark-angularX';
/*
@param target
    Class.prototype
@param name
    function name
@param descriptor
    {
        value: [Function: name],
        writable: true,
        enumerable: false,
        configurable: true
*/
export function OnAjaxError(msg = '请求失败!') {
    return function(target, name, descriptor) {
        const fun = descriptor.value;
        descriptor.value = async function(...args) {
            let error;
            try {
                return await Reflect.apply(fun, this, args);
            } catch (e) {
                error = e.originError ? e.originError.error : e;
            } finally {
                if (error) {
                    const toast = new SharkToastrService();
                    const errMsg = error.msg ? error.msg : (error.message ? error.message : msg);
                    toast.error(errMsg);
                    // throw error;
                }
            }
        };
        return descriptor;
    };
}
