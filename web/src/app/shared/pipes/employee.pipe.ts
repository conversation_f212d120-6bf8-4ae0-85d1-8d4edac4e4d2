import { Pipe, PipeTransform } from '@angular/core';
import { BasePipe } from './base.pipe';

const EmployeeType = {
    0: '正式员工',
    1: '正式员工-试用期',
    2: '外包',
    3: '派遣',
    4: '实习生-签三方',
    5: '实习生-未签三方'
};
@Pipe({
    name: 'employeeTypePipe'
})
export class EmployeeTypePipe extends BasePipe implements PipeTransform {
    constructor() {
        super();
    }

    transform(key?, isList = false) {
        return this.transfromData(EmployeeType, 'number', key, isList);
    }
}

const EmployeeRoleType = {
    0: '普通职员',
    1: '指导员',
    2: '负责人'
};
// tslint:disable-next-line:max-classes-per-file
@Pipe({
    name: 'employeeRoleTypePipe'
})
export class EmployeeRoleTypePipe extends BasePipe implements PipeTransform {
    constructor() {
        super();
    }

    transform(key, isList = false) {
        return this.transfromData(EmployeeRoleType, 'number', key, isList);
    }
}

const transferTypes = {
    0: '职责未变转岗(系统不对权限做删减或变更)',
    1: '职责变更转岗(系统自动收回原岗位涉及全部权限，新岗位需员工自行申请权限)'
};
// tslint:disable-next-line:max-classes-per-file
@Pipe({
    name: 'transferType'
})
export class TransferTypePipe extends BasePipe implements PipeTransform {
    constructor() {
        super();
    }

    transform(key, isList = false) {
        return this.transfromData(transferTypes, 'number', key, isList);
    }
}
