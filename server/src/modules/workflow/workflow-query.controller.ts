import { FlowData, IPageSearch, OpLogPo, TopoGraph } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, AjaxSearchResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import { contextPath } from '../../conf';
import WorkflowQuery from './workflow-query';
import PermissionService from '../permission/permission.service';
import { IUserPerms } from '@tiger/permission';
import { app } from '@tiger/boot/publish/spec/lib/src';

interface QueryDetail {
    flowId: string;
    topologyId?: string;
    subList?: string[];
}
interface QuerySubDetail {
    sublist: string;
    topologyId: string;
}

interface QueryGraph {
    topologyId: string;
}

export interface AllFlowData {
    flowData: FlowData | null;
    bpmData: TopoGraph;
    oplogs: OpLogPo[] | null;
}

interface ExportObject {
    序号?: any;
    文档编号?: any;
    文档名称?: any;
    文档密级?: any;
    文档类型?: any;
    所属部门?: any;
    拟制人?: any;
    审核人?: any;
    工单状态?: any;
    当前节点?: any;
    拟制日期?: any;
}


/**
 * 工单查询
 */
@RestController
@RequestMapping(`${contextPath}/xhr/bpmFlow`)
export default class WorkflowQueryController {

    constructor(private query: WorkflowQuery,
        private permission: PermissionService) {
    }

    /**
     * 导出文档列表，分页
     */
    @PostMapping('/exportDocumentList.json')
    async exportDocument(ctx: RequestContext<IPageSearch, AjaxResult<ExportObject[]>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const queryParam = Object.assign({
            page: 1,
            size: 2000,
            topologyId: 'written_doc_project',
            sortBy: ['-updateTime']
        }, ctx.request.body);
        const userInfo = ctx.openIDInfo;
        try {
            let list;
            const userPerms: IUserPerms | null = await this.permission.getUserPerms(userInfo.email);
            let permIds: any[] = [];
            if (userPerms !== null) {
                permIds = userPerms.perms.map(perm => {
                    return perm.permId
                });
            }
            if (permIds.includes(300104) || permIds.includes(300103)) {
                // 如果是管理员
                list = await this.query.search(queryParam);
            } else {
                list = await this.query.searchByAudited(userInfo.email, queryParam);
            }
            const exportList: ExportObject[] = [];
            if (list.result.length === 0) {
                const exportObject = {
                    序号: '',
                    文档编号: '',
                    文档名称: '',
                    文档密级: '',
                    文档类型: '',
                    所属部门: '',
                    创建人: '',
                    审批人: '',
                    工单状态: '',
                    当前节点: '',
                    创建时间: ''
                };
                exportList.push(exportObject);
            } else {
                let xh = 1;
                list.result.forEach((flowData) => {
                    if (!isNullOrUndefined(flowData.flowNodeMessage.content)) {
                        const approvers: string[] = flowData.flowNodeMessage.content.approvers;
                        const approver = (isNullOrUndefined(approvers) ||
                            approvers.constructor !== Array ||
                            approvers.length === 0) ? '/' : approvers.join(",");
                        const exportObject = {
                            序号: xh,
                            文档编号: flowData.flowNodeMessage.content.number,
                            文档名称: flowData.flowNodeMessage.content.name,
                            文档密级: this.query.confidentialityToName(flowData.flowNodeMessage.content.confidentiality),
                            文档类型: isNullOrUndefined(flowData.flowNodeMessage.content.categoryList) ?
                                '/' : flowData.flowNodeMessage.content.categoryList[0],
                            所属部门: flowData.flowNodeMessage.content.department,
                            创建人: flowData.flowNodeMessage.createUserName,
                            审批人: approver,
                            工单状态: flowData.flowNodeMessage.businessStatus === 0 ? '处理中' : '完结',
                            当前节点: flowData.flowNodeMessage.businessStatus === 0 ? this.query.nodeIdtoName(flowData.flowMetaData.currentNodeId) : '/',
                            创建时间: this.query.toTime(flowData.flowMetaData.createTime)
                        };
                        exportList.push(exportObject);
                        xh++;
                    }
                });
            }

            ctx.body = AjaxResult.success(exportList);
        } catch (e) {
            console.error('查询文档列表，失败:', e);
            ctx.body = AjaxResult.internal('查询文档列表');
        }
    }

    /**
     * 查询创建的工单列表，分页
     */
    @PostMapping('/queryDocumentList.json')
    async queryDocumentList(ctx: RequestContext<IPageSearch, AjaxSearchResult<FlowData>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const queryParam = Object.assign({
            topologyId: 'written_doc_project',
            sortBy: ['-updateTime']
        }, ctx.request.body);
        const userInfo = ctx.openIDInfo;
        try {
            let list;
            const userInfo = ctx.openIDInfo;
            const userPerms: IUserPerms | null = await this.permission.getUserPerms(userInfo.email);
            let permIds: any[] = [];
            if (userPerms !== null) {
                permIds = userPerms.perms.map(perm => {
                    return perm.permId
                });
            }
            if (permIds.includes(300104) || permIds.includes(300103)) {
                // 如果是管理员
                list = await this.query.search(queryParam);
            } else {
                list = await this.query.searchByAudited(userInfo.email, queryParam);
            }

            ctx.body = AjaxResult.success(list);
        } catch (e) {
            console.error('查询文档列表，失败:', e);
            ctx.body = AjaxResult.internal('查询文档列表');
        }
    }

    /**
     * 查询工单详情
     */
    @PostMapping('/queryFlowDetail.json')
    async queryDetail(ctx: RequestContext<QueryDetail, AjaxResult<FlowData>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const userInfo = ctx.openIDInfo;
        const { flowId } = ctx.request.body;
        try {
            const rst = await this.query.detailQuery(flowId, '', userInfo.email);

            const observerList = rst.flowNodeMessage.observerList;
            if (userInfo.email !== '<EMAIL>') {
                if (observerList && !observerList.includes(userInfo.email)) {
                    // 如果是成文工单
                    if (rst.flowMetaData.topologyId === 'written_doc_project') {
                        if (!ctx.session.permission.permExps.includes('document') &&
                            !ctx.session.permission.permExps.includes('monitor-flows')) {
                            return ctx.body = AjaxResult.forbidden('权限不够');
                        }
                    } else if (!ctx.session.permission.permExps.includes('monitor-flows')) {
                        return ctx.body = AjaxResult.forbidden('权限不够');
                    }
                }
            }
            ctx.body = AjaxResult.success(rst);
        } catch (e) {
            console.error(`查询工单详情失败:queryFlowDetail`, flowId, e);
            ctx.body = AjaxResult.internal('查询工单详情失败');
        }
    }
    /**
     * 返回所有信息，包含拓扑图，flowData，oplog
     * @param ctx
     */
    @PostMapping('/get.json')
    async queryAllDetail(ctx: RequestContext<any, AjaxResult<any>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const userInfo = ctx.openIDInfo;
        const { flowId, topologyId } = ctx.request.body;
        try {
            const rst = await this.getAll(topologyId, flowId);

            ctx.body = AjaxResult.success(rst);
        } catch (e) {
            console.error(`查询工单详情失败:get`, flowId, e);
            ctx.body = AjaxResult.internal('查询工单详情失败');
        }
    }
    /**
     * 返回所有子工单信息
     * @param ctx
     */
    @PostMapping('/getSub.json')
    async querySubAllDetail(ctx: RequestContext<QuerySubDetail, AjaxResult<any>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const userInfo = ctx.openIDInfo;
        const { topologyId, sublist } = ctx.request.body;
        const subListArray = sublist.split(',');

        try {
            await Promise.all(subListArray.map((element) => this.getAll(topologyId, element))).then((rst) => {

                ctx.body = AjaxResult.success(rst);
            });
        } catch (e) {
            console.error(`查询工单详情失败getsub:`, sublist, e);
            ctx.body = AjaxResult.internal('查询工单详情失败');
        }
    }
    /**
     * 查询工单历史
     */
    @PostMapping('/queryHistory.json')
    async queryHistory(ctx: RequestContext<QueryDetail, AjaxResult<OpLogPo[]>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const userInfo = ctx.openIDInfo;
        const { flowId } = ctx.request.body;
        try {
            const rst = await this.query.detailQuery(flowId, '', userInfo.email);

            const observerList = rst.flowNodeMessage.observerList;
            if (observerList && !observerList.includes(userInfo.email)) {
                if (rst.flowMetaData.topologyId === 'written_doc_project') {
                    if (!ctx.session.permission.permExps.includes('document') &&
                        !ctx.session.permission.permExps.includes('monitor-flows')) {
                        return ctx.body = AjaxResult.forbidden('权限不够');
                    }
                } else if (!ctx.session.permission.permExps.includes('monitor-flows')) {
                    return ctx.body = AjaxResult.forbidden('权限不够');
                }
            }
            const history = await this.query.historyQuery(flowId);

            ctx.body = AjaxResult.success(history);
        } catch (e) {
            console.error('查询工单操作历史失败:', e);
            ctx.body = AjaxResult.internal('查询工单操作历史失败');
        }
    }

    /**
     * 查询拓扑图
     */
    @PostMapping('/queryFlowMap.json')
    async queryFlowMap(ctx: RequestContext<QueryGraph, AjaxResult<TopoGraph>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { topologyId } = ctx.request.body;
        try {
            const graph = await this.query.getBpmFlowMap(topologyId);

            ctx.body = AjaxResult.success(graph);
        } catch (e) {
            console.error('查询拓扑图失败:', e);
            ctx.body = AjaxResult.internal('查询拓扑图失败');
        }
    }

    /**
     * 查询创建的工单列表，分页
     */
    @PostMapping('/queryCreatedList.json')
    async queryCreatedList(ctx: RequestContext<IPageSearch, AjaxSearchResult<FlowData>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const queryParam = Object.assign({
            sortBy: ['-updateTime']
        }, ctx.request.body);

        const userInfo = ctx.openIDInfo;
        try {
            const list = await this.query.searchByCreator(userInfo.email, queryParam);

            ctx.body = AjaxResult.success(list);
        } catch (e) {
            console.error('查询创建的工单列表，失败:', e);
            ctx.body = AjaxResult.internal('查询创建的工单列表失败');
        }
    }

    /**
     * 查询待我审核的工单列表，分页
     */
    @PostMapping('/queryDealingList.json')
    async queryDealingList(ctx: RequestContext<IPageSearch, AjaxSearchResult<FlowData>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const queryParam = Object.assign({
            sortBy: ['-urgeState', '-updateTime']
        }, ctx.request.body);
        const userInfo = ctx.openIDInfo;
        try {
            const list = await this.query.searchByAuditing(userInfo.email, queryParam);
            ctx.body = AjaxResult.success(list);
        } catch (e) {
            console.error('查询创建的工单列表，失败:', e);
            ctx.body = AjaxResult.internal('查询创建的工单列表失败');
        }
    }

    /**
     * 查询我处理过的工单列表，分页
     */
    @PostMapping('/queryDealedList.json')
    async queryDealedList(ctx: RequestContext<IPageSearch, AjaxSearchResult<FlowData>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const queryParam = Object.assign({
            sortBy: ['-updateTime']
        }, ctx.request.body);
        const userInfo = ctx.openIDInfo;
        try {
            const list = await this.query.searchByAudited(userInfo.email, queryParam);

            ctx.body = AjaxResult.success(list);
        } catch (e) {
            console.error('查询创建的工单列表，失败:', e);
            ctx.body = AjaxResult.internal('查询创建的工单列表失败');
        }
    }

    /**
     * 查询我跟踪的工单列表，分页
     */
    @PostMapping('/queryTrailedList.json')
    async queryTrailedList(ctx: RequestContext<IPageSearch, AjaxSearchResult<FlowData>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const queryParam = Object.assign({
            sortBy: ['-updateTime']
        }, ctx.request.body);
        const userInfo = ctx.openIDInfo;
        try {
            const list = await this.query.searchByTrailed(userInfo.email, queryParam);

            ctx.body = AjaxResult.success(list);
        } catch (e) {
            console.error('查询创建的工单列表，失败:', e);
            ctx.body = AjaxResult.internal('查询创建的工单列表失败');
        }
    }

    /**
     * 查询我监控的工单列表，分页
     */
    @PostMapping('/queryMonitorList.json')
    async queryMonitorList(ctx: RequestContext<IPageSearch, AjaxSearchResult<FlowData>>) {
        if (ctx.session.permission.permExps.includes('monitor-flows')) {
            if (isNullOrUndefined(ctx.request.body)) {
                return ctx.body = AjaxResult.badRequest('参数错误');
            }
            const queryParam = Object.assign({
                sortBy: ['-updateTime']
            }, ctx.request.body);
            try {
                const list = await this.query.search(queryParam);

                ctx.body = AjaxResult.success(list);
            } catch (e) {
                console.error('查询监控的工单列表，失败:', e);
                ctx.body = AjaxResult.internal('查询监控的工单列表失败');
            }
        } else {
            ctx.body = AjaxResult.forbidden('对不起，您没有访问权限！');
        }
    }

    /**
     *
     */
    //  @PostMapping('/msgQuery/count')
    //  async queryFlowCount(ctx: RequestContext<IPageSearch, AjaxResult<number>>) {
    //      if (isNullOrUndefined(ctx.request.body)) {
    //          return ctx.body = AjaxResult.badRequest('参数错误');
    //      }
    //      const userInfo = ctx.openIDInfo;
    //      try {
    //          const list = await this.query.count(ctx.request.body);
    //          ctx.body = AjaxResult.success(list);
    //      } catch (e) {
    //          console.error('查询创建的工单列表，失败:', e);
    //          ctx.body = AjaxResult.internal('查询创建的工单列表失败');
    //      }
    //  }

    /**
     * getAll处理逻辑有问题，重写
     *
     */
    async getAll(topologyId: string, flowId?: string | undefined): Promise<any> {
        const flowData = (flowId === 'undefined' || !flowId) ? { flowMetaData: {}, flowNodeMessage: {} } : await this.query.detailQuery(flowId || '', '');
        const oplogs = (flowId === 'undefined' || !flowId) ? {} : await this.query.historyQuery(flowId || '');
        const bpmData = await this.query.getBpmFlowMap(topologyId);
        return {
            flowData,
            bpmData,
            oplogs
        };
    }

}
