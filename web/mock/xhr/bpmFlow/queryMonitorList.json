{"code": 200, "data": {"pagination": {"page": 1, "size": 10, "total": 3247, "totalPage": 325, "offset": 0}, "result": [{"flowMetaData": {"product": "ius", "createTime": 1599638875255, "globalId": "IUS_SPECIAL_1599638874596", "topologyId": "ius_special_rights", "updateTime": 1599638949542, "nodeId": "30110102", "flowId": "62566958", "version": 3, "latest": true, "status": 200, "topologyName": "ius_special_rights", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "yx-fuxi", "applySystemName": "伏羲-营销数据运营平台", "applySystemDesc": "流量-栏目分析"}, "businessStatus": 1, "createUserName": "陈桂深", "haveChildFlow": false, "currUserName": "高文举", "businessObject": {"multiApplySystem": [{"applySystem": "yx-fuxi", "applySystemName": "伏羲-营销数据运营平台", "applySystemDesc": "流量-栏目分析"}], "allowApplySystem": [{"applySystem": "yx-fuxi", "applySystemName": "伏羲-营销数据运营平台", "applySystemDesc": "流量-栏目分析"}]}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": ["刘燕"], "acceptorList": ["<EMAIL>"], "workOrderId": "IUS_SPECIAL_1599638874596", "nodeId": "30110103", "flowId": "62566958", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "written-doc", "createTime": 1599638851679, "nextNodeList": ["70210102"], "globalId": "written-doc_1599638851049", "topologyId": "written_doc_project", "updateTime": 1599638853664, "nodeId": "70210101", "flowId": "62568930", "version": 1, "latest": true, "status": 100, "topologyName": "written_doc_project", "currentNodeId": "70210102"}, "flowNodeMessage": {"reachLogList": [{"nodeId": "70210102", "user": "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "reachTime": 1599638853424}], "createUserName": "甘甜", "businessStatus": 0, "updateTime": 1599638853645, "content": {"confidentiality": 5, "departmentId": 937, "stamp": 57, "approvers": ["李丹"], "effectEndTime": 0, "responsibleDepartmentList": ["食品宠物组", "商务组", "商品管理部", "采购部", "供应链管理部"], "countersignList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "version": "V1.0", "number": "YX-FS-530", "creatorUId": "<EMAIL>", "createTime": 1599638849937, "categoryList": ["流程类", "L6任务", "L5活动"], "name": "供应商违约处理方案审批表", "createUser": "甘甜", "responsibleDepartmentIdList": [884, 1034, 870, 893, 898], "department": "严选事业部>供应链中心>质量部>质量管理及控制组", "noticeUIdList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "mainDepartment": "质量部", "categoryId": 57, "fileId": 84, "mainDepartmentId": 895}, "observerList": ["<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com"], "createUserManager": "<EMAIL>", "createUserManagerName": "王燕", "trackList": ["<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com"], "createTime": 1599638851049, "acceptorNameList": ["张晓红"], "createUser": "<EMAIL>", "acceptorList": ["z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com"], "createUserOrg": "严选事业部 > 供应链中心 > 质量部"}}, {"flowMetaData": {"product": "ius", "createTime": 1599638322728, "globalId": "IUS_SPECIAL_1599638206720", "topologyId": "ius_special_rights_child", "updateTime": 1599638485881, "nodeId": "30110201", "flowId": "62566260", "version": 3, "latest": true, "status": 200, "topologyName": "ius_special_rights_child", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, "businessStatus": 2, "createUserName": "胡尧", "haveChildFlow": false, "currUserName": "王亚君", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "applyRoleList": [], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}], "notifyedList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "acceptorNameList": [], "acceptorList": [], "roleList": [], "delRoleList": [{"expireTime": 0, "roleId": 100, "roleName": "研发人员"}]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": [], "acceptorList": [], "workOrderId": "CHILD_IUS_SPECIAL_1599638206720_490", "nodeId": "9999", "flowId": "62566260", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599638207158, "globalId": "IUS_SPECIAL_1599638206720", "topologyId": "ius_special_rights", "updateTime": 1599638485037, "nodeId": "30110104", "flowId": "62568084", "version": 6, "latest": true, "status": 200, "topologyName": "ius_special_rights", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, "businessStatus": 2, "createUserName": "胡尧", "haveChildFlow": true, "currUserName": "邱晟", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "applyRoleList": [], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}], "notifyedList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "acceptorNameList": [], "acceptorList": [], "roleList": [], "delRoleList": [{"expireTime": 0, "roleId": 100, "roleName": "研发人员"}]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": [], "acceptorList": [], "workOrderId": "IUS_SPECIAL_1599638206720", "nodeId": "9999", "flowId": "62568084", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599637672755, "globalId": "IUS_SPECIAL_1599637672238", "topologyId": "ius_special_rights", "updateTime": 1599638361784, "nodeId": "30110103", "flowId": "62567577", "version": 8, "latest": true, "status": 200, "topologyName": "ius_special_rights", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "document", "applySystemName": "严选文档部署平台", "applySystemDesc": "稳定性平台交互搞维护"}, "businessStatus": 2, "createUserName": "刘洋", "haveChildFlow": false, "currUserName": "张浩", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "applyRoleList": [{"expireTime": 0, "roleId": 12, "roleName": "普通用户"}], "multiApplySystem": [{"applySystem": "document", "applySystemName": "严选文档部署平台", "applySystemDesc": "稳定性平台交互搞维护"}], "allowApplySystem": [{"applySystem": "document", "applySystemName": "严选文档部署平台", "applySystemDesc": "稳定性平台交互搞维护"}], "notifyedList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "thirdOwnerTrackList": ["<EMAIL>"], "acceptorNameList": [], "acceptorList": [], "roleList": [{"expireTime": 0, "roleId": 12, "roleName": "普通用户"}], "delRoleList": []}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": [], "acceptorList": [], "workOrderId": "IUS_SPECIAL_1599637672238", "nodeId": "9999", "flowId": "62567577", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599638322728, "globalId": "IUS_SPECIAL_1599638206720", "topologyId": "ius_special_rights_child", "updateTime": 1599638334966, "nodeId": "30110201", "flowId": "62566254", "version": 3, "latest": true, "status": 200, "topologyName": "ius_special_rights_child", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, "businessStatus": 2, "createUserName": "胡尧", "haveChildFlow": false, "currUserName": "邱晟", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "applyRoleList": [], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}], "allowApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}, {"applySystem": "icac", "applySystemName": "权限中心", "applySystemDesc": "邱总，不好意思，想再测一单，麻烦第一步（部门负责人）通过下"}], "notifyedList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "acceptorNameList": [], "acceptorList": [], "roleList": [{"expireTime": 0, "roleId": 2, "roleName": "超级管理员"}, {"expireTime": 0, "roleId": 8, "roleName": "中台架构师"}, {"expireTime": 0, "roleId": 9, "roleName": "普通用户"}], "delRoleList": []}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": [], "acceptorList": [], "workOrderId": "CHILD_IUS_SPECIAL_1599638206720_9358", "nodeId": "9999", "flowId": "62566254", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599638132542, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1599638132085", "topologyId": "ius_special_rights", "updateTime": 1599638140466, "nodeId": "30110101", "flowId": "62568011", "version": 2, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "麦琳", "haveChildFlow": false, "currUserName": "麦琳", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "yanxuan-pub", "applySystemName": "页面搭建系统", "applySystemDesc": "开通福礼商城业务域页面搭建权限"}], "notifyedList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["宾峥"], "acceptorList": ["<EMAIL>"], "workOrderId": "IUS_SPECIAL_1599638132085", "nodeId": "30110102", "flowId": "62568011", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599637337007, "globalId": "IUS_SPECIAL_1599637336565", "topologyId": "ius_special_rights", "updateTime": 1599637744798, "nodeId": "30110103", "flowId": "62562505", "version": 6, "latest": true, "status": 200, "topologyName": "ius_special_rights", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "currApplySystem": {"applySystem": "ops", "applySystemName": "严选运营工作台系统", "applySystemDesc": "福礼微商城活动页面搭建"}, "businessStatus": 2, "createUserName": "蔡中春", "haveChildFlow": false, "currUserName": "应柏璐", "businessObject": {"applyRoleList": [{"expireTime": 0, "roleId": 204, "roleName": "页面搭建—福礼商城"}], "multiApplySystem": [{"applySystem": "ops", "applySystemName": "严选运营工作台系统", "applySystemDesc": "福礼微商城活动页面搭建"}], "allowApplySystem": [{"applySystem": "ops", "applySystemName": "严选运营工作台系统", "applySystemDesc": "福礼微商城活动页面搭建"}], "acceptorNameList": [], "acceptorList": [], "roleList": [{"expireTime": 0, "roleId": 50, "roleName": "渠道运营-最低折扣表"}, {"expireTime": 0, "roleId": 55, "roleName": "活动计划日历查看"}, {"expireTime": 0, "roleId": 88, "roleName": "选品系统"}, {"expireTime": 0, "roleId": 204, "roleName": "页面搭建—福礼商城"}], "delRoleList": []}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": [], "acceptorList": [], "workOrderId": "IUS_SPECIAL_1599637336565", "nodeId": "9999", "flowId": "62562505", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1599619296530, "nextNodeList": [], "globalId": "PSR_1599619295972", "topologyId": "ius_personal_special_rights", "updateTime": 1599637029878, "nodeId": "30110103", "flowId": "62517246", "version": 5, "latest": true, "status": 200, "topologyName": "ius_personal_special_rights", "currentNodeId": "9999"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 2, "createUserName": "魏明", "currUserName": "张庆", "businessObject": {"applySystem": "ops", "roleList": [], "applyDesc": "申请测试环境权限,促销测试使用"}, "operateResult": "agree", "trackList": ["<EMAIL>", "z<PERSON><PERSON><PERSON><PERSON>@corp.netease.com", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "已开通测试服权限", "nodeId": "30110103", "flowId": "62517246", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "navAdmin", "createTime": 1599630933342, "nextNodeList": [], "globalId": "navAdmin_1599630932931", "topologyId": "nav_personnelchange_onboard", "updateTime": 1599636901007, "nodeId": "30010303", "flowId": "62547709", "version": 6, "latest": true, "status": 200, "topologyName": "nav_personnelchange_onboard", "currentNodeId": "9999"}, "flowNodeMessage": {"applerPhoneNum": "***********", "reachLogList": [{"nodeId": "30010304", "user": "<EMAIL>", "reachTime": 1599630935333}, {"nodeId": "30010302", "user": "<EMAIL>", "reachTime": 1599633080882}], "applerType": 2, "parentOrgPosId": 850, "applerUid": "wb.jiang<PERSON><PERSON>@mesg.corp.netease.com", "level": 96, "trackFullList": [], "remark": "进行商品设计工作", "createUserName": "蒋素圆", "businessStatus": 1, "updateTime": 1599636900995, "orgPosId": 1088, "dutyDesc": "商品图案设计", "applerName": "蒋素圆", "observerList": ["ganjun<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "wb.jiang<PERSON><PERSON>@mesg.corp.netease.com"], "applerRoleType": 0, "orgPosPath": "严选事业部>设计中心>商品设计部>耐用品组", "trackList": ["ganjun<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>"], "createTime": 1599630932932, "acceptorNameList": [], "createUser": "wb.jiang<PERSON><PERSON>@mesg.corp.netease.com", "orgPosName": "耐用品组", "acceptorList": []}}]}}