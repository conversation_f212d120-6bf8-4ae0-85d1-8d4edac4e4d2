import { deleteIcacOrgPosUser } from '../../service/permcenter/unitOrg';
import { disassociateUser, lockUser } from '../transfer/transfer.service';

export { getAllProductManager } from '../../service/productcenter/productInfo';
export async function departureExtendOperate(params: any) {
    const uidParam = { uid: params.uid };
    await deleteIcacOrgPosUser(params);
    await lockUser(uidParam);
    await disassociateUser(uidParam);
}
