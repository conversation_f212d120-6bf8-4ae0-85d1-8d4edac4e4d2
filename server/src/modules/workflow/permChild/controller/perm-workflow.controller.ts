import { FlowData, FlowSubmitData } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { appLogger } from '@tiger/logger';
import { isNullOrUndefined } from 'util';
import { contextPath } from '../../../../conf';
import { topologyName } from '../perm-config';
import Workflow from '../perm-workflow';

type SubmitResult = AjaxResult<string[]>;
/**
 * 工单创建与提交
 */
@RestController
@RequestMapping(`${contextPath}/xhr/workflow/${topologyName}`)
export class PermChildWorkflowController {

    constructor(private workflow: Workflow) {
    }

    /**
     * 工单创建并提交，触发流程流转
     * 创建多个业务系统权限子工单
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/submit.json')
    async submit(ctx: RequestContext<FlowSubmitData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        try {
            const userInfo = ctx.openIDInfo;
            const nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            appLogger.error('工单提交失败', e);
            ctx.body = AjaxResult.internal('工单提交失败' + JSON.stringify(e));
        }
    }
}
