import { Component, EventEmitter, Input, Output, HostListener } from '@angular/core';
import { IApp } from '../list.component';

@Component({
    selector: 'tag-block',
    styleUrls: ['./tagBlock.component.scss'],
    templateUrl: './tagBlock.component.html'
})
export class TagBlock {
    @Input() appList: IApp[];
    @Input() tagName: string;
    @Input() isFav: Boolean;
    @Input() cols: number;
    @Output() private favClick: EventEmitter<IApp> = new EventEmitter();

    constructor() { }

    idFn(index, item) {
        return item.id;
    }
    onFavClick(app) {
        this.favClick.emit(app);
    }

}
