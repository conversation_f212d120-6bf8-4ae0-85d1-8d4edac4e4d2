import {BaseWorkflow, FlowMetaData, FlowNodeMessage, SkipParam} from '@eagle/workflow-node';
import {Service} from '@tiger/boot';
import Config from './writtendoc-delete-config';

@Service
export default class Workflow extends BaseWorkflow {
    constructor(config: Config) {
        super(config);
    }

    /**
     * @description 获取节点的审批人信息。处理各个节点
     * @param flowMetaData 工单元数据
     * @param flowNodeMessage 工单业务数据
     * @returns string[] 审批人uid列表
     */
    async getApproverList(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<string[]> {
        const approverUsers: any[] = [];
        // 所有获取审批人的操作会放在成文管理系统
        return approverUsers;
    }

    /**
     * @description 流程跳转条件, 如果有多条件的跳转，需要用户进行重写实现，处理各个节点的跳转条件设置
     * @param flowMetaData
     * @param flowNodeMessage
     */
    async getCustomSkipParam(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<SkipParam> {
        const skipParam: SkipParam = {approved: flowNodeMessage.approved!, paramMap: {}};
        // 通过审批的情况下
        if (flowNodeMessage.approved) {
            skipParam.paramMap = {
                branch: 0
            };
        } else {
            skipParam.paramMap = {
                branch: 1
            };
        }
        return skipParam;
    }
}
