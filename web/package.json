{"name": "yanxuan-bflow", "version": "2.0.0", "description": "nav flow", "keywords": ["node", "koa", "angularx", "framework", "front-end", "web"], "scripts": {"dev": "sh scripts/dev/dev.sh", "dev:remote": "sh scripts/dev/dev-remote.sh", "server": "nodemon --config ./server/src/nodemon.json ./server/src/index.ts", "build": "sh scripts/build/build.sh", "build:test": "sh scripts/build/build-test.sh", "build:online": "sh scripts/build/build-online.sh", "deploy:test": "sh scripts/deploy/deploy-test.sh", "deploy:online": "sh scripts/deploy/deploy-online.sh", "tslint": "tslint --fix -c tslint.json './web/**/*.ts' './server/**/*.ts'", "test:server": "TS_NODE_PROJECT=server/spec/tsconfig.json JASMINE_CONFIG_PATH=server/spec/support/jasmine.json jasmine-ts"}, "repository": {"type": "git", "url": "https://git.mail.netease.com/yanxuan-bflow/yanxuan-bflow"}, "license": "LGPL", "author": "whw", "dependencies": {"moment": "^2.29.1"}, "devDependencies": {"@angular/common": "^6.0.0", "@angular/compiler": "^6.0.0", "@angular/compiler-cli": "^6.0.0", "@angular/core": "^6.0.0", "@angular/forms": "^6.0.0", "@angular/platform-browser": "^6.0.0", "@angular/platform-browser-dynamic": "^6.0.0", "@angular/router": "^6.0.0", "@eagle/workflow": "^6.5.0", "@shark/popo": "^6.0.0", "@shark/secure": "^6.0.1", "@shark/shark-angularX": "^6.5.1", "@shark/shark-angularX-webpack": "^6.0.9", "@shark/shark-css": "^7.0.19", "@shark/shark-deploy": "^6.0.8", "@shark/shark-yx-workflow": "^6.2.1", "@sharkr/utils": "^1.0.4", "@tiger/proxy": "^1.0.5", "@types/boom": "^7.2.0", "@types/ckeditor": "0.0.41", "@types/ip": "0.0.31", "@types/jasmine": "^2.8.5", "@types/koa": "^2.0.48", "@types/koa-compose": "^3.2.2", "@types/koa-etag": "^3.0.0", "@types/koa-router": "^7.0.27", "@types/path-to-regexp": "^1.1.1", "@yx-module/umc-manage": "^6.0.0", "@yx-module/umc-permission": "^6.2.3", "@yx-module/umc-selector": "^6.0.0", "chalk": "2.4.1", "file-saver": "^2.0.0", "get-port": "^3.2.0", "jasmine": "^2.9.0", "jasmine-core": "^2.9.1", "jasmine-ts": "^0.2.1", "jquery": "^3.3.1", "koa": "^2.6.2", "koa-body": "^4.0.4", "koa-proxies": "^0.8.1", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "koa-webpack-middleware": "^1.0.7", "nodemon": "^2.0.4", "openurl": "^1.1.1", "opn": "^5.4.0", "rxjs": "^6.2.0", "supertest": "^3.3.0", "ts-node": "^6.1.1", "typescript": "^2.9.2", "xlsx": "^0.14.1", "yargs": "^7.0.2", "zone.js": "^0.8.26"}}