import { BaseWorkflow, FlowMetaData, FlowNodeMessage, SkipParam } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import { notify } from '../../service/message/notify';
import { getOrgAgencyUser, getSecondLevelManagerInfoByOrgId, getSecondLevelPoliticalInfoByOrgId, getThirdLevelManagerInfoByOrgId, isAuthedThirdLevelOrg } from '../../service/permcenter/approvePerm';
import Config from './departure-config';
import { getAllProductManager } from './departure.service';
@Service
export default class Workflow extends BaseWorkflow {
    constructor(config: Config) {
        super(config);
    }

    // 通知产品经理
    async notifyProductMangager(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage) {
        const managerUidList = await getAllProductManager();
        try {
            await this.notifyCustomUser({
                text: 'bflow_workflow_departure_productmanager_notify_text',
                html: 'bflow_workflow_departure_productmanager_notify_html'
            }, managerUidList, flowMetaData, flowNodeMessage, {
                    subject: `[门户]-[离职申请工单]-关于${flowNodeMessage.applerName ? flowNodeMessage.applerName : flowNodeMessage.applerUid}的离职通知`
                });
        } catch (e) {
            console.log('通知产品经理失败', e);
        }
    }

    /**
     * @description 获取节点的审批人信息。处理各个节点
     * @param flowMetaData 工单元数据
     * @param flowNodeMessage 工单业务数据
     * @returns string[] 审批人uid列表
     */
    async getApproverList(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<string[]> {
        const currentNodeId = flowMetaData.currentNodeId;
        let approverUsers = [];
        switch (currentNodeId) {
            // 三级部门代理审批人
            case '30010202':
                approverUsers = await getThirdLevelManagerInfoByOrgId(flowNodeMessage.orgPosId);
                break;
            case '30010203':
                approverUsers = await getSecondLevelManagerInfoByOrgId(flowNodeMessage.orgPosId);
                break;
            // 指导员审批节点
            case '30010204':
                approverUsers = await getSecondLevelPoliticalInfoByOrgId(flowNodeMessage.orgPosId);
                break;
        }
        return approverUsers.map((approver: any) => {
            return approver.uid;
        });
    }

    /**
     * @description 流程跳转条件, 如果有多条件的跳转，需要用户进行重写实现，处理各个节点的跳转条件设置
     * @param flowMetaData
     * @param flowNodeMessage
     */
    async getCustomSkipParam(flowMetaData: FlowMetaData, flowNodeMessage: FlowNodeMessage): Promise<SkipParam> {
        const currentNodeId = flowMetaData.currentNodeId;
        const orgPosId = flowNodeMessage.orgPosId;
        const level = flowNodeMessage.level;
        const skipParam: any = { approved: flowNodeMessage.approved!, paramMap: {} };
        // 通过审批的情况下
        if (flowNodeMessage.approved) {
            switch (currentNodeId) {
                // 指导员审批节点
                case '30010204':
                    // 是否有代理审批节点
                    skipParam.paramMap.agency = await isAuthedThirdLevelOrg('depature', level, orgPosId) || false;
                    break;
            }
        }
        return skipParam;
    }
}
