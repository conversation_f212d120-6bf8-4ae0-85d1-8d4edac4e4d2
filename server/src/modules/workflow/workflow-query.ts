import { BaseWorkflowQuery } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import { AppConfig } from '@tiger/core';

@Service
export default class WorkflowQuery extends BaseWorkflowQuery {
    constructor() {
        super(AppConfig.productCode);
    }

    toTime(timestamp: number) {
        const time = new Date(timestamp);
        const Y = time.getFullYear() + '-';
        const M = (time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1) + '-';
        const D = time.getDate() + ' ';
        const h = time.getHours() + ':';
        const m = ('0' + time.getMinutes()).slice(-2) + ':';
        const s = ('0' + time.getSeconds()).slice(-2);
        return Y + M + D + h + m + s;
    }

    confidentialityToName(confidentiality: number) {
        let confidentialityName: string = '';
        if (confidentiality === 1) {
            confidentialityName = '绝密级';
        } else if (confidentiality === 2) {
            confidentialityName = '机密级';
        } else if (confidentiality === 3) {
            confidentialityName = '秘密级';
        } else if (confidentiality === 4) {
            confidentialityName = '普一级';
        } else if (confidentiality === 5) {
            confidentialityName = '普二级';
        } else if (confidentiality === 6) {
            confidentialityName = '普三级';
        }
        return confidentialityName;
    }

    nodeIdtoName(nodeId: string) {
        let nodeName: string = '';
        if (nodeId === '70210101') {
            nodeName = '拟写成文文件';
        } else if (nodeId === '70210102') {
            nodeName = '内控文档管理员复核';
        } else if (nodeId === '70210103') {
            nodeName = '会签人员会签';
        } else if (nodeId === '70210104') {
            nodeName = '三级部门负责人审核';
        } else if (nodeId === '70210105') {
            nodeName = '二级部门负责人审核';
        } else if (nodeId === '70210106') {
            nodeName = '网易严选CEO审批';
        } else if (nodeId === '70210107') {
            nodeName = '工单完结';
        } else if (nodeId === '70210108') {
            nodeName = '内控/合规部负责人审定';
        } else if (nodeId === '70210109') {
            nodeName = 'ISO管理员复核';
        }
        return nodeName;
    }

}
