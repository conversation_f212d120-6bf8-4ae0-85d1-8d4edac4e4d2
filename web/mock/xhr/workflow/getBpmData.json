{"code": 200, "data": {"needUpdateTopo": true, "needUpdateInOutVer": false, "productCode": "navAdmin", "modelId": "ce85c3d9b80b4560ae97d49d8dbdd238", "name": "ape_test_ape1", "description": "员工申请产品访问权限", "model": {"resourceId": "canvas", "stencilset": {"namespace": "http://b3mn.org/stencilset/bpmn2.0#"}, "id": "canvas", "modelType": "model", "properties": {"process_id": "", "documentation": "", "name": ""}, "childShapes": [{"outgoing": [{"resourceId": "sid-E0275BF7-9A6A-4B86-9A23-F72CF058ED3D"}], "resourceId": "sid-FB43CFFE-8CDD-4DE7-8947-05AB230A92A1", "bounds": {"upperLeft": {"x": 0, "y": 226}, "lowerRight": {"x": 30, "y": 256}}, "stencil": {"id": "StartNoneEvent"}, "dockers": [], "properties": {"formproperties": {"outputData": [{"needEncrypt": false, "name": "createUser", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "工单创建人邮箱id"}, {"needEncrypt": false, "name": "createUserName", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "创建人名字"}, {"needEncrypt": false, "name": "createUserManager", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "创建人直接主管邮箱id"}, {"needEncrypt": false, "name": "createUserManagerName", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "创建人直接主管姓名"}, {"needEncrypt": false, "name": "createUserOrg", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "创建人行政组织架构"}, {"needEncrypt": false, "name": "acceptorList", "publicField": true, "type": "Array", "isEncrypt": 1, "desc": "待审批人"}, {"needEncrypt": false, "name": "createTime", "publicField": true, "type": "<PERSON>", "isEncrypt": 1, "desc": "创建时间"}, {"needEncrypt": false, "name": "updateTime", "publicField": true, "type": "<PERSON>", "isEncrypt": 1, "desc": "更新时间"}, {"needEncrypt": false, "name": "trackList", "publicField": true, "type": "Array", "isEncrypt": 1, "desc": "待审批人列表"}, {"needEncrypt": false, "name": "applerUid", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "申请人邮箱"}, {"needEncrypt": false, "name": "applerName", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "申请人姓名"}, {"needEncrypt": false, "name": "manager<PERSON><PERSON>", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "申请人主管/BU邮箱"}, {"needEncrypt": false, "name": "<PERSON><PERSON><PERSON>", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "申请人主管/BU姓名"}, {"needEncrypt": false, "name": "employeeNum", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "工号"}, {"needEncrypt": false, "name": "userCenterOrgPosVOS", "publicField": true, "type": "Array", "isEncrypt": 1, "desc": "组织架构"}, {"needEncrypt": false, "name": "productId", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "产品id"}, {"needEncrypt": false, "name": "productName", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "产品name"}, {"needEncrypt": false, "name": "productCode", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "产品code"}, {"needEncrypt": false, "name": "remark", "publicField": true, "type": "string", "isEncrypt": 1, "desc": "备注"}, {"needEncrypt": false, "name": "business_status", "publicField": true, "type": "<PERSON>", "isEncrypt": 1, "desc": "工单业务状态"}, {"needEncrypt": false, "name": "productManagerList", "publicField": true, "type": "Array", "isEncrypt": 1, "desc": "产品负责人列表"}, {"needEncrypt": false, "name": "trackFullList", "publicField": true, "type": "<PERSON>", "desc": "完整的输入输出属性"}, {"needEncrypt": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "publicField": true, "type": "Boolean", "desc": "申请的产品是否有负责人"}, {"needEncrypt": false, "name": "isInIcac", "publicField": true, "type": "Boolean", "desc": "是否接入了权限中心"}], "inputData": []}, "documentation": "开始"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-33878B76-DECE-40FB-AE5E-2933906F3CDC"}], "resourceId": "sid-********-6C29-4D00-B685-3D5C734DC7E1", "bounds": {"upperLeft": {"x": 60, "y": 381}, "lowerRight": {"x": 160, "y": 461}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [{"needEncrypt": false, "name": "updateTime", "publicField": true, "type": "<PERSON>", "isEncrypt": 1, "desc": "更新时间"}, {"needEncrypt": false, "name": "acceptorList", "publicField": true, "type": "Array", "desc": "待审批人列表"}, {"needEncrypt": false, "name": "business_status", "publicField": true, "type": "<PERSON>", "desc": "工单业务状态"}, {"needEncrypt": false, "name": "reachTime", "publicField": true, "type": "<PERSON>", "desc": "接受时间"}, {"needEncrypt": false, "name": "trackList", "publicField": true, "type": "<PERSON>", "desc": "跟踪人列表"}, {"needEncrypt": false, "name": "trackFullList", "publicField": true, "type": "<PERSON>", "desc": "完整的跟踪人信息"}, {"needEncrypt": false, "name": "isInIcac", "publicField": true, "type": "Boolean", "desc": "是否接入过权限中心"}, {"needEncrypt": false, "name": "createUser", "publicField": true, "type": "string", "desc": "创建人uid"}, {"needEncrypt": false, "name": "createUserName", "publicField": true, "type": "string", "desc": "创建人名字"}, {"needEncrypt": false, "name": "applerUid", "publicField": true, "type": "string", "desc": "申请人uid"}, {"needEncrypt": false, "name": "applerName", "publicField": true, "type": "string", "desc": "申请人名字"}, {"needEncrypt": false, "name": "manager<PERSON><PERSON>", "publicField": true, "type": "string", "desc": "bu主管uid"}, {"needEncrypt": false, "name": "<PERSON><PERSON><PERSON>", "publicField": true, "type": "string", "desc": "bu主管名字"}, {"needEncrypt": false, "name": "employeeNum", "publicField": true, "type": "string", "desc": "工号"}, {"needEncrypt": false, "name": "userCenterOrgPosVOS", "publicField": true, "type": "Array", "desc": "组织架构"}, {"needEncrypt": false, "name": "productId", "publicField": true, "type": "string", "desc": "产品id"}, {"needEncrypt": false, "name": "productName", "publicField": true, "type": "string", "desc": "产品name"}, {"needEncrypt": false, "name": "productCode", "publicField": true, "type": "string", "desc": "产品code"}, {"needEncrypt": false, "name": "remark", "publicField": true, "type": "string", "desc": "备注"}, {"needEncrypt": false, "name": "productManagerList", "publicField": true, "type": "Array", "desc": "产品负责人list"}, {"needEncrypt": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "publicField": true, "type": "string", "desc": "是否有审批人"}, {"needEncrypt": false, "name": "acceptorNameList", "publicField": true, "type": "Array", "desc": "审批人列表姓名"}], "inputData": [{"product": "navAdmin", "name": "createUser", "publicField": true, "type": "string", "desc": "创建人邮箱"}, {"product": "navAdmin", "name": "createUserName", "publicField": true, "type": "string", "desc": "创建人姓名"}, {"product": "navAdmin", "name": "createUserManager", "publicField": true, "type": "string", "desc": "创建人的直接主管邮箱"}, {"product": "navAdmin", "name": "createUserManagerName", "publicField": true, "type": "string", "desc": "创建人的直接主管姓名"}, {"product": "navAdmin", "name": "createUserOrg", "publicField": true, "type": "string", "desc": "创建人所属行政组织架构"}, {"product": "navAdmin", "name": "applerUid", "publicField": true, "type": "string", "desc": "权限申请人uid"}, {"product": "navAdmin", "name": "applerName", "publicField": true, "type": "string", "desc": "权限申请人name"}, {"product": "navAdmin", "name": "manager<PERSON><PERSON>", "publicField": true, "type": "string", "desc": "申请人BU/二级部门主管"}, {"product": "navAdmin", "name": "<PERSON><PERSON><PERSON>", "publicField": true, "type": "string", "desc": "申请人BU/二级部门主管姓名"}, {"product": "navAdmin", "name": "employeeNum", "publicField": true, "type": "string", "desc": "申请人工号"}, {"product": "navAdmin", "name": "userCenterOrgPosVOS", "publicField": true, "type": "Array", "desc": "组织架构"}, {"product": "navAdmin", "name": "productId", "publicField": true, "type": "string", "desc": "待申请的产品id"}, {"product": "navAdmin", "name": "productCode", "publicField": true, "type": "string", "desc": "待申请的产品code"}, {"product": "navAdmin", "name": "productName", "publicField": true, "type": "string", "desc": "待申请的产品名称"}, {"product": "navAdmin", "name": "remark", "publicField": true, "type": "string", "desc": "工单备注"}, {"product": "navAdmin", "name": "trackList", "publicField": true, "type": "Array", "desc": "跟踪人列表"}, {"product": "navAdmin", "name": "acceptorList", "publicField": true, "type": "Array", "desc": "待审批人列表"}, {"product": "navAdmin", "name": "createTime", "publicField": true, "type": "<PERSON>", "desc": "创建时间"}, {"product": "navAdmin", "name": "updateTime", "publicField": true, "type": "<PERSON>", "desc": "更新时间"}, {"product": "navAdmin", "name": "business_status", "publicField": true, "type": "<PERSON>", "desc": "工单业务状态"}, {"product": "navAdmin", "name": "productManagerList", "publicField": true, "type": "Array", "desc": "产品负责人列表"}, {"product": "navAdmin", "name": "reachTime", "publicField": true, "type": "<PERSON>", "desc": "接受时间"}, {"product": "navAdmin", "name": "trackFullList", "publicField": true, "type": "<PERSON>", "desc": "完整的跟踪人信息"}, {"product": "navAdmin", "name": "roleList", "publicField": true, "type": "Array", "desc": "拥有的产品角色"}, {"product": "navAdmin", "name": "<PERSON><PERSON><PERSON><PERSON>", "publicField": true, "type": "Boolean", "desc": "申请的产品是否有负责人"}, {"product": "navAdmin", "name": "originRoleList", "publicField": true, "type": "Array", "desc": "更新之前用户拥有的角色"}, {"product": "navAdmin", "name": "isInIcac", "publicField": true, "type": "Boolean", "desc": "是否接入过权限中心"}]}, "formkeydefinition": "navAdmin", "processexecutor": "{\"userType\":0,\"roleId\":\"\",\"categoryId\":\"\",\"specUsers\":\"[]\"}", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "提交申请", "overrideid": "100", "name": "提交申请"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-D0FEC4AB-A926-4C3B-AFE6-D2848F653193"}], "resourceId": "sid-4DA958A0-26D9-4D47-93A7-70F39FD7D51A", "bounds": {"upperLeft": {"x": 1065, "y": 381}, "lowerRight": {"x": 1165, "y": 461}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "负责人审批", "overrideid": "500", "name": "负责人审批"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-A2655A71-2E33-4B82-BF3E-9D433AF8D428"}, {"resourceId": "sid-1FE45439-C970-4C95-B85B-78DFD7C5B215"}], "resourceId": "sid-7226EB9C-F880-4AF0-B169-D24B9E4F0E7F", "bounds": {"upperLeft": {"x": 405, "y": 401}, "lowerRight": {"x": 445, "y": 441}}, "stencil": {"id": "InclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-8FE9582C-219B-4F06-AE44-6BA7F9C636F6"}], "resourceId": "sid-E27C0367-E6D6-497F-9736-3CDC21FDE221", "bounds": {"upperLeft": {"x": 375, "y": 258}, "lowerRight": {"x": 475, "y": 338}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "DBA审批", "overrideid": "300", "name": "DBA审批"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-D8FDF07B-E057-42E7-A9FD-1DF08C9ED303"}], "resourceId": "sid-BED98281-9585-4D1B-934E-BD1AC6AC0EFD", "bounds": {"upperLeft": {"x": 375, "y": 498}, "lowerRight": {"x": 475, "y": 578}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "{\"userType\":0,\"roleId\":\"\",\"categoryId\":\"\",\"specUsers\":\"[]\"}", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "SA审批", "overrideid": "400", "name": "SA审批"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-80B0500E-E8A9-4C5B-A418-28D75930AEB5"}], "resourceId": "sid-34EF43DA-8D12-4B56-A858-ABF14BC60A30", "bounds": {"upperLeft": {"x": 975, "y": 401}, "lowerRight": {"x": 1015, "y": 441}}, "stencil": {"id": "InclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-4DA958A0-26D9-4D47-93A7-70F39FD7D51A"}], "resourceId": "sid-80B0500E-E8A9-4C5B-A418-28D75930AEB5", "bounds": {"upperLeft": {"x": 1015.1718662467667, "y": 421.211215313794}, "lowerRight": {"x": 1064.5195400032333, "y": 421.417690936206}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-4DA958A0-26D9-4D47-93A7-70F39FD7D51A"}}, {"outgoing": [{"resourceId": "sid-04CFDE38-E499-432C-8649-32C81EDC0F98"}], "resourceId": "sid-7CE72B24-E0C1-46D3-8132-8BA66BE05AA7", "bounds": {"upperLeft": {"x": 210, "y": 381}, "lowerRight": {"x": 310, "y": 461}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "{\"userType\":0,\"roleId\":\"\",\"categoryId\":\"\",\"specUsers\":\"[]\"}", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "主管审批", "overrideid": "200", "name": "主管审批"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-BED98281-9585-4D1B-934E-BD1AC6AC0EFD"}], "resourceId": "sid-A2655A71-2E33-4B82-BF3E-9D433AF8D428", "bounds": {"upperLeft": {"x": 425.1722605559663, "y": 441.61327204015834}, "lowerRight": {"x": 425.4136769440337, "y": 497.86329045984166}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-BED98281-9585-4D1B-934E-BD1AC6AC0EFD"}}, {"outgoing": [{"resourceId": "sid-********-6C29-4D00-B685-3D5C734DC7E1"}], "resourceId": "sid-E0275BF7-9A6A-4B86-9A23-F72CF058ED3D", "bounds": {"upperLeft": {"x": 30.84375, "y": 241}, "lowerRight": {"x": 110, "y": 380.625}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 15, "y": 15}, {"x": 110, "y": 241}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-********-6C29-4D00-B685-3D5C734DC7E1"}}, {"outgoing": [{"resourceId": "sid-7CE72B24-E0C1-46D3-8132-8BA66BE05AA7"}], "resourceId": "sid-33878B76-DECE-40FB-AE5E-2933906F3CDC", "bounds": {"upperLeft": {"x": 160.7109375, "y": 421}, "lowerRight": {"x": 210, "y": 421}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 1, "y": 40}], "properties": {"conditionsequenceflow": "", "showdiamondmarker": false}, "childShapes": [], "target": {"resourceId": "sid-7CE72B24-E0C1-46D3-8132-8BA66BE05AA7"}}, {"outgoing": [{"resourceId": "sid-E27C0367-E6D6-497F-9736-3CDC21FDE221"}], "resourceId": "sid-1FE45439-C970-4C95-B85B-78DFD7C5B215", "bounds": {"upperLeft": {"x": 425.0098233061779, "y": 338.66013874406985}, "lowerRight": {"x": 425.3768954438221, "y": 400.69533000593015}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 50, "y": 79}], "properties": {"conditionsequenceflow": "", "showdiamondmarker": false}, "childShapes": [], "target": {"resourceId": "sid-E27C0367-E6D6-497F-9736-3CDC21FDE221"}}, {"outgoing": [{"resourceId": "sid-BC1DB81E-9015-43E9-9787-BBBA228604A5"}], "resourceId": "sid-A1B3CD96-7697-4D7C-BEAA-73D187B1BE89", "bounds": {"upperLeft": {"x": 510, "y": 258}, "lowerRight": {"x": 610, "y": 338}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "DBA确认", "overrideid": "301", "name": "DBA确认"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-A55CB1CB-7348-4BCE-AD7C-2F05E406604D"}], "resourceId": "sid-3E35A7FF-A2F4-4E07-9247-DBF884C81937", "bounds": {"upperLeft": {"x": 510, "y": 498}, "lowerRight": {"x": 610, "y": 578}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "{\"userType\":0,\"roleId\":\"\",\"categoryId\":\"\",\"specUsers\":\"[]\"}", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "SA确认", "overrideid": "401", "name": "SA确认"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-A1B3CD96-7697-4D7C-BEAA-73D187B1BE89"}], "resourceId": "sid-8FE9582C-219B-4F06-AE44-6BA7F9C636F6", "bounds": {"upperLeft": {"x": 475.578125, "y": 298}, "lowerRight": {"x": 510, "y": 298}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 99, "y": 40}, {"x": 1, "y": 40}], "properties": {"conditionsequenceflow": "", "showdiamondmarker": false}, "childShapes": [], "target": {"resourceId": "sid-A1B3CD96-7697-4D7C-BEAA-73D187B1BE89"}}, {"outgoing": [{"resourceId": "sid-3E35A7FF-A2F4-4E07-9247-DBF884C81937"}], "resourceId": "sid-D8FDF07B-E057-42E7-A9FD-1DF08C9ED303", "bounds": {"upperLeft": {"x": 475.5703125, "y": 538}, "lowerRight": {"x": 509.4296875, "y": 538}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-3E35A7FF-A2F4-4E07-9247-DBF884C81937"}}, {"outgoing": [{"resourceId": "sid-B69B57F6-F874-4F18-9073-D70DAE567D5A"}, {"resourceId": "sid-27CE46B8-C25D-45DB-A6C8-3F23E24D5D4B"}], "resourceId": "sid-7BF22503-3A3B-47BC-ADFC-C5F0567E568F", "bounds": {"upperLeft": {"x": 1095, "y": 45}, "lowerRight": {"x": 1135, "y": 85}}, "stencil": {"id": "ExclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [], "resourceId": "sid-4FC27B48-A6F9-460A-A675-021F5854FE22", "bounds": {"upperLeft": {"x": 1215, "y": 407}, "lowerRight": {"x": 1243, "y": 435}}, "stencil": {"id": "EndNoneEvent"}, "dockers": [], "properties": {"formkeydefinition": "", "executionlisteners": "", "documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-4FC27B48-A6F9-460A-A675-021F5854FE22"}], "resourceId": "sid-B69B57F6-F874-4F18-9073-D70DAE567D5A", "bounds": {"upperLeft": {"x": 1135.12109375, "y": 65.5}, "lowerRight": {"x": 1229, "y": 406.11328125}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 1229, "y": 65.5}, {"x": 14, "y": 14}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-4FC27B48-A6F9-460A-A675-021F5854FE22"}}, {"outgoing": [{"resourceId": "sid-7CE72B24-E0C1-46D3-8132-8BA66BE05AA7"}], "resourceId": "sid-27CE46B8-C25D-45DB-A6C8-3F23E24D5D4B", "bounds": {"upperLeft": {"x": 260, "y": 65.5}, "lowerRight": {"x": 1094.44921875, "y": 380.4228515625}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 260, "y": 65.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-7CE72B24-E0C1-46D3-8132-8BA66BE05AA7"}}, {"outgoing": [{"resourceId": "sid-AA9E2A8B-08FC-47AC-9B59-18AC55AA05FC"}, {"resourceId": "sid-11147EB1-29E3-4E33-9812-8E45FA29B6E0"}, {"resourceId": "sid-3A17041A-9A76-42D4-AB32-B0A611F60D6C"}], "resourceId": "sid-B3495AF9-1CDF-4271-98ED-57219F5EAEAB", "bounds": {"upperLeft": {"x": 540, "y": 690}, "lowerRight": {"x": 580, "y": 730}}, "stencil": {"id": "ExclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-BED98281-9585-4D1B-934E-BD1AC6AC0EFD"}], "resourceId": "sid-AA9E2A8B-08FC-47AC-9B59-18AC55AA05FC", "bounds": {"upperLeft": {"x": 456.84623650379393, "y": 578.5422568037229}, "lowerRight": {"x": 551.413529121206, "y": 698.9323525712771}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-BED98281-9585-4D1B-934E-BD1AC6AC0EFD"}}, {"outgoing": [{"resourceId": "sid-1D04DD90-260B-4371-AFB5-D2B85AFA6BC2"}], "resourceId": "sid-19DD9E9F-98C1-44EE-B604-842AFEE76F1E", "bounds": {"upperLeft": {"x": 660, "y": 498}, "lowerRight": {"x": 760, "y": 578}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "{\"userType\":0,\"roleId\":\"\",\"categoryId\":\"\",\"specUsers\":\"[]\"}", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "SA执行1", "overrideid": "402", "name": "SA执行1"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-19DD9E9F-98C1-44EE-B604-842AFEE76F1E"}], "resourceId": "sid-11147EB1-29E3-4E33-9812-8E45FA29B6E0", "bounds": {"upperLeft": {"x": 569.9146961634178, "y": 578.5115484577898}, "lowerRight": {"x": 674.8899913365822, "y": 699.6368890422102}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-19DD9E9F-98C1-44EE-B604-842AFEE76F1E"}}, {"outgoing": [{"resourceId": "sid-BBFF667B-4CFA-42AD-BAEE-C4CB061AFABE"}], "resourceId": "sid-6C2120F3-D940-4958-A067-0903DCE879C4", "bounds": {"upperLeft": {"x": 810, "y": 498}, "lowerRight": {"x": 910, "y": 578}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "{\"userType\":0,\"roleId\":\"\",\"categoryId\":\"\",\"specUsers\":\"[]\"}", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "SA执行2", "overrideid": "403", "name": "SA执行2"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-6C2120F3-D940-4958-A067-0903DCE879C4"}], "resourceId": "sid-3A17041A-9A76-42D4-AB32-B0A611F60D6C", "bounds": {"upperLeft": {"x": 573.6507263096676, "y": 566.7998773846374}, "lowerRight": {"x": 809.9967346278324, "y": 702.9257085528626}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-6C2120F3-D940-4958-A067-0903DCE879C4"}}, {"outgoing": [{"resourceId": "sid-6C2120F3-D940-4958-A067-0903DCE879C4"}], "resourceId": "sid-1D04DD90-260B-4371-AFB5-D2B85AFA6BC2", "bounds": {"upperLeft": {"x": 760.21875, "y": 538}, "lowerRight": {"x": 809.78125, "y": 538}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-6C2120F3-D940-4958-A067-0903DCE879C4"}}, {"outgoing": [{"resourceId": "sid-34EF43DA-8D12-4B56-A858-ABF14BC60A30"}], "resourceId": "sid-BBFF667B-4CFA-42AD-BAEE-C4CB061AFABE", "bounds": {"upperLeft": {"x": 910.5703125, "y": 441.1953125}, "lowerRight": {"x": 995, "y": 538}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 995, "y": 538}, {"x": 20, "y": 20}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-34EF43DA-8D12-4B56-A858-ABF14BC60A30"}}, {"outgoing": [{"resourceId": "sid-26834156-57E5-435D-8623-F56BF468093F"}, {"resourceId": "sid-551D13C0-6542-4855-A828-896EF25C1DE0"}], "resourceId": "sid-00E35D86-9A8F-4EF5-8D63-B38F64000A2A", "bounds": {"upperLeft": {"x": 645, "y": 278}, "lowerRight": {"x": 685, "y": 318}}, "stencil": {"id": "InclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-00E35D86-9A8F-4EF5-8D63-B38F64000A2A"}], "resourceId": "sid-BC1DB81E-9015-43E9-9787-BBBA228604A5", "bounds": {"upperLeft": {"x": 610.453113769534, "y": 298.23911428326795}, "lowerRight": {"x": 644.718761230466, "y": 298.40151071673205}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 20.5, "y": 20.5}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-00E35D86-9A8F-4EF5-8D63-B38F64000A2A"}}, {"outgoing": [{"resourceId": "sid-F0459FD8-E815-49E1-B5ED-FB6689D5E3A1"}], "resourceId": "sid-9180E2A0-5C4B-435F-B42F-0D152470A338", "bounds": {"upperLeft": {"x": 660, "y": 165}, "lowerRight": {"x": 760, "y": 245}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": "", "formkeydefinition": "", "processexecutor": "", "executionlisteners": "", "documentation": "DBA执行1", "overrideid": "302", "name": "DBA执行1"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-9180E2A0-5C4B-435F-B42F-0D152470A338"}], "resourceId": "sid-26834156-57E5-435D-8623-F56BF468093F", "bounds": {"upperLeft": {"x": 685.96875, "y": 245.4453125}, "lowerRight": {"x": 710, "y": 298.5}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 710, "y": 298.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-9180E2A0-5C4B-435F-B42F-0D152470A338"}}, {"outgoing": [{"resourceId": "sid-5A390966-ADDB-4A18-9352-FCAE1EA3ADC7"}], "resourceId": "sid-03A2C3AC-5337-48A5-B154-BB3FD0EC8DAD", "bounds": {"upperLeft": {"x": 660, "y": 345}, "lowerRight": {"x": 760, "y": 425}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": "", "formkeydefinition": "", "processexecutor": "", "executionlisteners": "", "documentation": "DBA执行2", "overrideid": "304", "name": "DBA执行3"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-03A2C3AC-5337-48A5-B154-BB3FD0EC8DAD"}], "resourceId": "sid-551D13C0-6542-4855-A828-896EF25C1DE0", "bounds": {"upperLeft": {"x": 685.96875, "y": 298.5}, "lowerRight": {"x": 710, "y": 344.8046875}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 710, "y": 298.5}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-03A2C3AC-5337-48A5-B154-BB3FD0EC8DAD"}}, {"outgoing": [{"resourceId": "sid-55629DE0-8BBF-44C1-B284-C23C70216618"}], "resourceId": "sid-4848AD37-199B-4ACD-A195-128083CCAD8C", "bounds": {"upperLeft": {"x": 975, "y": 278}, "lowerRight": {"x": 1015, "y": 318}}, "stencil": {"id": "InclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-D5E1F2F4-306C-47A2-BF74-F66E3D769756"}], "resourceId": "sid-F0459FD8-E815-49E1-B5ED-FB6689D5E3A1", "bounds": {"upperLeft": {"x": 760.7109375, "y": 205}, "lowerRight": {"x": 810, "y": 205}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 1, "y": 40}], "properties": {"conditionsequenceflow": "", "showdiamondmarker": false}, "childShapes": [], "target": {"resourceId": "sid-D5E1F2F4-306C-47A2-BF74-F66E3D769756"}}, {"outgoing": [{"resourceId": "sid-7E75CA36-1D8E-4E23-8437-B6C3E1FB192F"}], "resourceId": "sid-D5E1F2F4-306C-47A2-BF74-F66E3D769756", "bounds": {"upperLeft": {"x": 810, "y": 165}, "lowerRight": {"x": 910, "y": 245}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": "", "formkeydefinition": "", "processexecutor": "", "executionlisteners": "", "documentation": "DBA执行2", "overrideid": "303", "name": "DBA执行2"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-B3495AF9-1CDF-4271-98ED-57219F5EAEAB"}], "resourceId": "sid-A55CB1CB-7348-4BCE-AD7C-2F05E406604D", "bounds": {"upperLeft": {"x": 560.1161797885485, "y": 578.0820270492284}, "lowerRight": {"x": 560.4385077114515, "y": 689.2851604507716}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 20.5, "y": 20.5}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-B3495AF9-1CDF-4271-98ED-57219F5EAEAB"}}, {"outgoing": [{"resourceId": "sid-4848AD37-199B-4ACD-A195-128083CCAD8C"}], "resourceId": "sid-7E75CA36-1D8E-4E23-8437-B6C3E1FB192F", "bounds": {"upperLeft": {"x": 910.3938201010102, "y": 239.71574273625149}, "lowerRight": {"x": 983.1022736489898, "y": 289.8037885137485}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 20, "y": 20}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-4848AD37-199B-4ACD-A195-128083CCAD8C"}}, {"outgoing": [{"resourceId": "sid-34EF43DA-8D12-4B56-A858-ABF14BC60A30"}], "resourceId": "sid-55629DE0-8BBF-44C1-B284-C23C70216618", "bounds": {"upperLeft": {"x": 995.082206598654, "y": 318.6406166702415}, "lowerRight": {"x": 995.417793401346, "y": 400.8593833297585}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 20, "y": 20}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-34EF43DA-8D12-4B56-A858-ABF14BC60A30"}}, {"outgoing": [{"resourceId": "sid-85CC7D22-DC56-4BB0-B17E-5AF7AF23B3C9"}], "resourceId": "sid-8C3F2F1D-F014-4F99-B966-095DC1A2BD93", "bounds": {"upperLeft": {"x": 810, "y": 345}, "lowerRight": {"x": 910, "y": 425}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": "", "formkeydefinition": "", "processexecutor": "", "executionlisteners": "", "documentation": "DBA执行4", "overrideid": "305", "name": "DBA执行4"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-8C3F2F1D-F014-4F99-B966-095DC1A2BD93"}], "resourceId": "sid-5A390966-ADDB-4A18-9352-FCAE1EA3ADC7", "bounds": {"upperLeft": {"x": 760.21875, "y": 385}, "lowerRight": {"x": 809.78125, "y": 385}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-8C3F2F1D-F014-4F99-B966-095DC1A2BD93"}}, {"outgoing": [{"resourceId": "sid-4848AD37-199B-4ACD-A195-128083CCAD8C"}], "resourceId": "sid-85CC7D22-DC56-4BB0-B17E-5AF7AF23B3C9", "bounds": {"upperLeft": {"x": 910.4108835350936, "y": 306.018263833727}, "lowerRight": {"x": 982.5578664649064, "y": 352.512986166273}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 20, "y": 20}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-4848AD37-199B-4ACD-A195-128083CCAD8C"}}, {"outgoing": [{"resourceId": "sid-ED25773E-0A8B-4699-BC6F-74777FB8C4D3"}], "resourceId": "sid-1897B30A-9C5C-4D5B-B80B-76A038785070", "bounds": {"upperLeft": {"x": 1065, "y": 210}, "lowerRight": {"x": 1165, "y": 290}}, "stencil": {"id": "UserTask"}, "dockers": [], "properties": {"formproperties": {"outputData": [], "inputData": []}, "formkeydefinition": "navAdmin", "processexecutor": "", "executionlisteners": {"delList": [], "addList": [], "list": []}, "documentation": "负责人确认", "overrideid": "600", "name": "负责人确认"}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-1897B30A-9C5C-4D5B-B80B-76A038785070"}], "resourceId": "sid-D0FEC4AB-A926-4C3B-AFE6-D2848F653193", "bounds": {"upperLeft": {"x": 1115, "y": 290.41015625}, "lowerRight": {"x": 1115, "y": 380.58984375}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 50, "y": 40}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-1897B30A-9C5C-4D5B-B80B-76A038785070"}}, {"outgoing": [{"resourceId": "sid-7BF22503-3A3B-47BC-ADFC-C5F0567E568F"}], "resourceId": "sid-ED25773E-0A8B-4699-BC6F-74777FB8C4D3", "bounds": {"upperLeft": {"x": 1115, "y": 84.7890625}, "lowerRight": {"x": 1115, "y": 209.9765625}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 20, "y": 20}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-7BF22503-3A3B-47BC-ADFC-C5F0567E568F"}}, {"outgoing": [{"resourceId": "sid-DCB2B5CE-44E0-4F70-AD91-0D2F5D33EDBD"}, {"resourceId": "sid-4F267EC6-75A9-4238-B4C6-192CEDB8F6DF"}, {"resourceId": "sid-C6004620-8628-4FD4-8A7C-908E138A163E"}], "resourceId": "sid-61B9E8BA-B58E-45AC-A48C-7247D29CDDED", "bounds": {"upperLeft": {"x": 240, "y": 780}, "lowerRight": {"x": 280, "y": 820}}, "stencil": {"id": "ExclusiveGateway"}, "dockers": [], "properties": {"documentation": ""}, "childShapes": []}, {"outgoing": [{"resourceId": "sid-61B9E8BA-B58E-45AC-A48C-7247D29CDDED"}], "resourceId": "sid-04CFDE38-E499-432C-8649-32C81EDC0F98", "bounds": {"upperLeft": {"x": 260.0530753344131, "y": 461.2841788195676}, "lowerRight": {"x": 260.4723152905869, "y": 779.4873055554324}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 50, "y": 40}, {"x": 20.5, "y": 20.5}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-61B9E8BA-B58E-45AC-A48C-7247D29CDDED"}}, {"outgoing": [{"resourceId": "sid-7226EB9C-F880-4AF0-B169-D24B9E4F0E7F"}], "resourceId": "sid-DCB2B5CE-44E0-4F70-AD91-0D2F5D33EDBD", "bounds": {"upperLeft": {"x": 266.69994961751405, "y": 435.7410963941686}, "lowerRight": {"x": 419.30005038248595, "y": 786.2589036058314}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 20.5, "y": 20.5}], "properties": {"conditionsequenceflow": "", "showdiamondmarker": false}, "childShapes": [], "target": {"resourceId": "sid-7226EB9C-F880-4AF0-B169-D24B9E4F0E7F"}}, {"outgoing": [{"resourceId": "sid-4DA958A0-26D9-4D47-93A7-70F39FD7D51A"}], "resourceId": "sid-4F267EC6-75A9-4238-B4C6-192CEDB8F6DF", "bounds": {"upperLeft": {"x": 274.9242206152932, "y": 457.752688407507}, "lowerRight": {"x": 1064.7723729897748, "y": 794.3529993025488}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 59.77351737277286, "y": 51.182705376388924}], "properties": {"conditionsequenceflow": "", "showdiamondmarker": false}, "childShapes": [], "target": {"resourceId": "sid-4DA958A0-26D9-4D47-93A7-70F39FD7D51A"}}, {"outgoing": [{"resourceId": "sid-4FC27B48-A6F9-460A-A675-021F5854FE22"}], "resourceId": "sid-C6004620-8628-4FD4-8A7C-908E138A163E", "bounds": {"upperLeft": {"x": 275.6180841897271, "y": 426.1827053763889}, "lowerRight": {"x": 1215.7735173727729, "y": 794.5760836861111}}, "stencil": {"id": "SequenceFlow"}, "dockers": [{"x": 20.5, "y": 20.5}, {"x": 14, "y": 14}], "properties": {"conditionsequenceflow": ""}, "childShapes": [], "target": {"resourceId": "sid-4FC27B48-A6F9-460A-A675-021F5854FE22"}}]}, "needUpdateInOut": false, "key": "ape_test_ape1"}}