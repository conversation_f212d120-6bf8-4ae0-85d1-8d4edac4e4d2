import { Service } from '@tiger/boot';
import { getServiceUrl } from '@tiger/core';
import axios from '@tiger/request';

const domains = {
    // dev: 'http://local.yx.mail.netease.com:9300',
    dev: 'http://yxius.you.163.com',
    // dev: 'http://dev.yxius.you.163.com',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-ius.service.mailsaas'
};


@Service
export class UserService {
    async getDetailUserInfo(uid: string) {
        const serviceUrl = getServiceUrl(domains) + '/navAdmin/user/inner/getDetailUserInfo.json';
        const res = await axios.get(serviceUrl, { params: { uid } });
        return res.data;
    }
}
