import { TgModule } from '@tiger/boot';

import { OnboardWorkflowLinkAuditController } from './controller/onboard-workflow-link-audit.controller';
import { OnboardWorkflowNotifyController } from './controller/onboard-workflow-notify.controller';
import { OnboardWorkflowController } from './controller/onboard-workflow.controller';

@TgModule({
    imports: [],
    controllers: [OnboardWorkflowLinkAuditController, OnboardWorkflowNotifyController, OnboardWorkflowController]
})
export class OnboardWorkflowModule { }
