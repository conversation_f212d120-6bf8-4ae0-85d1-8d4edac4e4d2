/**
 * @description
 * 此文件主要用于配置基础的转发代理服务
 */
import tigerProxy, { ITigerProxyOption } from '@tiger/proxy';
import { Context } from 'koa';
import Router from 'koa-router';
import { reminders, cmdbProxyOptions, contextPath, icacProxyOptions, port, umcPermProxyOptions, umcProxyOptions, flowServerOptions, bpmOrderOptions } from '../conf';

const router = new Router({
    prefix: contextPath
});

// 权限中心的转发配置
router.all('/xhr/userCenterManage/*', tigerProxy('*', umcProxyOptions));

// 权限中心的转发配置
router.all('/xhr/icac/*', tigerProxy('*', icacProxyOptions));

// 权限列表
router.all('/xhr/umc-permission/xhr/*', tigerProxy('*', umcPermProxyOptions));

// cmdb
router.all('/xhr/cmdbProduct/*', tigerProxy('*', cmdbProxyOptions));

router.all('/xhr/flowServer/*', tigerProxy('*', flowServerOptions));

// bpmorder
router.all('/xhr/bpmOrder/*', tigerProxy('*', bpmOrderOptions));

router.all('/xhr/proxy/*', tigerProxy('*', reminders));
// 权限中心
export const serviceProxyOptions: ITigerProxyOption = {
    target: `http://127.0.0.1:${port}`,
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => path.replace(`${contextPath}/xhr/manual`, '')
};

// 自己的
router.all(`/xhr/manual/*`, async (ctx: any, next: any) => {
    if (ctx.openIDInfo.email === '<EMAIL>' || ctx.openIDInfo.email === '<EMAIL>' || ctx.openIDInfo.email === '<EMAIL>') {
        await tigerProxy('*', serviceProxyOptions)(ctx, next);
    } else {
        ctx.body = {
            code: 401,
            errorCode: '没有操作权限'
        };
    }
});

export default router.routes();
