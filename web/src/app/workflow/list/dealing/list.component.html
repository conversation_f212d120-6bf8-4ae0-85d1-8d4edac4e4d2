<div class="m-workflow-dealing layout-main">
    <!-- <div class="section-header section-header-bordered">
        <span class="section-header-title">待办工单</span>
    </div> -->
   
    <div class="section m-tag-box m-reset-width">
        <div class="section-header section-header-bordered">
            <span class="section-header-title">待办工单</span>
        </div>
        <div class="section-block margin-b-0x">
            <form class="form form-inline">
                <div class="form-item-group">
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label" [ngClass]="{'col-5': !isMobile, 'col-8': isMobile}">工单编号：</label>
                        <div class="form-item-control" [ngClass]="{'col-19': !isMobile, 'col-16': isMobile}">
                            <shark-input [(ngModel)]="searchData.flowId" [size]="'full'" [placeholder]="'请填写'"  name="flowId"></shark-input>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <label class="form-item-label" [ngClass]="{'col-5': !isMobile, 'col-8': isMobile}">创建人：</label>
                        <div class="form-item-control" [ngClass]="{'col-19': !isMobile, 'col-16': isMobile}">
                            <single-user-selector [(model)]="searchData.createUser" [size]="'full'" name="createUserEmail" ></single-user-selector>
                        </div>
                    </div>
                    <div class="form-item" [ngClass]="{'col-8': !isMobile, 'col-24': isMobile}">
                        <div class="form-item-control">
                            <button class="btn btn-primary" (click)="doSearch()">搜索</button>
                        </div>
                    </div>
                </div>
            </form>
            <div class="m-tag-row">
                <span>工单类型:</span>
                <ng-container *ngFor="let key of flowTyleAttrKeys;let index = index;">
                  <button *ngIf="workOrderSearchBtn[key].num !== 0 || key === WORKFLOW_TYPE.All" (click)="onClickType(key)"  type="button" [ngClass]="{'btn': true, 'btn-secondary': activeTypeBtn !== key, 'btn-primary': activeTypeBtn === key }" >{{workOrderSearchBtn[key].name}}({{workOrderSearchBtn[key].num}})</button>
                </ng-container>
            </div>
            <!-- 工单名称 -->
            <div class="m-tag-row">
              <span>工单名称:</span>
              <ng-container *ngFor="let item of workOrderSearchBtn[activeTypeBtn].todoNameList;let index = index;">
                  <button *ngIf="item.num !== 0 || index === 0" (click)="onClickName(item.id, index)"  type="button" 
                  [ngClass]="{'btn': true, 'btn-secondary': activeNameBtn !== item.id.toString(), 'btn-primary': activeNameBtn === item.id.toString() }">{{item.name}}({{item.num}})</button>
              </ng-container>
          </div>
        </div>
    </div>
    <!-- 列表List -->
    <div class="section m-reset-width" *ngIf="itemList.length === 0">
     
        <div class="no-data-tip">暂无数据</div>
       
    </div>
    <div *ngIf="itemList.length > 0">
        <div class="m-batch-operation m-reset-width" *ngIf="showBatchApproval">
            <!-- 全选按钮 -->
            <ng-container *ngIf="isBatchAudit">
                <shark-checkbox class="m-all-check" [(ngModel)]="pageOfApprovalStatus[pagination.page].withAll" (ngModelChange)="onSelectAll($event)"></shark-checkbox>
                <span class="m-allcheck padding-l-1x" (click)="onSelectAll()">
                    {{!pageOfApprovalStatus[pagination.page].withAll ? '全选' : '取消全选'}}
                    （已选{{selectedCount}}条）
                </span>
            </ng-container>
            <ng-container *ngIf="showBatchApproval">
                <div class="m-batch-btns">
                    <button *ngIf="isBatchAudit" type="button" (click)="onBatchOpera(false)" class="m-btn-ghost">取消批量审批</button>
                    <div *ngIf="!isBatchAudit else batchOpear" shark-tooltip [template]="'组织架构类工单支持批量审批，权限类工单由于系统角色差异性暂不支持批量审批'" [direction]="'top'">
                        <button type="button" (click)="onBatchOpera(true)" class="m-btn-primary">批量审批</button>
                    </div>
                    <ng-template #batchOpear>
                        <button  type="button"  class="m-btn-primary" (click)="onActionSelected()">批量处理</button>
                    </ng-template>
                </div>
            </ng-container>
        </div>
        <div class="m-list-content m-reset-width" *ngFor="let item of itemList;let index = index;">
            <!-- 单选按钮 只有组织是可被选的 其他应该是disabled的  可备选并且是全选状态 勾选 -->
            <shark-checkbox  *ngIf="isBatchAudit && showBatchApproval" 
                              [disabled]="!setCheckStatus(item)"
                              class="m-item-check" #checkItem [(ngModel)]="item.checked"
                              (ngModelChange)="handleCheckbox($event, index, item)"></shark-checkbox>
            <!-- 权限类型  -->
            <div class="section m-list-item position-relative" [ngClass]="{'padding-v-1x': isMobile}"  *ngIf="item.flowMetaData.topologyId === 'ius_special_rights' || item.flowMetaData.topologyId === 'ius_special_rights_child' || item.flowMetaData.topologyId === 'ius_org_identity_permit_topology' || item.flowMetaData.topologyId === 'ius_org_identity_permit_child_topology' || item.flowMetaData.topologyId === 'ius_personal_special_rights_child' || item.flowMetaData.topologyId === 'ius_ext_system_auth_apply' || item.flowMetaData.topologyId === 'ius_ext_system_auth_clear'">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x u-mobile-tag" (click)="detail(item)" *ngIf="!isMobile">
                    <div class="border-success text-success bg-success float-right padding-h-1x" style="display: inline-block;"  >
                        权限
                    </div>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">
                            <span>
                                <div class="border-success text-success bg-success padding-h-1x" style="display: inline-block;" *ngIf="isMobile"  >
                                    权限
                                </div>
                                {{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}
                            </span>
                            <div *ngIf="item.flowNodeMessage.urgeState === 1" class="disp-inline-block" shark-tooltip [template]="'催办中'" [direction]="'top'">
                                <img class="m-uegr-img" src="../../../../assets/img/urge.png">
                            </div>
                        </div>
                        <div class="m-line-height" [ngClass]="{'position-absolute': !isMobile}" [style.bottom]="isMobile ? '0' : '15px'">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号： {{item.flowMetaData.flowId}}</span></div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                    </div>
                    <div [ngClass]="{'col-15': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="m-line-height">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <div class="m-line-height" *ngIf="item.flowMetaData.topologyId != 'ius_ext_system_auth_clear'">
                            <span><span class="m-label-color">员工类型：</span>{{extraBussinessData[index]?.userType || '无'}}</span>
                            <span class="margin-l-4x"><span class="m-label-color">岗位: </span>{{extraBussinessData[index]?.workPost}}</span>
                        </div>
                        <!-- item.flowNodeMessage.businessObject.multiApplySystem -->
                        <div class="m-line-height">
                            <span class="m-label-color">部门：</span>
                            <span *ngIf="item.flowMetaData.topologyId != 'ius_ext_system_auth_clear'">{{extraBussinessData[index]?.department}}</span>
                            <span *ngIf="item.flowMetaData.topologyId == 'ius_ext_system_auth_clear'">{{item.flowNodeMessage.changeUserInfo.fullDeptName}}</span>
                        </div>
                        <div class="m-line-height text-truncate text-width" shark-tooltip [template]="item | muliapplypipe" [direction]="'bottom'">
                            <span class="m-label-color">权限需求：</span>{{ item | muliapplypipe }}
                        </div>
                        <div class="m-line-height text-error" *ngIf="item.flowNodeMessage?.forOtherMobileCheckRemark">{{item.flowNodeMessage?.forOtherMobileCheckRemark}}</div>
                    </div>
                    <div class="m-operating" [ngClass]="{'col-3': !isMobile, 'col-24': isMobile}">
                        <div style="width: 100%;">
                            <div class="text-center">操作
                                <i shark-tooltip [template]="'权限类工单暂不支持快捷审批，功能敬请期待中＾Ｏ＾'" [direction]="'top'" class="icon-questioncircleo"></i>
                            </div>
                            <div class="margin-t-2x">
                                <!-- <button class="btn btn-link" type="button" (click)="onAudit(true)">通过</button>
                                <button class="btn btn-link" type="button" (click)="onAudit(false)">驳回</button> -->
                                <a
                                    *ngIf="!!item.flowNodeMessage.systemUrl && (item.flowMetaData.topologyId === 'ius_ext_system_auth_apply' || item.flowMetaData.topologyId === 'ius_ext_system_auth_clear') && item.flowMetaData.currentNodeId === '30111005'"
                                    class="btn btn-link"
                                    [href]="item.flowNodeMessage.systemUrl"
                                    target="_blank">
                                    去外部系统处理
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 组织架构类型 -->
            <div class="section m-list-item position-relative"  *ngIf="item.flowMetaData.topologyId === 'ius_entry_apply_topology' || item.flowMetaData.topologyId === 'ius_org_identity_confirm_topology' || item.flowMetaData.topologyId === 'ius_hold_post_apply_topology' || item.flowMetaData.topologyId === 'ius_quit_apply_topology' || item.flowMetaData.topologyId === 'ius_transfer_apply_topology'" >
                <div style="width: 100px; position: relative; cursor: pointer;" class="padding-v-2x" (click)="detail(item)" *ngIf="!isMobile">
                    <div class="border-primary text-primary bg-primary float-right padding-h-1x" style="display: inline-block;" >
                        组织架构
                    </div>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}"  (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">
                            <span>
                                <div class="border-primary text-primary bg-primary padding-h-1x" style="display: inline-block;" *ngIf="isMobile"  >
                                    组织架构
                                </div>
                                {{item | topoauthnamepipe}}-{{getUserName(item)}}
                            </span>
                            <div *ngIf="item.flowNodeMessage.urgeState === 1" class="disp-inline-block"   shark-tooltip [template]="'催办中'" [direction]="'top'">
                                <img class="m-uegr-img" src="../../../../assets/img/urge.png">
                            </div>
                        </div>
                        <div class="m-line-height" [ngClass]="{'position-absolute': !isMobile}" [style.bottom]="isMobile ? '0' : '15px'">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span> </div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                    </div>
                    <div [ngClass]="{'col-15': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <!-- <div class="m-line-height">当前节点/审批人：部门负责人审批</div> -->
                        <div class="m-line-height">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>

                        
                        <div class="m-line-height">
                            <span><span class="m-label-color">员工类型：</span>{{extraBussinessData[index]?.userType || '无'}}</span>
                            <span class="margin-l-4x">
                                <span class="m-label-color">{{getWorkPostLabel(item.flowMetaData.topologyId)}}：</span>
                                {{getWorkPostValue(item.flowMetaData.topologyId, index)}}
                            </span>
                        </div>
                        <ng-container>
                            <div class="m-line-height">
                                <span class="m-label-color">{{getWorkDepartLabel(item.flowMetaData.topologyId)}}：</span>
                                {{getWorkDepartValue(item.flowMetaData.topologyId, index)}}
                            </div>
                            <div *ngIf="item.flowNodeMessage.businessObject?.remark; else notHaveRemark" class="m-line-height text-truncate text-width" shark-tooltip [template]="item.flowNodeMessage.businessObject.remark" [direction]="'bottom'">
                                <span class="m-label-color">{{formatRemarkLabel(item.flowMetaData.topologyId)}}：</span>
                                {{item?.flowNodeMessage?.businessObject?.remark}}
                            </div>
                            <ng-template #notHaveRemark>
                                <div class="m-line-height text-truncate text-width">
                                    <span class="m-label-color">{{formatRemarkLabel(item.flowMetaData.topologyId)}}：</span>
                                    {{item?.flowNodeMessage?.businessObject?.remark}}
                                </div>
                            </ng-template>
                        </ng-container>
                    </div>
                    <div class="m-operating" [ngClass]="{'col-3': !isMobile, 'col-24': isMobile}">
                        <div style="width: 100%;">
                            <div class="text-center" *ngIf="!isMobile">
                                操作
                            </div>
                            <div>
                                <button class="btn btn-link" type="button" (click)="onAudit(true, item)">通过</button>
                                <button class="btn btn-link" type="button" (click)="onAudit(false, item)">驳回</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

             <!-- 处理过的 老工单 有点疑问 属于组织架构类型 照抄之前-->
             <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'nav_personnelchange_onboard' || item.flowMetaData.topologyId === 'nav_personnelchange_departure' || item.flowMetaData.topologyId === 'nav_personnelchange_transfer' || item.flowMetaData.topologyId === 'ius_personal_special_rights' ">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)" *ngIf="!isMobile">
                    <div *ngIf="item.flowMetaData.topologyId === 'ius_personal_special_rights' else org1" class="border-success text-success bg-success float-right padding-h-1x" style="display: inline-block;" >
                        权限
                    </div>
                    <ng-template #org1>
                        <div #org1 class="border-primary text-primary bg-primary float-right padding-h-1x" style="display: inline-block;" >
                            组织架构
                        </div>
                    </ng-template>
                </div>
                <div class="row" [ngClass]="{'margin-l-1x': !isMobile, 'margin-1x': isMobile}" style="flex: 1">
                    <div [ngClass]="{'col-6': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="m-line-height">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span> </div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                    </div>
                    <div [ngClass]="{'col-15': !isMobile, 'col-24': isMobile, 'padding-v-2x': !isMobile, 'padding-v-0x': isMobile}" (click)="detail(item)" style="cursor: pointer;">
                        <div class="m-line-height" *ngIf="item.flowMetaData.topologyId === 'ius_personal_special_rights' else personal1">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.status === 200 ? '工单完结' :item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span *ngIf="item.flowMetaData.status !== 200">
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <ng-template #personal1>
                            <div class="m-line-height" #personal1>
                                <span *ngIf="item.flowNodeMessage?.businessStatus === 0"><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                                <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                                <span>
                                    <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                    <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                                </span>
                            </div>
                        </ng-template>
                        
                    </div>
                    <div class="col-3 m-operating" *ngIf="!isMobile">
                        <div style="width: 100%;">

                            <div class="text-center">操作</div>
                                <div style="display:inline-block;">
                                    <!-- <button
                                        *ngIf="item.flowNodeMessage?.business_status === -1 || item.flowNodeMessage?.businessStatus === -1 else other1"  
                                        class="btn btn-link" type="button" (click)="goEditOrder(item)">
                                        编辑
                                    </button>
                                    <ng-template #other1>
                                        <span>/</span>
                                    </ng-template> -->
                                </div>
                               
                            </div>
                        </div>
                </div>
            </div>
            <!-- 文档类型 照抄之前-->
            <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'written_doc_delete_project' || item.flowMetaData.topologyId === 'written_doc_project' ">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)">
                    <div class="border-error text-error bg-error float-right padding-h-1x" style="display: inline-block;" >
                        文档
                    </div>
                </div>
                <div class="row margin-l-1x" style="flex: 1">
                    <div class="col-6 padding-v-2x" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="m-line-height">
                            <div><span class="m-label-color">工单编号 {{item.flowMetaData.flowId}}</span></div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                    </div>
                    <div class="col-15 padding-v-2x" (click)="detail(item)" style="cursor: pointer;">
                        <div class="m-line-height" *ngIf="item.flowMetaData.status === 200 else end">
                            <span class="m-label-color">当前节点/审批人：</span>工单完结
                        </div>
                        <ng-template #end>
                            <div class="m-line-height">
                                <span *ngIf="item.flowNodeMessage?.businessStatus === 0"><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                                <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                                <span>
                                    <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                    <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                                </span>
                            </div>
                        </ng-template>
                    </div>
                    <div class="col-3 m-operating">
                        <div style="width: 100%;">

                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <button *ngIf="(item.flowMetaData.topologyId === 'written_doc_delete_project' && 
                                    item.flowMetaData.currentNodeId !== '70210301' &&
                                    item.flowMetaData.currentNodeId !== '70210303') || (
                                    item.flowMetaData.topologyId === 'written_doc_project' && 
                                    item.flowMetaData.currentNodeId !== '70210101')"
                                    class="btn btn-link" type="button" (click)="toDeal(item)">处理</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="section-block text-right" *ngIf="pagination.total > 0">
        <shark-pagination [page]="pagination.page" [totalPage]="pagination.totalPage" [pageSize]="pagination.size"
            (onSizeChanged)="onSizeChanged($event);" (onPageChanged)="onPageChanged($event);"></shark-pagination>
    </div>
</div>