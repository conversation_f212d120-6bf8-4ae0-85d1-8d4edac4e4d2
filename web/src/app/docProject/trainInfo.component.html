<div class="section" [ngClass]="{'section-fold': !switch}">
    <div class="section-header section-header-bordered" (click)="switch = !switch" style="cursor: pointer;">
        <span class="section-header-title">培训信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block" *ngIf="switch">

        <!-- 上传培训记录前 -->
        <div class="form-item"  *ngIf="!showTrainDoc && hasAuthority" style="margin-top:10px;">
            <label class="form-item-label col-2 margin-v-1x required text-left">培训记录：</label>
            <div class=" form-item col-4" style="margin-top:5px;">
                <div class="form-item-control">
                    <shark-x-fileupload [(ngModel)]="fileList" [url]="url" [maxFiles]=1
                        [filterPreSelected]="filterPreSelected" (onSelected)="onSelected($event)" (onFailed)="onFailed($event)">上传附件</shark-x-fileupload>
                </div>
            </div>
        </div>

        <!-- 上传培训记录后 -->
        <div class="form-item" *ngIf="showTrainDoc">
            <div class=" form-item col-16">
                <label class="form-item-label col-2 text-left" style="margin-right:15px;">培训记录：</label>
                <div class="form-item-control col-14">
                    <ul>
                        <!-- <li *ngFor='let file of fileList'>{{file.name}}&nbsp;&nbsp;&nbsp;  -->
                        <li style="margin-top:5px;" *ngFor="let file of fileList">{{file.name}}&nbsp;&nbsp;&nbsp;
                            <a href="/bflow/bflow-base/xhr/fms/downLoadFile.do?topic=downLoad&key={{file.key}}&name={{file.name}}"
                                download="filename">下载</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class=" form-item col-16">
                <label class="form-item-label col-3 text-left" style="margin-right:10px;">培训记录日期：</label>
                <label class="form-item-label col-3 text-left">{{trainTime  | dateFormat: 'yyyy-MM-dd HH:mm' }}</label>
            </div>
        </div>
    </div>
    <div class="text-center" style="margin-bottom: 50px">
        <button class="btn btn-secondary btn-secondary-primary" style="margin-right: 100px"
            (click)="dismiss()">返回</button>
        <button class="btn btn-primary" style="margin-right: 100px" (click)="submit()" *ngIf="!showTrainDoc && hasAuthority">提交</button>
    </div>

</div>