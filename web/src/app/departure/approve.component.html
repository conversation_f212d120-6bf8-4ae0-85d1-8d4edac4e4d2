<div class="section" [ngClass]="{'section-fold': !switch}">
    <div class="section-header section-header-bordered" (click)="setSwitch()" style="cursor: pointer;">
        <span class="section-header-title">工单审批</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block" *ngIf="switch">
        <form class="form  ng-untouched ng-pristine ng-valid" novalidate="" shark-validform #form="shark-validform">
            <div class="form-item">
                <label class="form-item-label col-4">是否通过：</label>
                <div class="form-item-control col-20">
                    <shark-radio-group [(model)]="approveData.approved" [radios]="approveChoices" shark-valid
                        [validRule]="'required'"></shark-radio-group>
                </div>
            </div>
            <div class="form-item" *ngIf="!approveData.approved">
                <label class="form-item-label col-4">备注信息：</label>
                <div class="form-item-control col-20">
                    <textarea class="input input-textarea input-w-lg ng-untouched ng-pristine ng-valid"
                        name="operateRemark" [(ngModel)]="approveData.operateRemark" placeholder="温馨提示：如不通过，对于争议的内容，建议先线下与文件拟制人沟通审核建议" shark-valid
                        [validRule]="'required'"></textarea>
                </div>
            </div>
            <div class="form-item" *ngIf="approveData.approved">
                <label class="form-item-label col-4">备注信息：</label>
                <div class="form-item-control col-20">
                    <textarea class="input input-textarea input-w-lg ng-untouched ng-pristine ng-valid"
                        name="operateRemark" [(ngModel)]="approveData.operateRemark" placeholder="温馨提示：如不通过，对于争议的内容，建议先线下与文件拟制人沟通审核建议"></textarea>
                </div>
            </div>
            <div class="form-item">
                <div class="form-item-control col-offset-4 col-12">
                    <button class="btn btn-primary" [disabled]="submiting" (click)="save()">确定</button>
                </div>
            </div>
        </form>
    </div>
</div>