@import '~@shark/shark-css/scss/sharkpure';

.word-break {
    white-space: normal !important;
    word-break: break-all !important;
}

.toastr-message-withlink {
    float: left;
    display: block;
    max-width: 280px;
}
.ant-layout-sider-collapsed{
    flex: 0 0!important;
    max-width: 0!important;
    min-width: 0!important;
    width: 0!important;
}
@media print {
    .noprint {
        display: none;
    }
    html {
        overflow: auto;
        height: auto;
        body {
            overflow: auto;
            height: auto;
        }
        .layout {
            height: auto;
        }

        .layout-header {
            display: none;
        }
        .section-header .section-header-title {
            font-size: 16px;
            height: 24px;
            line-height: 24px;
        }
        .layout .layout-body .layout-aside {
            display: none;
        }
        .layout .layout-body .layout-main {
            padding: 0;
            min-width: auto;
            width: 720px;
        }
        .layout .layout-body {
            position: relative;
            left: 0;
            top: 0;
        }
        .section-header {
            padding: 5px 10px;
        }
        .section-block:last-child {
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 10px;
        }
    }
}

.no-data-tip {
    padding: 20px;
    font-weight: bold;
}

@media (max-width: 980px) {
    .layout .layout-body .layout-main {
        padding: 4px;
        min-width: 100%;
    }
}
