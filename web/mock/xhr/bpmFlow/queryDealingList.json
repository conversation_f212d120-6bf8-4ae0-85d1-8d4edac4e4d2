{"code": 200, "data": {"pagination": {"page": 1, "size": 10, "total": 106, "totalPage": 11, "offset": 0}, "result": [{"flowMetaData": {"product": "ius", "createTime": 1612249702072, "nextNodeList": ["30110702"], "globalId": "OSR_1612249701900", "topologyId": "ius_org_identity_permit_topology", "updateTime": 1612249704825, "nodeId": "30110701", "flowId": "11718063", "version": 2, "latest": true, "status": 100, "topologyName": "ius_org_identity_permit_topology", "currentNodeId": "30110702"}, "flowNodeMessage": {"orderType": 7, "code": "organized_permit_create", "workOrderType": "organized_permit_create", "businessStatus": 1, "createUserName": "张佳俊", "haveChildFlow": false, "currUserName": "张佳俊", "businessObject": {"specialTrackList": [], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "aaa", "key": "a2f4a71b-07ea-42fb-f79a-eab12f0dd19b"}, {"applySystem": "icac", "applySystemName": "内控工作台系统", "applySystemDesc": "ssss", "key": "f20c3119-aec8-4cae-f818-051b0afb96d9"}, {"applySystem": "navAdmin", "applySystemName": "严选门户管理平台", "applySystemDesc": "aaws", "key": "ea426a4a-61e0-4323-8333-39fc23cbbcca"}, {"applySystem": "ds", "applySystemName": "DataSmart", "applySystemDesc": "ss", "key": "0ae25dfa-bcf1-49cb-90a6-a34892907e21"}], "identityList": "[{\"departmentName\":\"测试部门\",\"employeeType\":\"0\",\"post\":\"-1\",\"level\":98,\"postName\":\"全部\",\"employeeTypeName\":\"正式员工\",\"id\":76,\"department\":306,\"key\":\"650ac8de-147d-4a7c-e834-952e422d9f4b\"}]", "notifyedList": [null, "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>"]}, "operateResult": "agree", "trackList": [], "currUid": "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "OSR_1612249701900", "nodeId": "30110702", "flowId": "11718063", "createUid": "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"}}, {"flowMetaData": {"product": "navAdmin", "createTime": 1610968740957, "nextNodeList": ["30010303"], "globalId": "navAdmin_1610968740797", "topologyId": "nav_personnelchange_onboard", "updateTime": 1611198257277, "nodeId": "30010304", "flowId": "11610488", "version": 3, "latest": true, "status": 100, "topologyName": "nav_personnelchange_onboard", "currentNodeId": "30010303"}, "flowNodeMessage": {"applerPhoneNum": "***********", "applerType": 2, "parentOrgPosId": 306, "applerUid": "<EMAIL>", "level": 97, "trackFullList": [], "createUserName": "王士瑜", "businessStatus": 0, "updateTime": 1611198257243, "orgPosId": 601, "dutyDesc": "软件测试", "applerName": "王士瑜", "observerList": ["<EMAIL>", "<EMAIL>", "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "applerRoleType": 0, "orgPosPath": "严选事业部>测试部门>质量组", "trackList": ["<EMAIL>", "wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "createTime": 1610968740797, "acceptorNameList": ["wb.<PERSON><PERSON><PERSON><PERSON><PERSON>"], "createUser": "<EMAIL>", "orgPosName": "质量组", "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"]}}, {"flowMetaData": {"product": "ius", "createTime": 1611023274538, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1611023274349", "topologyId": "ius_special_rights", "updateTime": 1611023279081, "nodeId": "30110101", "flowId": "11611901", "version": 2, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "曹健", "haveChildFlow": false, "currUserName": "曹健", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "test", "key": "ebace1ef-6010-41fc-f0a4-fc0bc2906254"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1611023274349", "nodeId": "30110102", "flowId": "11611901", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1610000497274, "nextNodeList": ["30110602"], "globalId": "HOLDPOST_1610000497103", "topologyId": "ius_hold_post_apply_topology", "updateTime": 1610000498631, "nodeId": "30110601", "flowId": "11538392", "version": 1, "latest": true, "status": 100, "topologyName": "ius_hold_post_apply_topology", "currentNodeId": "30110602"}, "flowNodeMessage": {"orderType": 5, "code": "holdposts_create", "workOrderType": "holdposts_create", "businessStatus": 1, "createUserName": "王亚君", "haveChildFlow": false, "currUserName": "王亚君", "businessObject": {"employeeNum": "H6000", "staffStatus": 0, "oaDepartment": "严选事业部-技术产品中心-基础技术部-中台技术组", "remark": "", "uid": "<EMAIL>", "employeeType": "", "leaderName": "王国云", "identityList": "[{\"key\":\"3a6455ca-b23f-481e-c85f-f7c58101c432\",\"departmentName\":\"测试部门\",\"department\":\"306\",\"level\":98,\"postName\":\"测试岗位-test0828\",\"post\":208},{\"key\":\"9c59e651-74c5-4728-d822-a728a03a3308\",\"departmentName\":\"测试部门\",\"department\":\"504\",\"level\":96,\"postName\":\"测试111\",\"post\":198},{\"key\":\"5d0c44da-4d12-4a12-a459-31dd84e74198\",\"departmentName\":\"中台技术组\",\"department\":\"569\",\"level\":96,\"postName\":\"四级部门负责人\",\"post\":4},{\"key\":\"f21d175e-27f8-45f2-c6bf-fd309b8fefbd\",\"departmentName\":\"中台技术组\",\"department\":\"569\",\"level\":96,\"postName\":\"产品经理\",\"post\":214}]", "effectTime": 1609948800411, "leaderUid": "<EMAIL>", "oaState": 1, "username": "王亚君", "applyedIdentityList": "[{\"key\":\"a7d52864-bbea-47ab-f55d-f5f2f71730fe\",\"department\":58,\"departmentName\":\"严选事业部 / 品控中心\",\"post\":\"212\",\"postName\":\"9.9岗位2\",\"level\":98}]"}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "HOLDPOST_1610000497103", "nodeId": "30110602", "flowId": "11538392", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1609919029692, "nextNodeList": ["30110602"], "globalId": "HOLDPOST_1609919029531", "topologyId": "ius_hold_post_apply_topology", "updateTime": 1609919031223, "nodeId": "30110601", "flowId": "11527280", "version": 1, "latest": true, "status": 100, "topologyName": "ius_hold_post_apply_topology", "currentNodeId": "30110602"}, "flowNodeMessage": {"orderType": 5, "code": "holdposts_create", "workOrderType": "holdposts_create", "businessStatus": 1, "createUserName": "胡尧", "haveChildFlow": false, "currUserName": "胡尧", "businessObject": {"employeeNum": "H21948", "staffStatus": 0, "oaDepartment": "严选事业部-技术产品中心-基础技术部-中台技术组", "remark": "", "uid": "<EMAIL>", "employeeType": "", "leaderName": "王国云", "identityList": "[{\"key\":\"267000ed-3384-4cdf-b113-3810316c3800\",\"departmentName\":\"邮件事业部\",\"department\":\"588\",\"level\":96,\"postName\":\"高级开发\",\"post\":6}]", "effectTime": 1609948800000, "leaderUid": "<EMAIL>", "oaState": 1, "username": "胡尧", "applyedIdentityList": "[{\"key\":\"3f92f443-062c-4cea-9c56-23251c0bb216\",\"department\":743,\"departmentName\":\"严选事业部 / 技术产品中心 / 业务研发部 / 平台前端组\",\"post\":0,\"postName\":\"无\",\"level\":96}]"}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "HOLDPOST_1609919029531", "nodeId": "30110602", "flowId": "11527280", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1609918398781, "nextNodeList": ["30110604"], "globalId": "HOLDPOST_1609918398620", "topologyId": "ius_hold_post_apply_topology", "updateTime": 1609918511832, "nodeId": "30110603", "flowId": "11530005", "version": 5, "latest": true, "status": 100, "topologyName": "ius_hold_post_apply_topology", "currentNodeId": "30110604"}, "flowNodeMessage": {"orderType": 5, "code": "holdposts_create", "workOrderType": "holdposts_create", "businessStatus": 1, "createUserName": "胡尧", "haveChildFlow": false, "currUserName": "胡尧", "businessObject": {"employeeNum": "H21948", "staffStatus": 0, "oaDepartment": "严选事业部-技术产品中心-基础技术部-中台技术组", "remark": "", "uid": "<EMAIL>", "employeeType": "", "leaderName": "王国云", "identityList": "[{\"key\":\"0451cad4-8357-4d1e-a0f8-1b1f71a4d1e4\",\"departmentName\":\"邮件事业部\",\"department\":\"588\",\"level\":96,\"postName\":\"高级开发\",\"post\":6}]", "effectTime": 1609862400000, "leaderUid": "<EMAIL>", "oaState": 1, "username": "胡尧", "applyedIdentityList": "[{\"key\":\"3275f9c0-f1a6-4756-d58f-aabf700f5044\",\"department\":743,\"departmentName\":\"严选事业部 / 技术产品中心 / 业务研发部 / 平台前端组\",\"post\":0,\"postName\":\"无\",\"level\":96}]"}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": ["张佳俊"], "conditionParamMap": {"employeeNum": "H21948", "staffStatus": 0, "inSecondaryDepartment": false, "oaDepartment": "严选事业部-技术产品中心-基础技术部-中台技术组", "remark": "", "uid": "<EMAIL>", "employeeType": "", "operateResult": "agree", "leaderName": "王国云", "isHaveChildFlow": false, "identityList": "[{\"key\":\"0451cad4-8357-4d1e-a0f8-1b1f71a4d1e4\",\"departmentName\":\"邮件事业部\",\"department\":\"588\",\"level\":96,\"postName\":\"高级开发\",\"post\":6}]", "effectTime": 1609862400000, "leaderUid": "<EMAIL>", "oaState": 1, "username": "胡尧", "applyedIdentityList": "[{\"key\":\"3275f9c0-f1a6-4756-d58f-aabf700f5044\",\"department\":743,\"departmentName\":\"严选事业部 / 技术产品中心 / 业务研发部 / 平台前端组\",\"post\":0,\"postName\":\"无\",\"level\":96}]"}, "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "HOLDPOST_1609918398620", "nodeId": "30110604", "flowId": "11530005", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1609311306069, "nextNodeList": ["30110603"], "globalId": "HOLDPOST_1609311305901", "topologyId": "ius_hold_post_apply_topology", "updateTime": 1609311366977, "nodeId": "30110602", "flowId": "11489363", "version": 3, "latest": true, "status": 100, "topologyName": "ius_hold_post_apply_topology", "currentNodeId": "30110603"}, "flowNodeMessage": {"orderType": 5, "code": "holdposts_create", "workOrderType": "holdposts_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "胡尧", "businessObject": {"uid": "<EMAIL>", "employeeNum": "DS0429", "employeeType": 2, "staffStatus": 2, "identityList": "[{\"key\":\"b4a33c12-3cae-43dc-89b2-43811c276fed\",\"departmentName\":\"中台技术组\",\"department\":\"308\",\"level\":97,\"postName\":\"测试岗位1\",\"post\":221}]", "remark": "fff", "effectTime": 1609257600000, "oaState": "离职", "username": "丁应应", "applyedIdentityList": "[{\"key\":\"f23a8503-09cb-4516-9c8d-6eab3df9e809\",\"department\":791,\"departmentName\":\"测试数据验证\",\"post\":\"216\",\"postName\":\"testabc\",\"level\":98},{\"key\":\"f92852d0-5d8f-43ae-a549-00f9dd5e5f96\",\"department\":791,\"departmentName\":\"测试数据验证\",\"level\":98,\"post\":\"6\",\"postName\":\"高级开发\"}]"}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "ffff", "acceptorNameList": ["张佳俊"], "conditionParamMap": {"employeeNum": "DS0429", "staffStatus": 2, "inSecondaryDepartment": false, "remark": "fff", "uid": "<EMAIL>", "employeeType": 2, "operateResult": "agree", "isHaveChildFlow": false, "identityList": "[{\"key\":\"b4a33c12-3cae-43dc-89b2-43811c276fed\",\"departmentName\":\"中台技术组\",\"department\":\"308\",\"level\":97,\"postName\":\"测试岗位1\",\"post\":221}]", "effectTime": 1609257600000, "oaState": "离职", "username": "丁应应", "applyedIdentityList": "[{\"key\":\"f23a8503-09cb-4516-9c8d-6eab3df9e809\",\"department\":791,\"departmentName\":\"测试数据验证\",\"post\":\"216\",\"postName\":\"testabc\",\"level\":98},{\"key\":\"f92852d0-5d8f-43ae-a549-00f9dd5e5f96\",\"department\":791,\"departmentName\":\"测试数据验证\",\"level\":98,\"post\":\"6\",\"postName\":\"高级开发\"}]"}, "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "HOLDPOST_1609311305901", "nodeId": "30110603", "flowId": "11489363", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1609310584021, "nextNodeList": ["30110602"], "globalId": "HOLDPOST_1609310583849", "topologyId": "ius_hold_post_apply_topology", "updateTime": 1609310585589, "nodeId": "30110601", "flowId": "11489121", "version": 1, "latest": true, "status": 100, "topologyName": "ius_hold_post_apply_topology", "currentNodeId": "30110602"}, "flowNodeMessage": {"orderType": 5, "code": "holdposts_create", "workOrderType": "holdposts_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"uid": "<EMAIL>", "employeeNum": "DS0429", "employeeType": 2, "staffStatus": 2, "identityList": "[{\"key\":\"a4d22c1d-732f-4800-cee8-f5618a19ce02\",\"departmentName\":\"中台技术组\",\"department\":\"308\",\"level\":97,\"postName\":\"三级部门负责人\",\"post\":3}]", "remark": "xxx", "effectTime": 1609257600000, "oaState": "离职", "username": "丁应应", "applyedIdentityList": "[{\"key\":\"c9d3fef2-01c5-4589-9608-373f9de4c51b\",\"department\":585,\"departmentName\":\"测试新增2\",\"post\":\"210\",\"postName\":\"资深开发岗\",\"level\":96}]"}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "HOLDPOST_1609310583849", "nodeId": "30110602", "flowId": "11489121", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1609307928597, "nextNodeList": ["30110604"], "globalId": "HOLDPOST_1609307928433", "topologyId": "ius_hold_post_apply_topology", "updateTime": 1609307947936, "nodeId": "30110602", "flowId": "11488379", "version": 3, "latest": true, "status": 100, "topologyName": "ius_hold_post_apply_topology", "currentNodeId": "30110604"}, "flowNodeMessage": {"orderType": 5, "code": "holdposts_create", "workOrderType": "holdposts_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "胡尧", "businessObject": {"uid": "<EMAIL>", "employeeNum": "DS0429", "employeeType": 2, "staffStatus": 2, "identityList": "[{\"key\":\"82b7256a-fcf0-48c0-c81b-637a8f5620e2\",\"departmentName\":\"中台技术组\",\"department\":\"308\",\"level\":97,\"postName\":\"无\",\"post\":0},{\"key\":\"696a987d-cf2a-460f-e86f-21bdce9fdd49\",\"departmentName\":\"邮件事业部\",\"department\":\"588\",\"level\":96,\"postName\":\"普通员工\",\"post\":4},{\"key\":\"285e05cb-1bd3-4698-c6f9-d03d8edc7833\",\"departmentName\":\"测试数据验证\",\"department\":\"791\",\"level\":98,\"postName\":\"资深开发\",\"post\":9}]", "remark": "vvv", "effectTime": 1609257600000, "oaState": "离职", "username": "丁应应", "applyedIdentityList": "[{\"key\":\"59bc3e68-e5bc-4bb0-dd1e-c29bef8d8e75\",\"department\":585,\"departmentName\":\"测试新增2\",\"post\":\"210\",\"postName\":\"资深开发岗\",\"level\":96}]"}, "operateResult": "agree", "trackList": [], "currUid": "<EMAIL>", "operateRemark": "", "acceptorNameList": ["张佳俊"], "conditionParamMap": {"employeeNum": "DS0429", "staffStatus": 2, "inSecondaryDepartment": true, "remark": "vvv", "uid": "<EMAIL>", "employeeType": 2, "operateResult": "agree", "isHaveChildFlow": false, "identityList": "[{\"key\":\"82b7256a-fcf0-48c0-c81b-637a8f5620e2\",\"departmentName\":\"中台技术组\",\"department\":\"308\",\"level\":97,\"postName\":\"无\",\"post\":0},{\"key\":\"696a987d-cf2a-460f-e86f-21bdce9fdd49\",\"departmentName\":\"邮件事业部\",\"department\":\"588\",\"level\":96,\"postName\":\"普通员工\",\"post\":4},{\"key\":\"285e05cb-1bd3-4698-c6f9-d03d8edc7833\",\"departmentName\":\"测试数据验证\",\"department\":\"791\",\"level\":98,\"postName\":\"资深开发\",\"post\":9}]", "effectTime": 1609257600000, "oaState": "离职", "username": "丁应应", "applyedIdentityList": "[{\"key\":\"59bc3e68-e5bc-4bb0-dd1e-c29bef8d8e75\",\"department\":585,\"departmentName\":\"测试新增2\",\"post\":\"210\",\"postName\":\"资深开发岗\",\"level\":96}]"}, "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "HOLDPOST_1609307928433", "nodeId": "30110604", "flowId": "11488379", "createUid": "<EMAIL>"}}, {"flowMetaData": {"product": "ius", "createTime": 1609234234663, "nextNodeList": ["30110102"], "globalId": "IUS_SPECIAL_1609234234495", "topologyId": "ius_special_rights", "updateTime": 1609234238298, "nodeId": "30110101", "flowId": "11482594", "version": 2, "latest": true, "status": 100, "topologyName": "ius_special_rights", "currentNodeId": "30110102"}, "flowNodeMessage": {"orderType": 0, "code": "personal_permit_create", "workOrderType": "personal_permit_create", "businessStatus": 1, "createUserName": "丁应应", "haveChildFlow": false, "currUserName": "丁应应", "businessObject": {"specialTrackList": ["<EMAIL>", "<EMAIL>"], "multiApplySystem": [{"applySystem": "cradle", "applySystemName": "中台治理平台", "applySystemDesc": "ffff", "key": "456efb3e-4467-4af4-ab5c-79633ef26cde"}], "notifyedList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com", "<EMAIL>", "<EMAIL>"]}, "operateResult": "agree", "trackList": ["<EMAIL>", "<EMAIL>"], "currUid": "<EMAIL>", "operateRemark": "第一个节点自动通过", "acceptorNameList": ["张佳俊"], "acceptorList": ["wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com"], "workOrderId": "IUS_SPECIAL_1609234234495", "nodeId": "30110102", "flowId": "11482594", "createUid": "<EMAIL>"}}]}}