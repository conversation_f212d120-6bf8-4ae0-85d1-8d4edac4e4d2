<div class="section" [ngClass]="{'section-fold': !uiModel.switch}">
    <div class="section-header section-header-bordered" style="cursor: pointer;"
        (click)="uiModel.switch = !uiModel.switch">
        <span class="section-header-title">工单信息</span>
        <span class="disp-inline-block margin-l-2x" *ngIf="uiModel.switch">
            <i class="icon-downcircleo"></i>
        </span>
        <span class="disp-inline-block margin-l-2x" *ngIf="!uiModel.switch">
            <i class="icon-upcircleo"></i>
        </span>
    </div>
    <div class="section-block">
        <form class="form form-horizontal" shark-validform #form="shark-validform" autocomplete="off">
            <div class="form-item" *ngIf="!isInstructor">
                <label class="form-item-label col-4">
                    申请人：
                </label>
                <div class="form-item-text col-14">
                    {{showData.appler}}
                </div>
            </div>
            <div class="form-item" *ngIf="isInstructor">
                <label class="form-item-label col-4">
                    指导员代发起离职帐号：
                </label>
                <div class="form-item-control col-12">
                    <shark-x-autocomplete [(ngModel)]="showData.rewriteAppler" name="applerSelect"
                        (onSelected)="dataNotify($event)" [size]="'full'" [filterData]="filterApplerData"
                        [debounceTime]=500 [focusSearch]=true shark-valid [validRule]="'required'">
                    </shark-x-autocomplete>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">
                    所在组织架构：
                </label>
                <div class="form-item-control col-14">
                    <div *ngFor="let item of showData.orgPosTreeList">{{item}}</div>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">离职说明：</label>
                <div class="form-item-control col-12">
                    <shark-textarea [(model)]="flowData.flowNodeMessage.remark" [size]="'full'" [maxLength]=200
                        [placeholder]="'离职说明'" shark-valid [validRule]="'required'"></shark-textarea>
                </div>
            </div>
            <div class="form-item">
                <label class="form-item-label col-4">跟踪者：</label>
                <div class="form-item-control col-16">
                    <user-select-result-list [(userList)]="userList" [placeholder]="'请选择对应的用户'">
                    </user-select-result-list>
                    <button class="btn btn-primary btn-sm" type="button" (click)="selectUsers()">添加跟踪者</button>
                </div>
            </div>

            <div class="form-item" *ngIf="cmdbRoleText!=''">
                <label class="form-item-label col-4"></label>
                <div class="form-item-text col-16 text-error vertical-baseline">
                    <i class="icon-exclamationcircle text-error"></i>&nbsp;{{cmdbRoleText}}，请立即前往 cmdb 进行交接！&nbsp; => &nbsp;&nbsp;<a href="http://yx.mail.netease.com/cmdb#/service/list">CMDB <i class="icon-link text-primary"></i></a>
                </div>
            </div>

            <div class="form-item">
                <div class="col-offset-4 col-20">
                    <button class="btn btn-primary" type="button" (click)="save()" [disabled]="pedding">提交</button>
                </div>
            </div>
        </form>
    </div>
</div>