import {
    GetMapping,
    RequestMapping,
    RestController
} from '@tiger/boot';
import { AjaxResult, QueryContext, RequestContext } from '@tiger/core';
import { contextPath, xhrPrefix } from '../../conf';
import { UserService } from './user.service';

interface IOpenIDInfo {
    /**
     * 姓名
     */
    fullname: string;
    /**
     * 邮箱地址
     */
    email: string;
}

/**
 * 用户信息
 */
@RestController
@RequestMapping(`${contextPath}${xhrPrefix}/user`)
export class UserController {
    constructor(private userService: UserService){
    }

    @GetMapping('/getUserInfo.json')
    public async list(ctx: QueryContext<null, AjaxResult<IOpenIDInfo>>) {
        ctx.body = AjaxResult.success(ctx.openIDInfo);
    }

    @GetMapping('/getDetailUserInfo.do')
    public async getDetailUserInfo(ctx: QueryContext<any, AjaxResult<IOpenIDInfo>>) {
        let userInfo: any;
        try {
            userInfo = await this.userService.getDetailUserInfo(
                ctx.query.uid
            );
            return (ctx.body = AjaxResult.success(userInfo));
        } catch (e) {
            return (ctx.body = AjaxResult.badRequest(e));
        }
    }

}