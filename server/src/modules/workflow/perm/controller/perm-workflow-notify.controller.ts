import { MpsPayload } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { isNullOrUndefined } from 'util';
import ChildWorkflow from '../../permChild/perm-workflow';
import WorkflowQuery from '../../workflow-query';
import { topologyName } from '../perm-config';
import Workflow from '../perm-workflow';

export interface Notify {
    payload: string;
}
/**
 * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
 */
export type NotifyBody = Notify[];

export type NotifyResult = AjaxResult<string>;

const system = { email: 'system', fullname: '系统' };
/**
 * 工单通知接口
 */
@RestController
@RequestMapping(`/workflow/${topologyName}`)
export class PermWorkflowNotifyController {

    constructor(
        private workflow: Workflow,
        private childWorkflow: ChildWorkflow,
        private query: WorkflowQuery
    ) { }

    /**
     * 流程流转通知
     * [{"payload":"{\"flowId\": \"4668724\",\"nodeId\":\"30019902\"}"}]
     */
    @PostMapping('/notify.json')
    async notify(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body;
        const promiseValue: any[] = [];
        let subList: string[] = [];
        const submitData: any[] = [];

        for (const message of messages) {
            const payload: MpsPayload = JSON.parse(message.payload);
            const createrKeywords = '您的工单状态发生变更';
            const keywords = '业务系统权限申请';
            const { flowMetaData, flowNodeMessage } = await this.workflow.getFlowDataTriggerByMps(payload);
            const userInfo = {
                email: flowNodeMessage.createUser,
                fullname: flowNodeMessage.createUserName
            };

            // 当三级部门负责人/二级部门负责人审批通过后，需要手动为用户创建业务系统的子工单
            // 因为submit接口只返回nextNodeId 所以需要先upsert获取到flowID之后再submit触发工单流转
            if (flowMetaData.currentNodeId === '30010504') {
                const subFlowData = {
                    flowMetaData: {
                        flowId: '',
                        version: 0,
                        currentNodeId: ''
                    },
                    flowNodeMessage: {
                        createUser: flowNodeMessage.createUser,
                        orgPosId: flowNodeMessage.orgPosId,
                        createUserName: flowNodeMessage.createUserName,
                        applicantProductList: flowNodeMessage.applicantProductList,
                        applerUid: flowNodeMessage.createUser,
                        applerName: flowNodeMessage.createUserName,
                        approved: true,
                        operateRemark: '系统自动创建',
                        parentFlowId: flowMetaData.flowId,
                        underThirdLevel: flowNodeMessage.underThirdLevel,
                        duty: flowNodeMessage.duty,
                        employeeNum: flowNodeMessage.employeeNum,
                        createUserOrg: flowNodeMessage.createUserOrg,
                        originOrgPos: flowNodeMessage.originOrgPos,
                        businessStatus: 0
                    }
                };
                // upsert和submit数据隔离
                subFlowData.flowNodeMessage.applicantProductList.forEach((element: any, index: number) => {
                    const upserFlowNodeMessage = JSON.parse(JSON.stringify(subFlowData.flowNodeMessage));

                    upserFlowNodeMessage.applicantProductList = [element];
                    upserFlowNodeMessage.sensitiveLevel = element.sensitiveLevel;
                    upserFlowNodeMessage.originOrgPos = subFlowData.flowNodeMessage.originOrgPos;
                    element.belongOrgPosId ? upserFlowNodeMessage.productBelong = true : upserFlowNodeMessage.productBelong = false;

                    promiseValue.push(this.childWorkflow.upsert(userInfo, subFlowData.flowMetaData, upserFlowNodeMessage));
                });

                // 生成子工单
                subList = await Promise.all(promiseValue);
                // 创建成功后更新工单流转
                subFlowData.flowNodeMessage.applicantProductList.forEach((element: any, index: number) => {
                    const submitFlowNodeMessage = JSON.parse(JSON.stringify(subFlowData.flowNodeMessage));
                    const submitMetaData = {
                        flowId: subList[index],
                        currentNodeId: '30010512',
                        version: -1
                    };

                    submitFlowNodeMessage.applicantProductList = [element];
                    submitFlowNodeMessage.sensitiveLevel = element.sensitiveLevel;
                    submitFlowNodeMessage.originOrgPos = subFlowData.flowNodeMessage.originOrgPos;
                    submitFlowNodeMessage.productBelong = !!element.belongOrgPosId;
                    submitFlowNodeMessage.operateRemark = '自动提交子工单';

                    submitData.push(this.childWorkflow.submit(userInfo, submitMetaData, submitFlowNodeMessage));
                });
                await Promise.all(submitData);
            }
            // 子工单创建成功后，将子工单flowId填入父工单subList字段中
            if (subList.length) {
                flowNodeMessage.subList = subList;
            }
            // 更新工单数据
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
            await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, createrKeywords);
            // 否则，通知审批人
            await this.workflow.notifyApprover(flowMetaData, flowNodeMessage, keywords, {
                html: 'bflow_workflow_perm_notify_html',
                text: 'bflow_workflow_perm_notify_text'
            });

        }
        return ctx.body = AjaxResult.success();

    }

    /**
     * 流程结束通知
     * [{"payload": "{\"flowId\":\"4392104\",\"nodeId\":\"30010301\"}"}]
     */
    @PostMapping('/notifyEnd.json')
    async notifyEnd(ctx: RequestContext<NotifyBody, NotifyResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }

        const messages = ctx.request.body[0];
        const payload: MpsPayload = JSON.parse(messages.payload);
        let flowMetaData = null;
        let flowNodeMessage = null;

        try {
            const flowData = await this.workflow.getFlowDataTriggerByMps(payload);
            flowMetaData = flowData.flowMetaData;
            flowNodeMessage = flowData.flowNodeMessage;
            // 更新业务状态
            flowNodeMessage.businessStatus = 1;
            await this.workflow.update(flowMetaData, flowNodeMessage, system);
        } catch (e) {
            console.error('结束节点数据更新失败', e);
            return ctx.body = AjaxResult.internal('工单更新失败');
        }

        const keywords = '审批结束';
        await this.workflow.notifyCreater(flowMetaData, flowNodeMessage, keywords);
        // 通知跟踪人
        // await this.workflow.notifyTrailer(flowMetaData, flowNodeMessage, '工单已经结束');
        ctx.body = AjaxResult.success();
    }

}
