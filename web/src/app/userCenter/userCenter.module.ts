import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import {UserCenterAjax} from './services/ajax.service';
import {UserCenterService} from './services/userCenter.service';
import {UserCenterComponent} from './userCenter.component';

import {AppConfig} from '../config'
const routers: any[] = [
    {
        path: 'userCenter',
        component: UserCenterComponent
    }
];

const services: any[] = [UserCenterAjax, UserCenterService];

const components = [UserCenterComponent];

@NgModule({
    imports: [SharedModule, RouterModule.forChild(routers)],
    exports: [],
    declarations: [...components],
    providers: [...services]
})
export class UserCenterModule {
    constructor(private userCenterAjax: UserCenterAjax) {
        this.userCenterAjax.setContextPath(AppConfig.contextPath + '/xhr/icac');
        this.userCenterAjax.setFilterCode((result, type) => {
            return result.code === 200;
        });
        this.userCenterAjax.setFilterData((result, type) => {
            return result.data;
        });
    }
}
