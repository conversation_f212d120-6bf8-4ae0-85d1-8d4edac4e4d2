import { FlowData, FlowSubmitData } from '@eagle/workflow-node';
import { PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import openid from '@tiger/openid';
import { isNullOrUndefined } from 'util';
import { getUpperOrgListByOrgPosId } from '../../../service/permcenter/unitOrg';
import WorkflowQuery from '../../workflow-query';
import { topologyName } from '../writtendoc-config';
import Workflow from '../writtendoc-workflow';
import { contextPath } from '../../../../conf';

type SubmitResult = AjaxResult<string[]>;
type UpsertResult = AjaxResult<string>;

interface QueryOrgName {
    orgPosIds: string[];
}

interface OrgResult {
    orgPosId: string;
    orgPosName: string;
}

/**
 * 工单创建与提交
 */
@RestController
@RequestMapping(`${contextPath}/xhr/workflow/${topologyName}`)
export default class WrittendocWorkflowController {

    constructor(private workflow: Workflow, private workflowQuery: WorkflowQuery) {
    }

    @PostMapping('/getOrgName.do')
    async getOrgName(ctx: RequestContext<QueryOrgName, AjaxResult<OrgResult[]>>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const orgPosIds: string[] = ctx.request.body.orgPosIds;
        const orgResults: OrgResult[] = [];

        for (const i of orgPosIds) {
            const iOrgList = await getUpperOrgListByOrgPosId(Number.parseInt(i, 10));
            await iOrgList.forEach((iOrg: { orgPosId: number; orgPosName: string; }) => {
                if (iOrg.orgPosId === Number.parseInt(i, 10)) {
                    const orgResult: OrgResult = {
                        orgPosId: i,
                        orgPosName: iOrg.orgPosName
                    };
                    orgResults.push(orgResult);
                    return;
                }
            });
        }
        ctx.body = AjaxResult.success(orgResults);
    }

    /**
     * 工单创建并提交，触发流程流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/submit.json')
    async submit(ctx: RequestContext<FlowSubmitData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        let { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;

        try {
            const flowData: FlowData = await this.workflowQuery.detailQuery(flowMetaData.flowId, flowMetaData.currentNodeId);
            flowNodeMessage.content = flowData.flowNodeMessage.content;
            if (!flowNodeMessage.approved) {
                flowNodeMessage.content.approved = false;
            }
            // 审批通过的情况下
            // 在两个节点中需要把当前审批人在待审批人的列表中去除，只有没有当前节点的待审批人的情况才submit，否则upsert
            if (flowMetaData.currentNodeId === '70210103' || flowMetaData.currentNodeId === '70210104' || flowMetaData.currentNodeId === '70210105') {
                const acceptorList = flowData.flowNodeMessage.acceptorList;
                const acceptorNameList = flowData.flowNodeMessage.acceptorNameList;
                const index = acceptorList.indexOf(userInfo.email);
                // 删除当前审批人
                acceptorList.splice(index, 1);
                acceptorNameList.splice(index, 1);
                // 把当前审批人从里面去掉
                if (acceptorList.length !== 0 && flowNodeMessage.approved === true) {
                    // 如果不是最后一个审批人的话，那么即使通过了审批也只是更新工单，并且把自己从工单的待办人中移除
                    const upsertNodeMessage: any = { ...flowNodeMessage, acceptorList, acceptorNameList };
                    const flowId = await this.workflow.upsert(userInfo, flowMetaData, upsertNodeMessage);
                    const flowIdList: string[] = [];
                    flowIdList.push(flowId);
                    return ctx.body = AjaxResult.success(flowIdList);
                } else if (acceptorList.length !== 0 && flowNodeMessage.approved === false) {
                    flowNodeMessage.content = flowData.flowNodeMessage.content;
                    flowNodeMessage.content.waitAcceptorList = acceptorList;
                    flowNodeMessage.content.approved = false;
                }
            }
            if (flowMetaData.currentNodeId === '70210107') {
                // 上传培训记录，默认通过
                const flowData: FlowData = await this.workflowQuery.detailQuery(flowMetaData.flowId, flowMetaData.currentNodeId);
                flowNodeMessage.content = flowData.flowNodeMessage.content;
                flowNodeMessage.content.fileKey = flowNodeMessage.fileKey;
                flowNodeMessage.content.fileName = flowNodeMessage.fileName;
                flowNodeMessage.content.trainTime = flowNodeMessage.trainTime;
                flowNodeMessage.approved = true;
                flowNodeMessage.operateRemark = '上传培训记录';
            }
            const nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            console.error('工单提交失败:', e);
            ctx.body = AjaxResult.internal('工单提交失败');
        }
    }

    /**
     * 工单创建与保存草稿，不提交，不触发流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/upsert.json', [openid()()])
    async upsert(ctx: RequestContext<FlowData, UpsertResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在处理中
        flowNodeMessage.businessStatus = 0;
        const userInfo = ctx.openIDInfo;
        try {
            const flowId = await this.workflow.upsert(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(flowId);
        } catch (e) {
            console.error('工单保存失败:', e);
            ctx.body = AjaxResult.internal('工单保存失败');
        }
    }

    /**
     * 工单创建与保存草稿，不提交，不触发流转
     * {"flowMetaData":{},"flowNodeMessage":{}}
     */
    @PostMapping('/upload.json', [openid()()])
    async upload(ctx: RequestContext<FlowSubmitData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { flowMetaData, flowNodeMessage } = ctx.request.body;
        // 0表示流程在完结
        flowNodeMessage.operateRemark = '上传培训记录';
        flowNodeMessage.businessStatus = 1;
        const userInfo = ctx.openIDInfo;
        try {
            // flowMetaData.currentNodeId = flowMetaData.nodeId;
            const flowId = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(flowId);
        } catch (e) {
            console.error('工单保存失败:', e);
            ctx.body = AjaxResult.internal('工单保存失败');
        }
    }

    /**
     *
     * @description 立项人撤回工单
     *
     * @param ctx
     */
    @PostMapping('/revoke.do', [openid()()])
    async revoke(ctx: RequestContext<FlowData, SubmitResult>) {
        if (isNullOrUndefined(ctx.request.body)) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const rst = ctx.request.body;
        const userInfo = ctx.openIDInfo;
        // 供应商选择工单撤回条件为99
        rst.flowNodeMessage.content.branch = 1;
        delete rst.flowNodeMessage.content.approved;

        const flowNodeMessage = { ...rst.flowNodeMessage, operateRemark: '撤回工单', approved: false };
        // 目前工单所处的节点版本信息
        const flowMetaData = {
            flowId: rst.flowMetaData.flowId,
            version: rst.flowMetaData.version,
            currentNodeId: rst.flowMetaData.currentNodeId
        };
        let nextNodeIds;
        try {
            nextNodeIds = await this.workflow.submit(userInfo, flowMetaData, flowNodeMessage);
            ctx.body = AjaxResult.success(nextNodeIds);
        } catch (e) {
            console.error('工单撤回失败:', e);
            ctx.body = AjaxResult.internal('工单撤回失败');
        }
    }
}
