import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Ajax, SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData } from '@shark/shark-yx-workflow';
import { AjaxUrl } from '../config';

@Component({
    templateUrl: './approve.component.html',
    selector: 'onboard-approve'
})
export class OnboardApproveComponent {
    @ViewChild('form') form;
    @Input()
    workFlowData: WorkFlowData = null;

    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    approveData: any = {
        flowId: '',
        approved: true,
        operateRemark: ''
    };

    approveChoices: any = [];

    switch: boolean = true;
    pedding: boolean = false;
    constructor(
        private router: Router,
        private ajax: Ajax,
        private toastr: SharkToastrService
    ) {
        this.initDataOption();
    }

    ngOnChanges(schanges) {
        if (schanges.workFlowData && schanges.workFlowData.currentValue) {
            this.flowData = this.workFlowData.flowData || {
                flowMetaData: {},
                flowNodeMessage: {}
            };
        }
    }

    initDataOption() {
        this.approveChoices = [{
            label: '通过',
            value: true
        }, {
            label: '不通过',
            value: false
        }];
    }

    submit() {
        this.form.submit(async () => {
            this.pedding = true;
            this.approveData.flowId = this.workFlowData.flowId;
            try {
                await this.ajax.postByJson(AjaxUrl.submitFlow.onboard, {
                    flowMetaData: this.flowData.flowMetaData,
                    flowNodeMessage: {
                        ...this.approveData
                    }
                });
                this.toastr.success('工单审批成功');
                this.router.navigate(['/workflow/dealingList']);
            } catch (e) {
                this.pedding = false;
                this.toastr.error(`工单审批失败: ${e.errorCode}`);
            }
        });
    }
}
