.section {
    position: absolute;
    top: 16px;
    bottom: 0;
    left: 16px;
    right: 16px;   

.section-entry {
    display: flex;
    padding: 0 80px;

    .btn {
        &:disabled {
            color: #333 !important;
            background-color: #fff !important;
            border: 0;
        }
    }

    .u-activeBtn:hover {
        color: #027DB4;
        text-decoration:underline;
    }

    .btn-secondary {
        border: 0;
        color: #333 !important;
    }

    .m-authority {
        flex: 1;
        .m-auth-title {
            width: 70%;
            height: 70px;
            line-height: 70px;
            background-color: rgba(184, 241, 241, 0.8);
            text-align: center;
            font-size: 20px;
            color: rgb(100, 177, 151);
            margin-bottom: 20px;
            font-weight: bold;
        }
    }

    .m-organization {
        flex: 1;

        .m-org-title {
            width: 70%;
            height: 70px;
            line-height: 70px;
            background-color: rgba(199, 179, 230, 0.8);
            text-align: center;
            font-size: 20px;
            color: rgb(73, 96, 221);
            margin-bottom: 20px;
            font-weight: bold;
        }
    }

    .m-hot-img {
        width: 30px;
        height: 30px;
        vertical-align: sub;
    }
}
}