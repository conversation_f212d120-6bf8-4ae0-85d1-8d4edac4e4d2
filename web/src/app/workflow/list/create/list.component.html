<div class="m-workflow-created layout-main">
    <div class="section">
        <div class="section-header section-header-bordered">
            <span class="section-header-title">我的发起</span>
        </div>
        <div class="section-block margin-b-0x">
            <form class="form form-inline">
                <div class="form-item-group">
                    <div class="form-item col-8">
                        <label class="form-item-label col-8">工单类型：</label>
                        <div class="form-item-control col-16">
                            <shark-select [(ngModel)]="searchData.topologyIds" (ngModelChange)="changeWolkflowType($event)" [clearable]="true"
                             [placeholder]="'请选择'" [data]="workFlowTypeOptions.data" [size]="'full'" name="topologyId"></shark-select>
                        </div>
                    </div>
                    <div class="form-item col-8">
                        <label class="form-item-label col-8">工单名称：</label>
                        <div class="form-item-control col-16">
                            <shark-select [(ngModel)]="searchData.topologyId" [placeholder]="'请选择'" [clearable]="true"
                             [data]="flowTypeOptions" [size]="'full'" name="name"></shark-select>
                        </div>
                    </div>
                    <div class="form-item col-8">
                        <label class="form-item-label col-8">工单编号：</label>
                        <div class="form-item-control col-16">
                            <shark-input [(ngModel)]="searchData.flowId" [size]="'full'" [placeholder]="'请填写'"  name="flowId"></shark-input>
                        </div>
                    </div>
                    <!-- <div class="form-item col-8">
                        <label class="form-item-label col-8">创建人：</label>
                        <div class="form-item-control col-16">
                            <single-user-selector [(model)]="searchData.createUser" [size]="'full'" name="createUserEmail" ></single-user-selector>
                        </div>
                    </div> -->
                    <div class="form-item col-8">
                        <label class="form-item-label col-8">工单状态：</label>
                        <div class="form-item-control col-16">
                            <!-- TODO 这边的值明天确认 -->
                            <shark-select [(ngModel)]="searchData.status"  [clearable]="true" [data]="topologyStatusOptions" [size]="'full'" [placeholder]="'请选择'" name="status"></shark-select>
                        </div>
                    </div>
                    <div class="form-item col-16">
                        <label class="form-item-label col-4">选择时间：</label>
                        <div class="form-item-control col-20">
                            <shark-select [(ngModel)]="searchData.dateType" [data]="dateType.data" name="dateType"  [clearable]="true" [placeholder]="'请选择时间类型'"></shark-select>
                            <shark-select class="margin-l-2x" [(ngModel)]="searchData.createTime" [data]="timeFrameOptions.data" name="timeFrame" 
                            [ngStyle]="{'display': searchData.dateType === dateType.POINT ? 'inline-block' : 'none'}"></shark-select>
                            <shark-daterangepicker class="margin-l-1x" [(ngModel)]="searchData.createTime" [ngStyle]="{'display': searchData.dateType === dateType.RANGE ? 'inline-block' : 'none', 'width': '68%' }"
                                [format]="'yyyy-MM-dd HH:mm:ss'" [ngModelOptions]="{standalone: true}"></shark-daterangepicker>
                        </div>
                    </div>
                    
                    <div class="form-item col-24">
                        <div class="col-8"></div>
                        <div class="form-item-control col-16">
                            <div class="col-4"></div>
                            <div class="col-20">
                                <button class="btn btn-primary" (click)="doSearch()">搜索</button>
                                <button class="btn btn-secondary" (click)="doReset()">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="section" *ngIf="itemList.length === 0">
     
        <div class="no-data-tip">暂无数据</div>
       
    </div>
    <div *ngIf="itemList.length > 0">
        <div *ngFor="let item of itemList;let index = index;">
            <!-- 根据不同的类型渲染 内部再根据同类型不能工单展示页面 -->
            <!-- 权限类型 -->
            <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'ius_special_rights' || item.flowMetaData.topologyId === 'ius_special_rights_child' || item.flowMetaData.topologyId === 'ius_org_identity_permit_topology' || item.flowMetaData.topologyId === 'ius_org_identity_permit_child_topology' || item.flowMetaData.topologyId === 'ius_ext_system_auth_apply' || item.flowMetaData.topologyId === 'ius_ext_system_auth_clear'">
                <div style="min-width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)">
                    <div class="border-success text-success bg-success float-right padding-h-1x" style="display: inline-block;" >
                        权限
                    </div>
                </div>
                <div class="row margin-l-1x" style="flex: 1" >
                    <div class="col-6 padding-v-2x position-relative m-left-info" style="cursor: pointer;" (click)="detail(item)">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="position-absolute m-line-height" style="bottom: 15px">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span></div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                        <img *ngIf="item.flowNodeMessage.urgeState === 1 && !extraBussinessData[index]?.allowUrge && item.flowNodeMessage.nodeId !== '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/urged.png">
                        <img *ngIf="item.flowNodeMessage.nodeId === '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/overed.png">
                        <img *ngIf="item.flowNodeMessage.withdrawState === 1" class="u-urged-img" src="../../../../assets/img/cancel.png">
                    </div>
                    <div class="col-15 padding-v-2x" style="cursor: pointer;" (click)="detail(item)">
                        <div class="m-line-height">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <div class="m-line-height" *ngIf="item.flowMetaData.topologyId != 'ius_ext_system_auth_clear'">
                            <span><span class="m-label-color">员工类型：</span>{{extraBussinessData[index]?.userType || '无'}}</span>
                            <span class="margin-l-4x"><span class="m-label-color">岗位：</span>{{extraBussinessData[index]?.workPost}}</span>
                        </div>
                        <!-- item.flowNodeMessage.businessObject.multiApplySystem -->
                        <div class="m-line-height">
                            <span class="m-label-color">部门：</span>
                            <span *ngIf="item.flowMetaData.topologyId != 'ius_ext_system_auth_clear'">{{extraBussinessData[index]?.department}}</span>
                            <span *ngIf="item.flowMetaData.topologyId == 'ius_ext_system_auth_clear'">{{item.flowNodeMessage.changeUserInfo.fullDeptName}}</span>
                        </div>
                        <div class="m-line-height text-truncate text-width" shark-tooltip [template]="item | muliapplypipe" [direction]="'bottom'">
                            <span class="m-label-color">权限需求：</span>{{ item | muliapplypipe }}
                        </div>
                        <div class="m-line-height text-error" *ngIf="item.flowNodeMessage?.forOtherMobileCheckRemark">{{item.flowNodeMessage?.forOtherMobileCheckRemark}}</div>
                    </div>
                    <div class="col-3 m-operating">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <urge-btn [parent]='this' [data]='item' [sucCb]='getItemList' [disabled]='!extraBussinessData[index]?.allowUrge'></urge-btn>
                                <withdraw-btn [parent]='this' [data]='item' [sucCb]='getItemList' [isShow]='extraBussinessData[index]?.allowWithDraw && item.flowNodeMessage.withdrawState !== 1'></withdraw-btn>
                                <button
                                        *ngIf="item.flowNodeMessage?.business_status === -1 || item.flowNodeMessage?.businessStatus === -1"
                                        class="btn btn-link" type="button" (click)="goEditOrder(item)">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 组织架构类型 -->
            <div class="section m-list-item position-relative"  *ngIf="item.flowMetaData.topologyId === 'ius_entry_apply_topology' || item.flowMetaData.topologyId === 'ius_org_identity_confirm_topology' || item.flowMetaData.topologyId === 'ius_hold_post_apply_topology' || item.flowMetaData.topologyId === 'ius_quit_apply_topology' || item.flowMetaData.topologyId === 'ius_transfer_apply_topology'" >
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)">
                    <div class="border-primary text-primary bg-primary float-right padding-h-1x" style="display: inline-block;" >
                        组织架构
                    </div>
                </div>
                <div class="row margin-l-1x" style="flex: 1;">
                    <div class="col-6 padding-v-2x position-relative m-left-info" style="cursor: pointer;" (click)="detail(item)">
                        <div class="text-lg m-line-height">{{item | topoauthnamepipe}}-{{item.flowNodeMessage.createUserName}}</div>                        
                        <div class="position-absolute m-line-height" style="bottom: 15px">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span> </div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                        <img *ngIf="item.flowNodeMessage.urgeState === 1 && !extraBussinessData[index]?.allowUrge && item.flowNodeMessage.nodeId !== '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/urged.png">
                        <img *ngIf="item.flowNodeMessage.nodeId === '9999' && item.flowNodeMessage.withdrawState !== 1" class="u-urged-img" src="../../../../assets/img/overed.png">
                        <img *ngIf="item.flowNodeMessage.withdrawState === 1" class="u-urged-img" src="../../../../assets/img/cancel.png">
                    </div>
                    <div class="col-15 padding-v-2x" style="cursor: pointer;" (click)="detail(item)">
                        <div class="m-line-height">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span>
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <div class="m-line-height">
                            <span><span class="m-label-color">员工类型：</span>{{extraBussinessData[index]?.userType || '无'}}</span>

                            <span class="margin-l-4x">
                                <span class="m-label-color">{{getWorkPostLabel(item.flowMetaData.topologyId)}}：</span>
                                {{getWorkPostValue(item.flowMetaData.topologyId, index)}}
                            </span>
                        </div>
                        <ng-container>
                            <div class="m-line-height">
                                <span class="m-label-color">{{getWorkDepartLabel(item.flowMetaData.topologyId)}}：</span>
                                {{getWorkDepartValue(item.flowMetaData.topologyId, index)}}
                            </div>
                            
                            <div *ngIf="item.flowNodeMessage.businessObject.remark; else notHaveRemark" class="m-line-height text-truncate text-width" shark-tooltip [template]="item.flowNodeMessage.businessObject.remark" [direction]="'bottom'">
                                <span class="m-label-color">{{formatRemarkLabel(item.flowMetaData.topologyId)}}：</span>
                                {{item.flowNodeMessage.businessObject.remark}}
                            </div>
                            <ng-template #notHaveRemark>
                                <div class="m-line-height text-truncate text-width">
                                    <span class="m-label-color">{{formatRemarkLabel(item.flowMetaData.topologyId)}}：</span>
                                    {{item.flowNodeMessage.businessObject.remark}}
                                </div>
                            </ng-template>
                        </ng-container>
                       
                    </div>
                    <div class="col-3 m-operating">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <urge-btn [parent]='this' [data]='item' [sucCb]='getItemList' [disabled]='!extraBussinessData[index]?.allowUrge'></urge-btn>
                                <withdraw-btn [parent]='this' [data]='item' [sucCb]='getItemList' [isShow]='extraBussinessData[index]?.allowWithDraw && item.flowNodeMessage.withdrawState !== 1'></withdraw-btn>
                                <button
                                        *ngIf="item.flowNodeMessage?.business_status === -1 || item.flowNodeMessage?.businessStatus === -1"
                                        class="btn btn-link" type="button" (click)="goEditOrder(item)">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 老工单 属于组织架构类型-->
            <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'nav_personnelchange_onboard' || item.flowMetaData.topologyId === 'nav_personnelchange_departure' || item.flowMetaData.topologyId === 'nav_personnelchange_transfer' || item.flowMetaData.topologyId === 'ius_personal_special_rights' ">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)">
                    <div *ngIf="item.flowMetaData.topologyId === 'ius_personal_special_rights' else org1" class="border-success text-success bg-success float-right padding-h-1x" style="display: inline-block;" >
                        权限
                    </div>
                    <ng-template #org1>
                        <div #org1 class="border-primary text-primary bg-primary float-right padding-h-1x" style="display: inline-block;" >
                            组织架构
                        </div>
                    </ng-template>
                </div>
                <div class="row margin-l-1x" style="flex: 1;" (click)="detail(item)">
                    <div class="col-6 padding-v-2x position-relative m-left-info" style="cursor: pointer;" (click)="detail(item)">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="m-line-height">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：</span> {{item.flowMetaData.flowId}}</div>
                            <div><span class="m-label-color">更新时间：</span>{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</div>
                        </div>
                        <img *ngIf="item.flowMetaData.status === 200" class="u-urged-img" src="../../../../assets/img/overed.png">
                    </div>
                    <div class="col-15 padding-v-2x" style="cursor: pointer;" (click)="detail(item)">
                        <div class="m-line-height" *ngIf="item.flowMetaData.topologyId === 'ius_personal_special_rights' else personal1">
                            <span><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.status === 200 ? '工单完结' :item.flowNodeMessage.nodeId | nodenamepipe }}</span>
                            <span *ngIf="item.flowMetaData.status !== 200">
                                <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                            </span>
                        </div>
                        <ng-template #personal1>
                            <div class="m-line-height" #personal1>
                                <span *ngIf="item.flowNodeMessage?.businessStatus === 0"><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                                <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                                <span>
                                    <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                    <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                                </span>
                            </div>
                        </ng-template>
                        
                    </div>
                    <div class="col-3 m-operating">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                                <div style="display:inline-block;">

                                    <button
                                        *ngIf="item.flowNodeMessage?.business_status === -1 || item.flowNodeMessage?.businessStatus === -1 else other1"  
                                        class="btn btn-link" type="button" (click)="goEditOrder(item)">
                                        编辑
                                    </button>
                                </div>
                               
                            </div>
                        </div>
                </div>
            </div>
            <!-- 文档类型 -->
            <div class="section m-list-item" *ngIf="item.flowMetaData.topologyId === 'written_doc_delete_project' || item.flowMetaData.topologyId === 'written_doc_project' ">
                <div style="width: 100px; cursor: pointer;" class="padding-v-2x" (click)="detail(item)">
                    <div class="border-error text-error bg-error float-right padding-h-1x" style="display: inline-block;" >
                        文档
                    </div>
                </div>
                <div class="row margin-l-1x" style="flex: 1;" >
                    <div class="col-6 padding-v-2x position-relative m-left-info" (click)="detail(item)" style="cursor: pointer;">
                        <div class="text-lg m-line-height">{{item.flowMetaData.topologyName | toponamepipe}}-{{item.flowNodeMessage.createUserName}}</div>
                        <div class="m-line-height">
                            <div (click)="$event.stopPropagation();" style="cursor: auto;"><span class="m-label-color">工单编号：{{item.flowMetaData.flowId}}</span> </div>
                            <div><span class="m-label-color">更新时间：{{item.flowMetaData.updateTime | dateFormat: 'yyyy-MM-dd HH:mm'}}</span></div>
                        </div>
                        <img *ngIf="item.flowMetaData.status === 200" class="u-urged-img" src="../../../../assets/img/overed.png">
                    </div>
                    <div class="col-15 padding-v-2x"(click)="detail(item)" style="cursor: pointer;">
                        <div class="m-line-height" *ngIf="item.flowMetaData.status === 200 else end">
                            <span class="m-label-color">当前节点/审批人：</span>工单完结
                        </div>
                        <ng-template #end>
                            <div class="m-line-height">
                                <span *ngIf="item.flowNodeMessage?.businessStatus === 0"><span class="m-label-color">当前节点/审批人：</span>{{item.flowMetaData.currentNodeId | nodenamepipe }}</span>
                                <span *ngIf="item.flowNodeMessage?.businessStatus !== 0">/</span>
                                <span>
                                    <popo *ngFor="let uid of item.flowNodeMessage?.acceptorList;" [email]="uid"></popo>
                                    <span *ngIf="item.flowNodeMessage?.acceptorList?.length === 0">/</span>
                                </span>
                            </div>
                        </ng-template>
                    </div>
                    <div class="col-3 m-operating">
                        <div style="width: 100%;">
                            <div class="text-center">操作</div>
                            <div class="margin-t-2x">
                                <button class="btn btn-link" type="button" (click)="detail(item)">查看详情</button>
                                <!-- 如果工单类型为成文文档下发 显示撤回或编辑 或处理-->
                                <div *ngIf="item.flowMetaData.topologyId === 'written_doc_project'" style="display:inline-block;">
                                    <button *ngIf="item?.flowMetaData?.currentNodeId !== '70210101' &&
                                        item?.flowMetaData?.currentNodeId !== '70210107' && item?.flowMetaData?.currentNodeId !== '9999' && email ===
                                        item?.flowNodeMessage?.createUser" class="btn btn-link"
                                        type="button" (click)="revoke(item)">撤回</button>
                                    <button
                                        *ngIf="item.flowMetaData.currentNodeId === '70210101' &&
                                        item.flowNodeMessage?.content?.approved === undefined"
                                        class="btn btn-link" type="button" (click)="toEdit(item)">编辑</button>
                                        <!-- 如果当前节点为拟写成文文件、审批不通过、登录人是创建人 则显示处理按钮 -->
                                    <button class="btn btn-link" (click)="toEdit(item)"
                                        *ngIf="item.flowNodeMessage?.content?.approved === false
                                    && item.flowMetaData.currentNodeId === '70210101' &&
                                        email === item.flowNodeMessage?.createUser">处理</button>
                                    <button *ngIf="item?.flowMetaData?.currentNodeId !== '70210101' &&
                                        item?.flowMetaData?.currentNodeId !== '70210107' && item?.flowMetaData?.currentNodeId !== '9999' && email ===
                                        item?.flowNodeMessage?.createUser" class="btn btn-link"
                                        type="button" (click)="onUrge(item.flowMetaData.flowId)">催办</button>
                                    <div *ngIf="item?.flowMetaData?.currentNodeId !== '70210101' &&
                                        item?.flowMetaData?.currentNodeId !== '70210107' && item?.flowMetaData?.currentNodeId !== '9999' && email ===
                                        item?.flowNodeMessage?.createUser" class="tootip" shark-tooltip [direction]="'top'" [template]="detailMessage">
                                        <i _ngcontent-c0="" class="iconRecord icon-questioncircleo margin-l-2x"></i>
                                    </div>
                                </div>
                                <!-- 如果工单类型为文档作废工单 显示撤回或处理-->
                                <div *ngIf="item.flowMetaData.topologyId === 'written_doc_delete_project'"
                                    style="display:inline-block;">
                                    <button
                                        *ngIf="item?.flowMetaData?.currentNodeId !== '70210301' &&
                                        item?.flowMetaData?.currentNodeId !== '70210303' && item?.flowMetaData?.currentNodeId !== '9999' && email ===
                                        flowData?.flowNodeMessage?.createUser "
                                        class="btn btn-link" type="button" (click)="revokeDel(item)">撤回</button>
                                    <button *ngIf="item.flowMetaData.currentNodeId === '70210301' &&
                                        item.flowNodeMessage?.content?.approved === false" class="btn btn-link"
                                        type="button" (click)="toDeal(item)">处理</button>
                                    <button *ngIf="item?.flowMetaData?.currentNodeId !== '70210301' &&
                                        item?.flowMetaData?.currentNodeId !== '70210303' && item?.flowMetaData?.currentNodeId !== '9999' && email ===
                                        flowData?.flowNodeMessage?.createUser" class="btn btn-link"
                                        type="button" (click)="onUrge(item.flowMetaData.flowId)">催办</button>
                                    <div *ngIf="item?.flowMetaData?.currentNodeId !== '70210301' &&
                                        item?.flowMetaData?.currentNodeId !== '70210303' && item?.flowMetaData?.currentNodeId !== '9999' && email ===
                                        flowData?.flowNodeMessage?.createUser" class="tootip" shark-tooltip [direction]="'top'" [template]="detailMessage">
                                        <i _ngcontent-c0="" class="iconRecord icon-questioncircleo margin-l-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="section-block text-right" *ngIf="pagination.total > 0">
        <shark-pagination [page]="pagination.page" [totalPage]="pagination.totalPage" [pageSize]="pagination.size"
            (onSizeChanged)="onSizeChanged($event);" (onPageChanged)="onPageChanged($event);"></shark-pagination>
    </div>
</div>
