import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SharkToastrService } from '@shark/shark-angularX';
import { WorkFlowData, YXWorkFlowDetailBase } from '@shark/shark-yx-workflow';
import { UserService } from '../shared/services/user.service';

@Component({
    templateUrl: './docProject.component.html'
})
export class DocProjectComponent implements YXWorkFlowDetailBase {
    trainData:any;
    workFlowData: WorkFlowData = null;
    flowData: any = {
        flowMetaData: {},
        flowNodeMessage: {}
    };
    // isEdit: boolean = false; //0代表不是编辑页，1代表编辑页
    isEdit: number = 0; // 是否从列表页的编辑按钮跳进来的， 1：是， 0：不是
    isDetail:boolean = false; //false:不是从文档管理列表跳转进来，true：是 
    userInfo: any = {};

    trainStatus:number; //是否需要培训 1：培训，0：不培训
    // 有上传培训的权限
    hasAuthority : boolean = false;
    // 展示培训信息
    currentNodeId:string;
    businessStatus:number;
    //在详情页展示培训信息
    showTrainList:boolean = false;
    //获取当前登录人邮箱
    cookies:{
        YX_CSRF_TOKEN?: string;
        yx_name?: string;
        yx_username?: string;
        has_user?:string;
    } = {};


    constructor(
        private sharkToastrService: SharkToastrService,
        private router: Router,
        private userServ: UserService,
        private aRoute: ActivatedRoute
    ) {
        // this.userServ.getUserInfo().then((userInfo) => {
        //     this.userInfo = userInfo.data;// zhanshiyongxianyoude 
        // });
        this.userServ.getUser().then((userInfo) => {
            this.userInfo = userInfo;
            // console.log(this.userInfo);
        });// 暂时用登陆时的获取用户的接口
        this.aRoute.params.subscribe((params) => {
            this.isEdit = params.isEdit !== undefined ? Number(params.isEdit) : 0;
            this.isDetail = params.isDetail !== undefined ? params.isDetail : false;
            this.trainStatus = Number(params.trainStatus);
            this.businessStatus = Number(params.businessStatus);
            this.currentNodeId = params.currentNodeId;
        });

        //获取当前登录人邮箱
        document.cookie.split('; ').forEach(i => {
            const kv = i.split('=');
            this.cookies[kv[0]] = decodeURIComponent(kv[1]);
        });
    }
    ngOnInit() {
        document.title = '严选业务门户-成文文档下发工单';
        const createUser = this.workFlowData.flowData.flowNodeMessage['content'].creatorUId;//文档创建人邮箱
        console.log(createUser);
        // console.log(this.cookies.yx_username);
        if (createUser !== undefined && this.cookies.yx_username === createUser){
            //若当前登录人是 文档创建人，则有权限进行培训文档上传
            this.hasAuthority = true;
        }

        // 是否展示培训信息 
        if (this.isDetail) {//是从管理列表跳转过来的 
            if (this.flowData.flowMetaData.currentNodeId === '70210107') { //工单完结且还未上传培训信息
                this.showTrainList = this.hasAuthority ? true : false; // 有权限上传，则展示，否则不展示
            } else if (this.flowData.flowMetaData.currentNodeId === '9999') {//已经上传培训信息（this.businessStatus === 1）
                this.showTrainList = true;  //展示
            }
        } else if (this.flowData.flowMetaData.currentNodeId === '9999') {//其他页面跳转过来：展示培训信息的条件————只有当已上传了附件（businessStatus === 1）才展示
            // this.showTrainList = (this.currentNodeId === '70210107');
            this.showTrainList = true;

        }

    }
    ngOnDestroy() {
        document.title = '严选业务门户';
    }
    onFlowDataChange(workFlowData: WorkFlowData) {
        this.workFlowData = workFlowData;
        this.flowData = this.workFlowData.flowData || {
            flowMetaData: {},
            flowNodeMessage: {}
        };
    }
    // 是否是编辑
    showEdit() {
        // const uid = this.userInfo.uid;
        // const uid='<EMAIL>';// 获取用户信息接口有问题，先暂时写死uid，走流程
        const uid=this.userInfo.email;
        // if (this.workFlowData.nodeId === '70210101') {
        //     return true;
        // }
        if (this.isEdit === 1) {
            if (this.flowData.flowNodeMessage.businessStatus === -1 && uid === this.flowData.flowNodeMessage.createUser) {
                return true;
            }
        }
        return false;
    }
    // 是否是待审核
    showApprove() {
        // 只要当前节点不是工单完结 或 结束 都要有审批列表
        // const uid = this.userInfo.email;
        // console.log(this.userInfo.email);
        if (((this.flowData.flowNodeMessage.acceptorList || []).includes(this.userInfo.email)) && this.flowData.flowMetaData.currentNodeId !== '70210107' && this.flowData.flowMetaData.currentNodeId !== '9999') {
            return true;
        } else {
            return false;
        }

        // const uid = this.userInfo.uid;
        // const uid='<EMAIL>';

        // const uid=this.userInfo.email;
        // if ((this.flowData.flowNodeMessage.acceptorList || []).includes(uid)) {
        //     return true;
        // } else {
        //     return false;
        // }

        
    }

    // // 点击处理按钮 跳转至拟写成文文件页面
    // toEdit() {
    //     if (window.location.hostname === 'bflow.test.yx.mail.netease.com') {
    //         window.open('http://document.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
    //     } else if (window.location.hostname === 'dev.test.yx.mail.netease.com') {
    //         window.open('http://dev.test.yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
    //     } else if (window.location.hostname === 'yx.mail.netease.com') {
    //         window.open('http://yx.mail.netease.com/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
    //     } else {
    //         window.open('http://remote.yx.mail.netease.com:9000/written-doc/index.html#/editdocs/' + this.flowData.flowNodeMessage.content.fileId);
    //     }
    // }
}
